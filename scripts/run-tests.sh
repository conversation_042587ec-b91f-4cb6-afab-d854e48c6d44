#!/bin/bash

# YoTracker Test Runner Script
# This script sets up the test environment and runs tests with proper database configuration

set -e

echo "🧪 YoTracker Test Runner"
echo "========================"

# Default values
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-3306}
DB_USERNAME=${DB_USERNAME:-admin}
DB_PASSWORD=${DB_PASSWORD:-password}
TESTING_DB_NAME=${TESTING_DB_NAME:-testing}
APP_KEY=${APP_KEY:-test-secret-key}

# Check if MySQL is running
echo "🔍 Checking MySQL connection..."
if ! nc -z $DB_HOST $DB_PORT; then
    echo "❌ MySQL is not running on $DB_HOST:$DB_PORT"
    echo "💡 Start MySQL with: docker-compose up mysql -d"
    exit 1
fi

echo "✅ MySQL is running on $DB_HOST:$DB_PORT"

# Set environment variables for tests
export DB_HOST=$DB_HOST
export DB_PORT=$DB_PORT
export DB_USERNAME=$DB_USERNAME
export DB_PASSWORD=$DB_PASSWORD
export TESTING_DB_NAME=$TESTING_DB_NAME
export APP_KEY=$APP_KEY

echo "🗄️  Using test database: $TESTING_DB_NAME"

# Run tests
echo "🚀 Running tests..."

if [ "$1" = "verbose" ] || [ "$1" = "-v" ]; then
    echo "📝 Running tests in verbose mode..."
    go test ./... -v
elif [ "$1" = "specific" ] && [ -n "$2" ]; then
    echo "🎯 Running specific test: $2"
    go test ./... -v -run "$2"
elif [ "$1" = "package" ] && [ -n "$2" ]; then
    echo "📦 Running tests for package: $2"
    go test "./$2" -v
else
    echo "🏃 Running all tests..."
    go test ./...
fi

echo "✅ Tests completed!"
