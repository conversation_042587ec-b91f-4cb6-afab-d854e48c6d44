package middleware

import (
	"github.com/gin-gonic/gin"
	"yotracker/internal/models"
)

func CheckForClient() gin.HandlerFunc {
	return func(c *gin.Context) {
		user, _ := c.Get("user")
		if user.(models.User).ClientId == nil {
			c.AbortWithStatusJSON(401, gin.H{"error": "Unauthorized"})
			return
		}
		if user.(models.User).Client.Id == 0 {
			c.AbortWithStatusJSON(401, gin.H{"error": "Unauthorized"})
			return
		}
		c.Next()
	}
}
