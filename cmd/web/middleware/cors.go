package middleware

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

func CorsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Avoid overwriting headers if they are already set
		if c.<PERSON>.Header().Get("Access-Control-Allow-Origin") == "" {
			origin := c.Request.Header.Get("Origin")
			fmt.Println(origin)
			// Only set CORS headers if the request has an Origin header
			if origin != "" {
				c.Writer.Header().Set("Access-Control-Allow-Origin", origin) // Set only once
				c.<PERSON>.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
				c.<PERSON>.Header().Set("Access-Control-Allow-Headers", "Origin, Content-Type, Authorization, X-Requested-With, Accept")
				c.<PERSON>.Header().Set("Access-Control-Allow-Credentials", "true")
			}
		}

		// Handle preflight OPTIONS request
		if c.Request.Method == http.MethodOptions {
			c.AbortWithStatus(http.StatusOK)
			return
		}

		c.Next()
	}
}
