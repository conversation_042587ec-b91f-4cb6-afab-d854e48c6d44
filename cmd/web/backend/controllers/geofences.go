package controllers

import (
	"net/http"
	"strconv"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
)

func GetAllGeofences(c *gin.Context) {
	var geofences []models.Geofence
	var total int64
	filter := map[string]interface{}{}

	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}

	config.DB.Scopes(utils.Paginate(c)).Preload("ClientDevice").Preload("Client").Where(filter).Order("id desc").Find(&geofences)
	config.DB.Model(&models.Geofence{}).Where(filter).Count(&total)

	// Extract current_page and per_page from query params (same logic as utils.Paginate)
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}
	perPage, _ := strconv.Atoi(c.Query("per_page"))
	switch {
	case perPage > 100:
		perPage = 100
	case perPage <= 0:
		perPage = 10
	}
	c.JSON(http.StatusOK, gin.H{
		"data":         geofences,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
	})
}

func SearchGeofences(c *gin.Context) {
	var geofences []models.Geofence
	filter := map[string]interface{}{}

	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}
	if name := c.Query("name"); name != "" {
		config.DB.Where(filter).Where("name LIKE ?", "%"+name+"%").Preload("ClientDevice").Preload("Client").Order("id desc").Find(&geofences)
	} else {
		config.DB.Where(filter).Preload("ClientDevice").Preload("Client").Order("id desc").Find(&geofences)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": geofences,
	})
}

func GetGeofenceById(c *gin.Context) {
	var geofence models.Geofence
	if err := config.DB.Preload("ClientDevice").Preload("Client").First(&geofence, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Geofence not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": geofence,
	})
}

func CreateGeofence(c *gin.Context) {
	var req models.CreateGeofenceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Validate geofence based on type
	if err := service.ValidateGeofenceByType(req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Validate and determine client_id based on scope
	var clientId uint
	appliesTo := req.AppliesTo
	if appliesTo == "" {
		appliesTo = "client"
	}

	switch appliesTo {
	case "client":
		// For client-level geofences, get client_id from query parameter
		clientIdStr := c.Query("client_id")
		if clientIdStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "client_id query parameter is required for client-level geofences",
			})
			return
		}
		clientIdParsed, err := strconv.ParseUint(clientIdStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Invalid client_id",
			})
			return
		}
		clientId = uint(clientIdParsed)
	case "fleet":
		if req.FleetId == nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "fleet_id is required when applies_to is 'fleet'",
			})
			return
		}
		var fleet models.Fleet
		if err := config.DB.First(&fleet, *req.FleetId).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Fleet not found",
			})
			return
		}
		clientId = fleet.ClientId
	case "device":
		if req.ClientDeviceId == nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "client_device_id is required when applies_to is 'device'",
			})
			return
		}
		var clientDevice models.ClientDevice
		if err := config.DB.First(&clientDevice, *req.ClientDeviceId).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Client device not found",
			})
			return
		}
		clientId = clientDevice.ClientId
		req.DeviceId = clientDevice.DeviceId // Set for backward compatibility
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "applies_to must be 'client', 'fleet', or 'device'",
		})
		return
	}

	status := req.Status
	if status == "" {
		status = "active"
	}

	geofenceType := req.GeofenceType
	if geofenceType == "" {
		geofenceType = "circle"
	}

	triggerEvents := req.TriggerEvents
	if triggerEvents == "" {
		triggerEvents = "both"
	}

	geofence := models.Geofence{
		ClientId:       clientId,
		Name:           req.Name,
		GeofenceType:   geofenceType,
		AppliesTo:      appliesTo,
		FleetId:        req.FleetId,
		ClientDeviceId: req.ClientDeviceId,
		DeviceId:       req.DeviceId,
		TriggerEvents:  triggerEvents,
		Radius:         req.Radius,
		Latitude:       req.Latitude,
		Longitude:      req.Longitude,
		Coordinates:    req.Coordinates,
		Status:         status,
	}

	result := config.DB.Create(&geofence)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Geofence created successfully",
		"data":    geofence,
	})
}

func UpdateGeofence(c *gin.Context) {
	var req models.UpdateGeofenceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	var geofence models.Geofence
	if err := config.DB.First(&geofence, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Geofence not found",
		})
		return
	}

	// Validate update based on geofence type
	if err := service.ValidateGeofenceUpdateByType(geofence, req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Update fields
	if req.Name != "" {
		geofence.Name = req.Name
	}
	if req.GeofenceType != "" {
		geofence.GeofenceType = req.GeofenceType
	}
	if req.Status != "" {
		geofence.Status = req.Status
	}

	// Update scope fields
	if req.AppliesTo != "" {
		geofence.AppliesTo = req.AppliesTo
	}
	if req.FleetId != nil {
		geofence.FleetId = req.FleetId
	}
	if req.ClientDeviceId != nil {
		geofence.ClientDeviceId = req.ClientDeviceId
	}
	if req.DeviceId != "" {
		geofence.DeviceId = req.DeviceId
	}
	if req.TriggerEvents != "" {
		geofence.TriggerEvents = req.TriggerEvents
	}

	// Update type-specific fields
	if req.Latitude != nil {
		geofence.Latitude = req.Latitude
	}
	if req.Longitude != nil {
		geofence.Longitude = req.Longitude
	}
	if req.Radius != nil {
		geofence.Radius = req.Radius
	}
	if req.Coordinates != nil {
		geofence.Coordinates = req.Coordinates
	}

	result := config.DB.Save(&geofence)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Geofence updated successfully",
		"data":    geofence,
	})
}

func DeleteGeofence(c *gin.Context) {
	var geofence models.Geofence
	if err := config.DB.First(&geofence, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Geofence not found",
		})
		return
	}

	// Clear cache for affected devices
	if geofence.ClientDeviceId != nil {
		service.ClearGeofenceStateCache(*geofence.ClientDeviceId)
	}

	result := config.DB.Delete(&geofence)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Geofence deleted successfully",
	})
}

func GetGeofenceEvents(c *gin.Context) {
	var events []models.GeofenceEvent
	var total int64
	filter := map[string]interface{}{}

	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if geofenceId := c.Query("geofence_id"); geofenceId != "" {
		filter["geofence_id"] = geofenceId
	}
	if eventType := c.Query("event_type"); eventType != "" {
		filter["event_type"] = eventType
	}

	query := config.DB.Scopes(utils.Paginate(c)).Preload("Geofence").Preload("ClientDevice").Where(filter)

	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			query = query.Where("event_timestamp BETWEEN ? AND ?", startDate, endDate)
		}
	}

	query.Order("id desc").Find(&events)
	config.DB.Model(&models.GeofenceEvent{}).Where(filter).Count(&total)

	c.JSON(http.StatusOK, gin.H{
		"data":         events,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}

func GetGeofencesByDevice(c *gin.Context) {
	clientDeviceIdStr := c.Param("device_id")
	clientDeviceId, err := strconv.ParseUint(clientDeviceIdStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid device ID",
		})
		return
	}

	geofences, err := service.GetGeofencesByDevice(uint(clientDeviceId))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": geofences,
	})
}
