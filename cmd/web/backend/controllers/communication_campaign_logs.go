package controllers

import (
	"net/http"
	"strconv"
	"time"

	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// CommunicationCampaignLogController handles communication campaign log operations
type CommunicationCampaignLogController struct{}

// NewCommunicationCampaignLogController creates a new controller instance
func NewCommunicationCampaignLogController() *CommunicationCampaignLogController {
	return &CommunicationCampaignLogController{}
}

// GetCommunicationCampaignLogs retrieves communication campaign logs with filtering and pagination
func (cc *CommunicationCampaignLogController) GetCommunicationCampaignLogs(c *gin.Context) {
	var filter models.CommunicationCampaignLogFilterRequest
	if err := c.ShouldBindQuery(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set default pagination
	page := 1
	limit := 50
	if filter.Page != nil {
		page = *filter.Page
	}
	if filter.Limit != nil {
		limit = *filter.Limit
	}
	offset := (page - 1) * limit

	// Build query
	query := config.DB.Model(&models.CommunicationCampaignLog{}).Preload("Client")

	// Apply filters
	if filter.ClientId != nil {
		query = query.Where("client_id = ?", *filter.ClientId)
	}
	if filter.CampaignType != nil {
		query = query.Where("campaign_type = ?", *filter.CampaignType)
	}
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}
	if filter.StartDate != nil {
		startDate, err := time.Parse("2006-01-02", *filter.StartDate)
		if err == nil {
			query = query.Where("created_at >= ?", startDate)
		}
	}
	if filter.EndDate != nil {
		endDate, err := time.Parse("2006-01-02", *filter.EndDate)
		if err == nil {
			// Add one day to include the end date
			endDate = endDate.Add(24 * time.Hour)
			query = query.Where("created_at < ?", endDate)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get paginated results
	var logs []models.CommunicationCampaignLog
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&logs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve logs"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": logs,
		"pagination": gin.H{
			"page":   page,
			"limit":  limit,
			"total":  total,
			"pages":  (total + int64(limit) - 1) / int64(limit),
			"offset": offset,
		},
	})
}

// GetCommunicationCampaignLog retrieves a specific communication campaign log
func (cc *CommunicationCampaignLogController) GetCommunicationCampaignLog(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var log models.CommunicationCampaignLog
	if err := config.DB.Preload("Client").First(&log, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Log not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve log"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": log})
}

// CreateCommunicationCampaignLog creates a new communication campaign log
func (cc *CommunicationCampaignLogController) CreateCommunicationCampaignLog(c *gin.Context) {
	var request models.CommunicationCampaignLogRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set default status if not provided
	if request.Status == "" {
		request.Status = models.StatusSent
	}

	log := models.CommunicationCampaignLog{
		ClientId:     request.ClientId,
		To:           request.To,
		CampaignType: request.CampaignType,
		Message:      request.Message,
		Status:       request.Status,
		ErrorMessage: request.ErrorMessage,
		Metadata:     request.Metadata,
	}

	if err := config.DB.Create(&log).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create log"})
		return
	}

	// Reload with client data
	config.DB.Preload("Client").First(&log, log.Id)

	c.JSON(http.StatusCreated, gin.H{"data": log})
}

// UpdateCommunicationCampaignLog updates a communication campaign log
func (cc *CommunicationCampaignLogController) UpdateCommunicationCampaignLog(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var request models.CommunicationCampaignLogRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var log models.CommunicationCampaignLog
	if err := config.DB.First(&log, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Log not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve log"})
		return
	}

	// Update fields
	if request.ClientId != nil {
		log.ClientId = request.ClientId
	}
	if request.To != "" {
		log.To = request.To
	}
	if request.CampaignType != "" {
		log.CampaignType = request.CampaignType
	}
	if request.Message != "" {
		log.Message = request.Message
	}
	if request.Status != "" {
		log.Status = request.Status
	}
	if request.ErrorMessage != nil {
		log.ErrorMessage = request.ErrorMessage
	}
	if request.Metadata != nil {
		log.Metadata = request.Metadata
	}

	if err := config.DB.Save(&log).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update log"})
		return
	}

	// Reload with client data
	config.DB.Preload("Client").First(&log, log.Id)

	c.JSON(http.StatusOK, gin.H{"data": log})
}

// DeleteCommunicationCampaignLog deletes a communication campaign log
func (cc *CommunicationCampaignLogController) DeleteCommunicationCampaignLog(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var log models.CommunicationCampaignLog
	if err := config.DB.First(&log, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Log not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve log"})
		return
	}

	if err := config.DB.Delete(&log).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete log"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Log deleted successfully"})
}

// GetCommunicationCampaignLogStatistics retrieves statistics for communication campaign logs
func (cc *CommunicationCampaignLogController) GetCommunicationCampaignLogStatistics(c *gin.Context) {
	var filter models.CommunicationCampaignLogFilterRequest
	if err := c.ShouldBindQuery(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Build base query
	query := config.DB.Model(&models.CommunicationCampaignLog{})

	// Apply filters
	if filter.ClientId != nil {
		query = query.Where("client_id = ?", *filter.ClientId)
	}
	if filter.CampaignType != nil {
		query = query.Where("campaign_type = ?", *filter.CampaignType)
	}
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}
	if filter.StartDate != nil {
		startDate, err := time.Parse("2006-01-02", *filter.StartDate)
		if err == nil {
			query = query.Where("created_at >= ?", startDate)
		}
	}
	if filter.EndDate != nil {
		endDate, err := time.Parse("2006-01-02", *filter.EndDate)
		if err == nil {
			endDate = endDate.Add(24 * time.Hour)
			query = query.Where("created_at < ?", endDate)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get counts by campaign type
	var smsCount, whatsappCount, emailCount int64
	query.Where("campaign_type = ?", models.CampaignTypeSMS).Count(&smsCount)
	query.Where("campaign_type = ?", models.CampaignTypeWhatsApp).Count(&whatsappCount)
	query.Where("campaign_type = ?", models.CampaignTypeEmail).Count(&emailCount)

	// Get counts by status
	var successfulCount, failedCount int64
	query.Where("status = ?", models.StatusSent).Count(&successfulCount)
	query.Where("status = ?", models.StatusFailed).Count(&failedCount)

	statistics := models.CommunicationCampaignLogStatistics{
		TotalMessages:   total,
		SmsCount:        smsCount,
		WhatsappCount:   whatsappCount,
		EmailCount:      emailCount,
		SuccessfulCount: successfulCount,
		FailedCount:     failedCount,
	}

	c.JSON(http.StatusOK, gin.H{"data": statistics})
}

// GetCommunicationCampaignLogsByClient retrieves logs for a specific client
func (cc *CommunicationCampaignLogController) GetCommunicationCampaignLogsByClient(c *gin.Context) {
	clientId, err := strconv.ParseUint(c.Param("clientId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	// Set default pagination
	page := 1
	limit := 50
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil {
			page = p
		}
	}
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil {
			limit = l
		}
	}
	offset := (page - 1) * limit

	// Get total count for this client
	var total int64
	config.DB.Model(&models.CommunicationCampaignLog{}).Where("client_id = ?", clientId).Count(&total)

	// Get paginated results for this client
	var logs []models.CommunicationCampaignLog
	if err := config.DB.Where("client_id = ?", clientId).
		Preload("Client").
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&logs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve logs"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": logs,
		"pagination": gin.H{
			"page":   page,
			"limit":  limit,
			"total":  total,
			"pages":  (total + int64(limit) - 1) / int64(limit),
			"offset": offset,
		},
	})
}
