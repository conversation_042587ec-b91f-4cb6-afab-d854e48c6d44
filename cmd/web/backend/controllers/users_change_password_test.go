package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/seed"
	"yotracker/internal/service"
	"yotracker/internal/utils"
	"yotracker/migrations"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupChangePasswordBackendTest() {
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}
	if os.Getenv("APP_KEY") == "" {
		os.Setenv("APP_KEY", "test-secret-key")
	}

	config.InitTestDB()
	migrations.Migrate()
	seed.Seed()
	gin.SetMode(gin.TestMode)
}

func TestChangePasswordBackend(t *testing.T) {
	// Setup test database
	setupChangePasswordBackendTest()

	// Clean up any existing test users
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	// Create test user
	hashedPassword := service.HashPassword("oldpassword")
	user := models.User{
		Name:     "Test Backend User",
		Email:    "<EMAIL>",
		Password: hashedPassword,
		UserType: "backend",
	}
	result := config.DB.Create(&user)
	if result.Error != nil {
		t.Fatalf("Failed to create test user: %v", result.Error)
	}

	// Generate token for authentication
	token, _ := service.GenerateToken(&user, "access")

	t.Run("successful password change", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]string{
			"current_password":      "oldpassword",
			"password":              "newpassword123",
			"password_confirmation": "newpassword123",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/change_password", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		// Mock middleware to set user in context
		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/change_password", ChangePassword)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Password changed successfully", response["message"])

		// Verify password was actually changed
		var updatedUser models.User
		err := config.DB.First(&updatedUser, user.Id).Error
		if err != nil {
			t.Fatalf("Failed to find updated user: %v", err)
		}
		assert.True(t, service.CheckPassword(updatedUser.Password, "newpassword123"))
	})

	t.Run("incorrect current password", func(t *testing.T) {
		reqBody := map[string]string{
			"current_password":      "wrongpassword",
			"password":              "newpassword123",
			"password_confirmation": "newpassword123",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/change_password", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router := gin.New()
		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/change_password", ChangePassword)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Current password is incorrect", response["message"])
	})

	t.Run("password confirmation mismatch", func(t *testing.T) {
		reqBody := map[string]string{
			"current_password":      "oldpassword",
			"password":              "newpassword123",
			"password_confirmation": "differentpassword",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/change_password", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router := gin.New()
		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/change_password", ChangePassword)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Password and password confirmation do not match", response["message"])
	})

	t.Run("unauthorized access", func(t *testing.T) {
		reqBody := map[string]string{
			"current_password":      "oldpassword",
			"password":              "newpassword123",
			"password_confirmation": "newpassword123",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/change_password", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		router := gin.New()
		// No middleware to set user in context
		router.PUT("/change_password", ChangePassword)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Unauthorized", response["message"])
	})

	// Cleanup
	config.DB.Delete(&user)
}
