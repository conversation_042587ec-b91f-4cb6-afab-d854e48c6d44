package controllers

import (
	"bytes"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/mail"
	"yotracker/internal/models"
	"yotracker/internal/templates"
	"yotracker/internal/utils"

	"github.com/SebastiaanKlippert/go-wkhtmltopdf"
	"github.com/gin-gonic/gin"
)

func GetAllInvoices(c *gin.Context) {
	var invoices []models.Invoice
	var total int64
	filter := map[string]interface{}{}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}
	if paymentTypeId := c.Query("payment_type_id"); paymentTypeId != "" {
		filter["payment_type_id"] = paymentTypeId
	}
	if currencyId := c.Query("currency_id"); currencyId != "" {
		filter["currency_id"] = currencyId
	}

	config.DB.Scopes(utils.Paginate(c)).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Preload("Currency").Where(filter).Order("id desc").Find(&invoices)
	config.DB.Model(&models.Invoice{}).Where(filter).Count(&total)
	// Extract current_page and per_page from query params (same logic as utils.Paginate)
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}
	perPage, _ := strconv.Atoi(c.Query("per_page"))
	switch {
	case perPage > 100:
		perPage = 100
	case perPage <= 0:
		perPage = 10
	}
	c.JSON(http.StatusOK, gin.H{
		"data":         invoices,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
	})
}

func GetInvoiceById(c *gin.Context) {
	var invoice models.Invoice
	if err := config.DB.Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Preload("InvoicePayments.PaymentType").Preload("InvoicePayments.Currency").Preload("Currency").Preload("PaymentType").First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": invoice,
	})
}

func CreateInvoice(c *gin.Context) {
	// Define a custom request struct to handle invoice items
	type InvoiceWithItemsRequest struct {
		models.InvoiceRequest
		InvoiceItems []models.InvoiceItemRequest `json:"items"`
	}

	var req InvoiceWithItemsRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	userIDValue, _ := c.Get("userID")
	userID := userIDValue.(uint)
	invoice := models.Invoice{
		ClientId:              req.ClientId,
		CreatedById:           &userID,
		CouponId:              req.CouponId,
		TaxRateId:             req.TaxRateId,
		PaymentTypeId:         req.PaymentTypeId,
		CurrencyId:            req.CurrencyId,
		Date:                  req.Date,
		DueDate:               req.DueDate,
		Amount:                req.Amount,
		Subtotal:              req.Subtotal,
		BaseCurrencyAmount:    req.BaseCurrencyAmount,
		BaseCurrencySubtotal:  req.BaseCurrencySubtotal,
		BaseCurrencyTaxAmount: req.BaseCurrencyTaxAmount,
		Discount:              req.Discount,
		DiscountType:          req.DiscountType,
		Status:                req.Status,
		Balance:               req.Amount,
		BaseCurrencyBalance:   req.BaseCurrencyAmount,
		Xrate:                 req.Xrate,
		DiscountAmount:        req.DiscountAmount,
		CouponDiscountAmount:  req.CouponDiscountAmount,
		TaxAmount:             req.TaxAmount,
		AdminNotes:            req.AdminNotes,
		Terms:                 req.Terms,
		Recurring:             req.Recurring,
		RecurFrequency:        req.RecurFrequency,
		RecurStartDate:        req.RecurStartDate,
		RecurEndDate:          req.RecurEndDate,
		RecurNextDate:         req.RecurNextDate,
		Description:           req.Description,
		IsSubscription:        req.IsSubscription,
	}
	//if is_subscription, set next_billing_date on client
	if req.IsSubscription != nil && *req.IsSubscription {
		var client models.Client
		if err := config.DB.First(&client, req.ClientId).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"message": "Client not found",
			})
			return
		}

		// Determine next billing date
		var nextBillingDate time.Time
		if req.NextBillingDate != nil {
			// Use the provided next billing date
			nextBillingDate = *req.NextBillingDate
		} else {
			// Calculate next billing date from client's billing cycle
			if client.BillingCycle != nil {
				billingCycle := *client.BillingCycle
				billingDay := uint(5) // default billing day
				if client.BillingDay != nil {
					billingDay = *client.BillingDay
				}
				nextBillingDate = calculateNextBillingDate(*invoice.Date, billingCycle, billingDay)
			} else {
				// Default to monthly billing if no cycle is set
				nextBillingDate = calculateNextBillingDate(*invoice.Date, "monthly", 5)
			}
		}

		// Update client's next billing date
		client.NextBillingDate = &nextBillingDate
		if err := config.DB.Save(&client).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"message": "Failed to update client next billing date: " + err.Error(),
			})
			return
		}
	}

	// Use a transaction to ensure both invoice and items are created together
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Create(&invoice).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Create invoice items if provided
	if len(req.InvoiceItems) > 0 {
		for i := range req.InvoiceItems {
			// Set the invoice ID for each item

			// Create the invoice item
			invoiceItem := models.InvoiceItem{
				InvoiceId:             invoice.Id,
				ClientDeviceId:        req.InvoiceItems[i].ClientDeviceId,
				TaxRateId:             req.InvoiceItems[i].TaxRateId,
				Name:                  req.InvoiceItems[i].Name,
				Description:           req.InvoiceItems[i].Description,
				Quantity:              req.InvoiceItems[i].Quantity,
				ItemPosition:          req.InvoiceItems[i].ItemPosition,
				UnitCost:              req.InvoiceItems[i].UnitCost,
				BaseCurrencyUnitCost:  req.InvoiceItems[i].BaseCurrencyUnitCost,
				Discount:              req.InvoiceItems[i].Discount,
				DiscountType:          req.InvoiceItems[i].DiscountType,
				DiscountAmount:        req.InvoiceItems[i].DiscountAmount,
				TaxAmount:             req.InvoiceItems[i].TaxAmount,
				BaseCurrencyTaxAmount: req.InvoiceItems[i].BaseCurrencyTaxAmount,
				BaseCurrencyTotal:     req.InvoiceItems[i].BaseCurrencyTotal,
				Total:                 req.InvoiceItems[i].Total,
			}

			if err := tx.Create(&invoiceItem).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"message": "Failed to create invoice item: " + err.Error(),
				})
				return
			}
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to commit transaction: " + err.Error(),
		})
		return
	}
	//generate reference
	reference := utils.GenerateReference(strconv.Itoa(int(invoice.Id)))
	config.DB.Model(&invoice).Update("reference", reference)

	// Fetch the complete invoice with items to return in the response
	var completeInvoice models.Invoice
	config.DB.Preload("InvoiceItems").First(&completeInvoice, invoice.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Invoice created successfully",
		"data":    completeInvoice,
	})
}

func UpdateInvoice(c *gin.Context) {
	// Define a custom request struct to handle invoice items
	type InvoiceWithItemsRequest struct {
		models.InvoiceRequest
		InvoiceItems []models.InvoiceItemRequest `json:"items"`
	}

	var req InvoiceWithItemsRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	var invoice models.Invoice
	if err := config.DB.First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}
	if invoice.Reference == nil {
		reference := utils.GenerateReference(c.Param("id"))
		invoice.Reference = &reference
	}
	invoice.ClientId = req.ClientId
	invoice.CouponId = req.CouponId
	invoice.TaxRateId = req.TaxRateId
	invoice.PaymentTypeId = req.PaymentTypeId
	invoice.CurrencyId = req.CurrencyId
	invoice.Date = req.Date
	invoice.DueDate = req.DueDate
	invoice.Amount = req.Amount
	invoice.Subtotal = req.Subtotal
	invoice.BaseCurrencyAmount = req.BaseCurrencyAmount
	invoice.BaseCurrencySubtotal = req.BaseCurrencySubtotal
	invoice.BaseCurrencyTaxAmount = req.BaseCurrencyTaxAmount
	invoice.Discount = req.Discount
	invoice.DiscountType = req.DiscountType
	invoice.Status = req.Status
	invoice.Xrate = req.Xrate
	invoice.DiscountAmount = req.DiscountAmount
	invoice.CouponDiscountAmount = req.CouponDiscountAmount
	invoice.TaxAmount = req.TaxAmount
	invoice.AdminNotes = req.AdminNotes
	invoice.Terms = req.Terms
	invoice.Recurring = req.Recurring
	invoice.RecurFrequency = req.RecurFrequency
	invoice.RecurStartDate = req.RecurStartDate
	invoice.RecurEndDate = req.RecurEndDate
	invoice.RecurNextDate = req.RecurNextDate
	invoice.Description = req.Description
	invoice.IsSubscription = req.IsSubscription

	// Use a transaction to ensure both invoice and items are updated together
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Save(&invoice).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Handle invoice items if provided
	if len(req.InvoiceItems) > 0 {
		// Delete existing invoice items
		if err := tx.Where("invoice_id = ?", invoice.Id).Delete(&models.InvoiceItem{}).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{
				"message": "Failed to delete existing invoice items: " + err.Error(),
			})
			return
		}

		// Create new invoice items
		for i := range req.InvoiceItems {
			// Set the invoice ID for each item

			// Create the invoice item
			invoiceItem := models.InvoiceItem{
				InvoiceId:             invoice.Id,
				ClientDeviceId:        req.InvoiceItems[i].ClientDeviceId,
				TaxRateId:             req.InvoiceItems[i].TaxRateId,
				Name:                  req.InvoiceItems[i].Name,
				Description:           req.InvoiceItems[i].Description,
				Quantity:              req.InvoiceItems[i].Quantity,
				ItemPosition:          req.InvoiceItems[i].ItemPosition,
				UnitCost:              req.InvoiceItems[i].UnitCost,
				BaseCurrencyUnitCost:  req.InvoiceItems[i].BaseCurrencyUnitCost,
				Discount:              req.InvoiceItems[i].Discount,
				DiscountType:          req.InvoiceItems[i].DiscountType,
				DiscountAmount:        req.InvoiceItems[i].DiscountAmount,
				TaxAmount:             req.InvoiceItems[i].TaxAmount,
				BaseCurrencyTaxAmount: req.InvoiceItems[i].BaseCurrencyTaxAmount,
				BaseCurrencyTotal:     req.InvoiceItems[i].BaseCurrencyTotal,
				Total:                 req.InvoiceItems[i].Total,
			}

			if err := tx.Create(&invoiceItem).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"message": "Failed to create invoice item: " + err.Error(),
				})
				return
			}
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to commit transaction: " + err.Error(),
		})
		return
	}
	UpdateInvoiceStatus(invoice.Id)

	// Fetch the complete invoice with items to return in the response
	var completeInvoice models.Invoice
	config.DB.Preload("InvoiceItems").First(&completeInvoice, invoice.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Invoice updated successfully",
		"data":    completeInvoice,
	})
}

func DeleteInvoice(c *gin.Context) {
	var invoice models.Invoice
	if err := config.DB.First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}

	result := config.DB.Delete(&invoice)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	//delete invoice items
	config.DB.Where("invoice_id = ?", invoice.Id).Delete(&models.InvoiceItem{})
	//delete invoice payments
	config.DB.Where("invoice_id = ?", invoice.Id).Delete(&models.InvoicePayment{})
	c.JSON(http.StatusOK, gin.H{
		"message": "Invoice deleted successfully",
	})
}

func SearchInvoices(c *gin.Context) {
	var invoices []models.Invoice
	filter := map[string]interface{}{}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}
	if paymentTypeId := c.Query("payment_type_id"); paymentTypeId != "" {
		filter["payment_type_id"] = paymentTypeId
	}
	if currencyId := c.Query("currency_id"); currencyId != "" {
		filter["currency_id"] = currencyId
	}
	if isSubscription := c.Query("is_subscription"); isSubscription != "" {
		filter["is_subscription"] = isSubscription
	}

	if reference := c.Query("reference"); reference != "" {
		config.DB.Where(filter).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Where("reference LIKE ?", "%"+reference+"%").Order("id desc").Find(&invoices)
	} else {
		config.DB.Where(filter).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Order("id desc").Find(&invoices)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": invoices,
	})
}
func UpdateInvoiceStatus(invoiceID uint) error {
	var invoice models.Invoice
	if err := config.DB.First(&invoice, invoiceID).Error; err != nil {
		return err
	}

	var totalPayments float64
	var totalBaseCurrencyPayments float64
	err := config.DB.Model(&models.InvoicePayment{}).
		Where("invoice_id = ?", invoice.Id).
		Select("COALESCE(SUM(amount), 0)").Scan(&totalPayments).Error
	if err != nil {
		return err
	}
	err = config.DB.Model(&models.InvoicePayment{}).
		Where("invoice_id = ?", invoice.Id).
		Select("COALESCE(SUM(base_currency_amount), 0)").Scan(&totalBaseCurrencyPayments).Error
	if err != nil {
		return err
	}

	balance := *invoice.Amount - totalPayments
	baseCurrencyBalance := *invoice.BaseCurrencyAmount - totalBaseCurrencyPayments
	invoice.Balance = &balance
	invoice.BaseCurrencyBalance = &baseCurrencyBalance

	// Skip canceled or draft invoices
	if invoice.Status == "cancelled" || invoice.Status == "draft" {
		return config.DB.Save(&invoice).Error
	}
	switch {
	case balance == 0:
		invoice.Status = "paid"
	case balance < 0:
		invoice.Status = "overpaid"
	case balance > 0 && totalPayments > 0:
		invoice.Status = "partial"
	case balance > 0 && totalPayments == 0:
		invoice.Status = "pending"
	}

	return config.DB.Save(&invoice).Error
}

// calculateNextBillingDate calculates the next billing date based on invoice date, billing cycle and billing day
func calculateNextBillingDate(invoiceDate time.Time, billingCycle string, billingDay uint) time.Time {
	switch billingCycle {
	case "monthly":
		return calculateNextMonthlyDate(invoiceDate, int(billingDay))
	case "quarterly":
		return calculateNextQuarterlyDate(invoiceDate, int(billingDay))
	case "half_yearly":
		return calculateNextHalfYearlyDate(invoiceDate, int(billingDay))
	case "yearly":
		return calculateNextYearlyDate(invoiceDate, int(billingDay))
	default:
		// Default to monthly if unknown cycle
		return calculateNextMonthlyDate(invoiceDate, int(billingDay))
	}
}

// calculateNextMonthlyDate calculates the next monthly billing date
func calculateNextMonthlyDate(from time.Time, billingDay int) time.Time {
	year, month, _ := from.Date()

	// Create the target date for this month
	targetDate := time.Date(year, month, billingDay, 0, 0, 0, 0, from.Location())

	// If the billing day doesn't exist in this month (e.g., Feb 30), use the last day of the month
	if targetDate.Month() != month {
		targetDate = time.Date(year, month+1, 0, 0, 0, 0, 0, from.Location()) // Last day of current month
	}

	// If the target date has already passed this month, move to next month
	if targetDate.Before(from) || targetDate.Equal(from) {
		nextMonth := month + 1
		nextYear := year
		if nextMonth > 12 {
			nextMonth = 1
			nextYear++
		}

		targetDate = time.Date(nextYear, nextMonth, billingDay, 0, 0, 0, 0, from.Location())

		// Handle months with fewer days
		if targetDate.Month() != nextMonth {
			targetDate = time.Date(nextYear, nextMonth+1, 0, 0, 0, 0, 0, from.Location())
		}
	}

	return targetDate
}

// calculateNextQuarterlyDate calculates the next quarterly billing date (every 3 months)
func calculateNextQuarterlyDate(from time.Time, billingDay int) time.Time {
	year, month, _ := from.Date()

	// Find the next quarter month
	var nextQuarterMonth time.Month
	switch {
	case month <= 3:
		nextQuarterMonth = 3
	case month <= 6:
		nextQuarterMonth = 6
	case month <= 9:
		nextQuarterMonth = 9
	case month <= 12:
		nextQuarterMonth = 12
	}

	targetDate := time.Date(year, nextQuarterMonth, billingDay, 0, 0, 0, 0, from.Location())

	// Handle months with fewer days
	if targetDate.Month() != nextQuarterMonth {
		targetDate = time.Date(year, nextQuarterMonth+1, 0, 0, 0, 0, 0, from.Location())
	}

	// If the target date has already passed, move to next quarter
	if targetDate.Before(from) || targetDate.Equal(from) {
		nextQuarterMonth += 3
		if nextQuarterMonth > 12 {
			nextQuarterMonth -= 12
			year++
		}

		targetDate = time.Date(year, nextQuarterMonth, billingDay, 0, 0, 0, 0, from.Location())

		// Handle months with fewer days
		if targetDate.Month() != nextQuarterMonth {
			targetDate = time.Date(year, nextQuarterMonth+1, 0, 0, 0, 0, 0, from.Location())
		}
	}

	return targetDate
}

// calculateNextHalfYearlyDate calculates the next half-yearly billing date (every 6 months)
func calculateNextHalfYearlyDate(from time.Time, billingDay int) time.Time {
	year, month, _ := from.Date()

	// Find the next half-year month (June or December)
	var nextHalfYearMonth time.Month
	if month <= 6 {
		nextHalfYearMonth = 6
	} else {
		nextHalfYearMonth = 12
	}

	targetDate := time.Date(year, nextHalfYearMonth, billingDay, 0, 0, 0, 0, from.Location())

	// Handle months with fewer days
	if targetDate.Month() != nextHalfYearMonth {
		targetDate = time.Date(year, nextHalfYearMonth+1, 0, 0, 0, 0, 0, from.Location())
	}

	// If the target date has already passed, move to next half-year
	if targetDate.Before(from) || targetDate.Equal(from) {
		if nextHalfYearMonth == 6 {
			nextHalfYearMonth = 12
		} else {
			nextHalfYearMonth = 6
			year++
		}

		targetDate = time.Date(year, nextHalfYearMonth, billingDay, 0, 0, 0, 0, from.Location())

		// Handle months with fewer days
		if targetDate.Month() != nextHalfYearMonth {
			targetDate = time.Date(year, nextHalfYearMonth+1, 0, 0, 0, 0, 0, from.Location())
		}
	}

	return targetDate
}

// calculateNextYearlyDate calculates the next yearly billing date
func calculateNextYearlyDate(from time.Time, billingDay int) time.Time {
	year, month, day := from.Date()

	// Try to create the target date for this year
	targetDate := time.Date(year, month, billingDay, 0, 0, 0, 0, from.Location())

	// If the billing day doesn't exist in this month, use the last day of the month
	if targetDate.Month() != month {
		targetDate = time.Date(year, month+1, 0, 0, 0, 0, 0, from.Location())
	}

	// If the target date has already passed this year, move to next year
	if targetDate.Before(from) || (targetDate.Equal(from) && day >= billingDay) {
		year++
		targetDate = time.Date(year, month, billingDay, 0, 0, 0, 0, from.Location())

		// Handle months with fewer days
		if targetDate.Month() != month {
			targetDate = time.Date(year, month+1, 0, 0, 0, 0, 0, from.Location())
		}
	}

	return targetDate
}
func GenerateInvoicePDF(c *gin.Context) {
	var invoice models.Invoice
	if err := config.DB.Preload("Client").Preload("Client.Country").Preload("InvoiceItems").Preload("InvoicePayments").Preload("InvoicePayments.PaymentType").Preload("Currency").Preload("PaymentType").First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}
	tmpl, err := templates.GetInvoiceTemplate()
	if err != nil {
		c.String(http.StatusInternalServerError, "Template error: %v", err)
		return
	}

	var htmlBuffer bytes.Buffer
	invoiceView := models.ToInvoiceView(invoice)
	err = tmpl.Execute(&htmlBuffer, invoiceView)
	if err != nil {
		c.String(http.StatusInternalServerError, "Template render error: %v", err)
		return
	}

	// Step 2: Generate PDF from HTML using wkhtmltopdf
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		c.String(http.StatusInternalServerError, "PDF generator error: %v", err)
		return
	}

	page := wkhtmltopdf.NewPageReader(&htmlBuffer)
	pdfg.AddPage(page)

	err = pdfg.Create()
	if err != nil {
		c.String(http.StatusInternalServerError, "PDF creation failed: %v", err)
		return
	}

	// Step 3: Serve the PDF
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", "attachment; filename=invoice.pdf")
	c.Data(http.StatusOK, "application/pdf", pdfg.Bytes())
}

func SendInvoiceEmail(c *gin.Context) {
	var invoice models.Invoice

	// Fetch the invoice with all necessary relationships
	if err := config.DB.Preload("Client").Preload("Client.Country").Preload("InvoiceItems").Preload("InvoicePayments").Preload("InvoicePayments.PaymentType").Preload("Currency").Preload("PaymentType").First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}

	// Generate PDF content (reusing logic from GenerateInvoicePDF)
	tmpl, err := templates.GetInvoiceTemplate()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Template error: " + err.Error(),
		})
		return
	}

	var htmlBuffer bytes.Buffer
	invoiceView := models.ToInvoiceView(invoice)
	err = tmpl.Execute(&htmlBuffer, invoiceView)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Template render error: " + err.Error(),
		})
		return
	}

	// Generate PDF from HTML using wkhtmltopdf
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "PDF generator error: " + err.Error(),
		})
		return
	}

	page := wkhtmltopdf.NewPageReader(&htmlBuffer)
	pdfg.AddPage(page)

	err = pdfg.Create()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "PDF creation failed: " + err.Error(),
		})
		return
	}

	// Send email with PDF attachment
	err = mail.SendInvoiceEmail(&invoice, pdfg.Bytes())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to send email: " + err.Error(),
		})
		return
	}

	// Get invoice reference for response
	invoiceReference := "Unknown"
	if invoice.Reference != nil {
		invoiceReference = *invoice.Reference
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Invoice email sent successfully",
		"data": gin.H{
			"invoice_id":   invoice.Id,
			"reference":    invoiceReference,
			"client_email": invoice.Client.Email,
		},
	})
}
