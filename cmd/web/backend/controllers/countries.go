package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
)

func GetAllCountries(c *gin.Context) {
	var countries []models.Country
	filter := map[string]interface{}{}
	if active := c.Query("active"); active != "" {
		filter["active"] = active
	}

	config.DB.Where(filter).Find(&countries)
	c.JSO<PERSON>(http.StatusOK, gin.H{
		"data": countries,
	})
}
