package controllers

import (
	"fmt"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"

	"github.com/gin-gonic/gin"
)

func GetAllDrivers(c *gin.Context) {
	var drivers []models.Driver

	query := config.DB

	// Include relations
	query = query.Preload("User").Preload("Fleet").Preload("Country").Preload("Client")

	// Apply search filter if provided
	if search := c.Query("search"); search != "" {
		query = query.Where("name LIKE ? OR driver_license_no LIKE ? OR phone_number LIKE ? OR email LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter if provided
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply client filter if provided
	if clientId := c.Query("client_id"); clientId != "" {
		query = query.Where("client_id = ?", clientId)
	}

	// Apply fleet filter if provided
	if fleetId := c.Query("fleet_id"); fleetId != "" {
		query = query.Where("fleet_id = ?", fleetId)
	}

	result := query.Find(&drivers)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to fetch drivers",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": drivers,
	})
}

func SearchDrivers(c *gin.Context) {
	var drivers []models.Driver

	query := config.DB

	// Include relations
	query = query.Preload("User").Preload("Fleet").Preload("Country").Preload("Client")

	// Apply search filter
	if search := c.Query("search"); search != "" {
		query = query.Where("name LIKE ? OR driver_license_no LIKE ? OR phone_number LIKE ? OR email LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter if provided
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply client filter if provided
	if clientId := c.Query("client_id"); clientId != "" {
		query = query.Where("client_id = ?", clientId)
	}

	// Apply fleet filter if provided
	if fleetId := c.Query("fleet_id"); fleetId != "" {
		query = query.Where("fleet_id = ?", fleetId)
	}

	// Apply pagination
	page := c.DefaultQuery("page", "1")
	limit := c.DefaultQuery("limit", "10")

	var offset int
	if page == "1" {
		offset = 0
	} else {
		offset = (parseInt(page) - 1) * parseInt(limit)
	}

	result := query.Offset(offset).Limit(parseInt(limit)).Find(&drivers)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to search drivers",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": drivers,
	})
}

func GetDriverById(c *gin.Context) {
	var driver models.Driver

	if err := config.DB.Preload("User").Preload("Fleet").Preload("Country").Preload("Client").
		First(&driver, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Driver not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": driver,
	})
}

func CreateDriver(c *gin.Context) {
	var req models.CreateDriverRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Get current user ID
	userId, _ := c.Get("user_id")

	// Create driver
	driver := models.Driver{
		CreatedById:       userId.(uint),
		ClientId:          req.ClientId,
		FleetId:           req.FleetId,
		CountryId:         req.CountryId,
		Name:              req.Name,
		Dob:               req.Dob,
		Gender:            req.Gender,
		BloodType:         req.BloodType,
		DriverLicenseNo:   req.DriverLicenseNo,
		LicenseExpiryDate: req.LicenseExpiryDate,
		PhoneNumber:       req.PhoneNumber,
		Email:             req.Email,
		Address:           req.Address,
		Rfid:              req.Rfid,
		Description:       req.Description,
		Status:            req.Status,
	}

	// Start transaction
	tx := config.DB.Begin()

	// Create driver record
	if err := tx.Create(&driver).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Failed to create driver: " + err.Error(),
		})
		return
	}

	// Create user record if email is provided
	if req.Email != nil && *req.Email != "" {
		// Generate a default password
		defaultPassword := service.GenerateRandomPassword()
		hashedPassword := service.HashPassword(defaultPassword)

		user := models.User{
			CreatedById: userId.(uint),
			ClientId:    &req.ClientId,
			DriverId:    &driver.Id,
			Name:        req.Name,
			Email:       *req.Email,
			Password:    hashedPassword,
			UserType:    "driver",
			Gender:      req.Gender,
			Status:      &req.Status,
			Description: req.Description,
		}

		if err := tx.Create(&user).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Failed to create user: " + err.Error(),
			})
			return
		}

		// Update driver with user ID
		driver.UserId = &user.Id
		if err := tx.Save(&driver).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{
				"message": "Failed to update driver with user ID",
			})
			return
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to commit transaction",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Driver created successfully",
		"data":    driver,
	})
}

func UpdateDriver(c *gin.Context) {
	var req models.UpdateDriverRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	var driver models.Driver
	if err := config.DB.First(&driver, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Driver not found",
		})
		return
	}

	// Update driver fields
	driver.FleetId = req.FleetId
	driver.CountryId = req.CountryId
	driver.Name = req.Name
	driver.Dob = req.Dob
	driver.Gender = req.Gender
	driver.BloodType = req.BloodType
	driver.DriverLicenseNo = req.DriverLicenseNo
	driver.LicenseExpiryDate = req.LicenseExpiryDate
	driver.PhoneNumber = req.PhoneNumber
	driver.Email = req.Email
	driver.Address = req.Address
	driver.Rfid = req.Rfid
	driver.Description = req.Description
	driver.Status = req.Status

	// Start transaction
	tx := config.DB.Begin()

	// Update driver
	if err := tx.Save(&driver).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Failed to update driver: " + err.Error(),
		})
		return
	}

	// Update associated user if exists
	if driver.UserId != nil {
		var user models.User
		if err := tx.First(&user, *driver.UserId).Error; err == nil {
			user.Name = req.Name
			user.Gender = req.Gender
			user.Status = &req.Status
			user.Description = req.Description

			if req.Email != nil && *req.Email != "" {
				user.Email = *req.Email
			}

			if err := tx.Save(&user).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"message": "Failed to update user: " + err.Error(),
				})
				return
			}
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to commit transaction",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Driver updated successfully",
		"data":    driver,
	})
}

func DeleteDriver(c *gin.Context) {
	var driver models.Driver
	if err := config.DB.First(&driver, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Driver not found",
		})
		return
	}

	// Start transaction
	tx := config.DB.Begin()

	// Delete associated user if exists
	if driver.UserId != nil {
		if err := tx.Delete(&models.User{}, *driver.UserId).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{
				"message": "Failed to delete associated user",
			})
			return
		}
	}

	// Delete driver
	if err := tx.Delete(&driver).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to delete driver",
		})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to commit transaction",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Driver deleted successfully",
	})
}

// Helper function to parse string to int
func parseInt(s string) int {
	var i int
	_, err := fmt.Sscanf(s, "%d", &i)
	if err != nil {
		return 10 // default limit
	}
	return i
}
