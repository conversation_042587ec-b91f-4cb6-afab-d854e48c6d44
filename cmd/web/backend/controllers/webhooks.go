package controllers

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/mail"
	"yotracker/internal/models"
	"yotracker/internal/payment_gateways"
)

func PaynowWebhook(c *gin.Context) {
	//print all params
	fmt.Println("Webhook received")
	c.Request.ParseForm() // Needed if not already parsed
	for key, values := range c.Request.PostForm {
		for _, value := range values {
			fmt.Printf("Key: %s, Value: %s\n", key, value)
		}
	}
	var paymentType models.PaymentType
	if err := config.DB.Where("system_name = ?", "paynow").First(&paymentType).Error; err != nil {
		c.JSON(400, gin.H{
			"message": "Payment type not found",
		})
		return
	}
	var options map[string]string
	err := json.Unmarshal(*paymentType.Options, &options)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"message": "Error unmarshalling options",
		})
		return
	}

	pollUrl := c.Request.FormValue("pollurl")
	status := c.Request.FormValue("status")
	amount := c.Request.FormValue("amount")
	hash := c.Request.FormValue("hash")
	reference := c.Request.FormValue("reference")
	paynowReference := c.Request.FormValue("paynowreference")
	fmt.Println(reference, amount, paynowReference, pollUrl, status, hash)
	var invoice models.Invoice
	if err := config.DB.Preload("Client").Preload("Currency").Preload("InvoiceItems").Preload("InvoicePayments").Preload("InvoicePayments.PaymentType").Where("id = ?", reference).First(&invoice).Error; err != nil {
		c.JSON(400, gin.H{
			"message": "Invoice not found",
		})
		return
	}
	rtgsIntegrationKey := options["usd_integration_key"]
	rtgsIntegrationIdValue := options["rtgs_integration_id"]
	rtgsIntegrationId, err := strconv.Atoi(rtgsIntegrationIdValue)
	usdIntegrationKey := options["usd_integration_key"]
	usdIntegrationIdValue := options["usd_integration_id"]
	usdIntegrationId, err := strconv.Atoi(usdIntegrationIdValue)
	var integrationId int
	var integrationKey string
	if invoice.Currency.Code == "USD" {
		integrationId = usdIntegrationId
		integrationKey = usdIntegrationKey
	} else {
		integrationId = rtgsIntegrationId
		integrationKey = rtgsIntegrationKey
	}
	hashData := []string{
		reference,
		paynowReference,
		amount,
		status,
		pollUrl,
	}
	paynow := payment_gateways.Paynow{
		IntegrationKey: integrationKey,
		IntegrationId:  integrationId,
	}
	if !paynow.VerifyHash(hashData, hash) {
		c.JSON(400, gin.H{
			"message": "Invalid hash",
		})
		return
	}

	if status == "Paid" || status == "Awaiting Delivery" {
		today := time.Now()
		//convert amount to float
		convertedAmount, err := strconv.ParseFloat(amount, 64)
		if err != nil {
			c.JSON(400, gin.H{
				"message": "Invalid amount",
			})
			return
		}
		payment := models.InvoicePayment{
			InvoiceId:     invoice.Id,
			PaymentTypeId: paymentType.Id,
			CurrencyId:    invoice.CurrencyId,
			Date:          &today,
			TransId:       &paynowReference,
			Amount:        convertedAmount,
			Xrate:         invoice.Xrate,
		}
		result := config.DB.Create(&payment)
		if result.Error != nil {
			c.JSON(400, gin.H{
				"message": "Failed to create payment",
			})
			return
		}
		UpdateInvoiceStatus(invoice.Id)
		//refresh invoice payment data
		config.DB.Preload("Invoice").Preload("Invoice.Client").Preload("PaymentType").Preload("Currency").First(&payment)
		//notify client
		go func() {
			err := mail.SendInvoicePaymentEmail(&payment)
			if err != nil {
				// Log error but don't fail the request
				log.Printf("Failed to send invoice payment email: %v", err)
			}
		}()

	}
	fmt.Println(pollUrl, status, amount, hash, reference, paynowReference)
	c.JSON(200, gin.H{
		"message": "Webhook received",
	})
}
