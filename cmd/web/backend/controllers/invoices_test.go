package controllers

import (
	"testing"
	"time"
)

func TestCalculateNextBillingDate(t *testing.T) {
	baseDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	// Test monthly billing
	nextDate := calculateNextBillingDate(baseDate, "monthly", 15)
	expected := time.Date(2024, 2, 15, 0, 0, 0, 0, time.UTC)
	if !nextDate.Equal(expected) {
		t.<PERSON><PERSON>rf("Monthly billing: expected %v, got %v", expected, nextDate)
	}

	// Test quarterly billing (January 15 -> March 15, since January is in Q1 and we go to next quarter end)
	nextDate = calculateNextBillingDate(baseDate, "quarterly", 15)
	expected = time.Date(2024, 3, 15, 0, 0, 0, 0, time.UTC)
	if !nextDate.Equal(expected) {
		t.Errorf("Quarterly billing: expected %v, got %v", expected, nextDate)
	}

	// Test yearly billing
	nextDate = calculateNextBillingDate(baseDate, "yearly", 15)
	expected = time.Date(2025, 1, 15, 0, 0, 0, 0, time.UTC)
	if !nextDate.Equal(expected) {
		t.Errorf("Yearly billing: expected %v, got %v", expected, nextDate)
	}

	// Test half-yearly billing (January 15 -> June 15, since January is in H1 and we go to end of H1)
	nextDate = calculateNextBillingDate(baseDate, "half_yearly", 15)
	expected = time.Date(2024, 6, 15, 0, 0, 0, 0, time.UTC)
	if !nextDate.Equal(expected) {
		t.Errorf("Half-yearly billing: expected %v, got %v", expected, nextDate)
	}
}
