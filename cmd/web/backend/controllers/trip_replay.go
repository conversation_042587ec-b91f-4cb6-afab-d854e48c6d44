package controllers

import (
	"fmt"
	"net/http"
	"strconv"
	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
)

// TripReplayResponse represents the response for trip replay
type TripReplayResponse struct {
	Trip    models.Trip      `json:"trip"`
	GPSData []models.GPSData `json:"gps_data"`
	Stats   TripReplayStats  `json:"stats"`
}

// TripReplayStats provides statistics about the trip replay data
type TripReplayStats struct {
	TotalPoints      int     `json:"total_points"`
	MovingPoints     int     `json:"moving_points"`
	StationaryPoints int     `json:"stationary_points"`
	MaxSpeed         float64 `json:"max_speed"`
	AvgSpeed         float64 `json:"avg_speed"`
	Duration         string  `json:"duration"`
}

// GetTripReplay returns all GPS data for a specific trip for replay functionality
func GetTripReplay(c *gin.Context) {
	tripIDStr := c.Param("trip_id")
	tripID, err := strconv.ParseUint(tripIDStr, 10, 32)
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid trip ID"})
		return
	}

	// Get the trip details
	var trip models.Trip
	if err := config.DB.Preload("ClientDevice").First(&trip, uint(tripID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Trip not found"})
		return
	}

	// Get ALL GPS data for this trip, ordered by timestamp for smooth replay
	var gpsData []models.GPSData
	query := config.DB.Where("trip_id = ?", tripID).Order("gps_timestamp ASC")

	// Optional: Add pagination for very large trips
	if limit := c.Query("limit"); limit != "" {
		if limitInt, err := strconv.Atoi(limit); err == nil && limitInt > 0 {
			query = query.Limit(limitInt)
		}
	}

	if offset := c.Query("offset"); offset != "" {
		if offsetInt, err := strconv.Atoi(offset); err == nil && offsetInt >= 0 {
			query = query.Offset(offsetInt)
		}
	}

	if err := query.Find(&gpsData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch GPS data"})
		return
	}

	// Calculate statistics
	stats := calculateTripReplayStats(gpsData)

	response := TripReplayResponse{
		Trip:    trip,
		GPSData: gpsData,
		Stats:   stats,
	}

	c.JSON(http.StatusOK, response)
}

// GetTripReplaySimplified returns simplified GPS data for trip replay (reduced data for performance)
func GetTripReplaySimplified(c *gin.Context) {
	tripIDStr := c.Param("trip_id")
	tripID, err := strconv.ParseUint(tripIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid trip ID"})
		return
	}

	// Get simplified GPS data (every Nth point for performance)
	interval := 5 // Show every 5th point by default
	if intervalStr := c.Query("interval"); intervalStr != "" {
		if intervalInt, err := strconv.Atoi(intervalStr); err == nil && intervalInt > 0 {
			interval = intervalInt
		}
	}

	var gpsData []models.GPSData
	// Use ROW_NUMBER() to get every Nth record
	err = config.DB.Raw(`
		SELECT * FROM (
			SELECT *, ROW_NUMBER() OVER (ORDER BY gps_timestamp) as row_num
			FROM gps_data 
			WHERE trip_id = ?
		) as numbered_data
		WHERE row_num % ? = 1
		ORDER BY gps_timestamp ASC
	`, tripID, interval).Scan(&gpsData).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch GPS data"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"trip_id":      tripID,
		"gps_data":     gpsData,
		"interval":     interval,
		"total_points": len(gpsData),
	})
}

// calculateTripReplayStats calculates statistics for the trip replay
func calculateTripReplayStats(gpsData []models.GPSData) TripReplayStats {
	stats := TripReplayStats{
		TotalPoints: len(gpsData),
	}

	if len(gpsData) == 0 {
		return stats
	}

	var totalSpeed float64
	var speedCount int
	var maxSpeed float64

	for _, point := range gpsData {
		if point.Speed != nil {
			speed := *point.Speed
			if speed > 0 {
				stats.MovingPoints++
				totalSpeed += speed
				speedCount++
				if speed > maxSpeed {
					maxSpeed = speed
				}
			} else {
				stats.StationaryPoints++
			}
		} else {
			stats.StationaryPoints++
		}
	}

	stats.MaxSpeed = maxSpeed
	if speedCount > 0 {
		stats.AvgSpeed = totalSpeed / float64(speedCount)
	}

	// Calculate duration
	if len(gpsData) > 1 {
		start := gpsData[0].GPSTimestamp
		end := gpsData[len(gpsData)-1].GPSTimestamp
		if start != nil && end != nil {
			duration := end.Sub(*start)
			hours := int(duration.Hours())
			minutes := int(duration.Minutes()) % 60
			if hours > 0 {
				stats.Duration = fmt.Sprintf("%dh %dm", hours, minutes)
			} else {
				stats.Duration = fmt.Sprintf("%dm", minutes)
			}
		}
	}

	return stats
}
