package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/seed"
	"yotracker/internal/service"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func setupUpdateProfileBackendTest() {
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}
	if os.Getenv("APP_KEY") == "" {
		os.Setenv("APP_KEY", "test-secret-key")
	}

	config.InitTestDB()
	migrations.Migrate()
	seed.Seed()
	gin.SetMode(gin.TestMode)
}

func TestUpdateProfileBackend(t *testing.T) {
	// Setup test database
	setupUpdateProfileBackendTest()

	// Create test user
	hashedPassword := service.HashPassword("password123")
	user := models.User{
		Name:     "Test Backend User",
		Email:    "<EMAIL>",
		Password: hashedPassword,
		UserType: "backend",
	}
	config.DB.Create(&user)

	// Generate token for authentication
	token, _ := service.GenerateToken(&user, "access")

	t.Run("successful profile update", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]interface{}{
			"name":              "Updated Backend User",
			"email":             "<EMAIL>",
			"gender":            "female",
			"telegram_user_id":  "987654321",
			"slack_webhook_url": "https://hooks.slack.com/backend",
			"description":       "Updated backend description",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/profile", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		// Mock middleware to set user in context
		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/profile", UpdateProfile)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Profile updated successfully", response["message"])

		// Verify profile was actually updated
		var updatedUser models.User
		config.DB.First(&updatedUser, user.Id)
		assert.Equal(t, "Updated Backend User", updatedUser.Name)
		assert.Equal(t, "<EMAIL>", updatedUser.Email)
		assert.Equal(t, "female", *updatedUser.Gender)
		assert.Equal(t, "987654321", *updatedUser.TelegramUserId)
		assert.Equal(t, "https://hooks.slack.com/backend", *updatedUser.SlackWebhookUrl)
		assert.Equal(t, "Updated backend description", *updatedUser.Description)
	})

	t.Run("email already exists", func(t *testing.T) {
		// Create another user with a different email
		anotherUser := models.User{
			Name:     "Another Backend User",
			Email:    "<EMAIL>",
			Password: hashedPassword,
			UserType: "backend",
		}
		config.DB.Create(&anotherUser)

		router := gin.New()
		reqBody := map[string]interface{}{
			"name":  "Updated Name",
			"email": "<EMAIL>", // Try to use existing email
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/profile", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/profile", UpdateProfile)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Email already exists", response["message"])

		// Cleanup
		config.DB.Delete(&anotherUser)
	})

	t.Run("same email update allowed", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]interface{}{
			"name":  "Updated Name Same Email",
			"email": user.Email, // Use same email - should be allowed
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/profile", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/profile", UpdateProfile)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Profile updated successfully", response["message"])
	})

	t.Run("invalid email format", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]interface{}{
			"name":  "Updated Name",
			"email": "invalid-email-format", // Invalid email
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/profile", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/profile", UpdateProfile)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("unauthorized access", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]interface{}{
			"name":  "Updated Name",
			"email": "<EMAIL>",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/profile", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		// No middleware to set user in context
		router.PUT("/profile", UpdateProfile)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Unauthorized", response["message"])
	})

	// Cleanup
	config.DB.Delete(&user)
}
