package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"

	"github.com/gin-gonic/gin"
)

// GetAllReports returns all reports (admin view)
func GetAllReports(c *gin.Context) {
	var reports []models.Report
	err := config.DB.Order("category, name").Find(&reports).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch reports"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    reports,
	})
}

// CreateReport creates a new report definition (admin only)
func CreateReport(c *gin.Context) {
	var request models.CreateReportRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	report := models.Report{
		Name:           request.Name,
		Description:    request.Description,
		Category:       request.Category,
		ReportType:     request.ReportType,
		DefaultFilters: request.DefaultFilters,
		Status:         models.ReportStatusActive,
	}

	err := config.DB.Create(&report).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create report"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    report,
		"message": "Report created successfully",
	})
}

// UpdateReport updates a report definition (admin only)
func UpdateReport(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	var report models.Report
	err = config.DB.First(&report, uint(reportID)).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
		return
	}

	var request models.UpdateReportRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Update fields if provided
	if request.Name != nil {
		report.Name = *request.Name
	}
	if request.Description != nil {
		report.Description = *request.Description
	}
	if request.Category != nil {
		report.Category = *request.Category
	}
	if request.Status != nil {
		report.Status = *request.Status
	}
	if request.DefaultFilters != nil {
		report.DefaultFilters = request.DefaultFilters
	}

	err = config.DB.Save(&report).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update report"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    report,
		"message": "Report updated successfully",
	})
}

// DeleteReport deletes a report definition (admin only)
func DeleteReport(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	err = config.DB.Delete(&models.Report{}, uint(reportID)).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete report"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Report deleted successfully",
	})
}

// GetAllScheduledReports returns all scheduled reports (admin view)
func GetAllScheduledReports(c *gin.Context) {
	var scheduledReports []models.ScheduledReport
	err := config.DB.Preload("Report").
		Order("created_at DESC").
		Find(&scheduledReports).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch scheduled reports"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    scheduledReports,
	})
}

// GetScheduledReportById returns a specific scheduled report
func GetScheduledReportById(c *gin.Context) {
	scheduledReportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scheduled report ID"})
		return
	}

	var scheduledReport models.ScheduledReport
	err = config.DB.Preload("Report").First(&scheduledReport, uint(scheduledReportID)).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Scheduled report not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    scheduledReport,
	})
}

// RunScheduledReport manually runs a scheduled report
func RunScheduledReport(c *gin.Context) {
	scheduledReportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scheduled report ID"})
		return
	}

	var scheduledReport models.ScheduledReport
	err = config.DB.Preload("Report").First(&scheduledReport, uint(scheduledReportID)).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Scheduled report not found"})
		return
	}

	// Parse filters
	var filters models.ReportFilters
	if scheduledReport.Filters != nil {
		err = json.Unmarshal(scheduledReport.Filters, &filters)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid filter configuration"})
			return
		}
	}

	// Set default date range if not provided
	if filters.StartDate == nil || filters.EndDate == nil {
		endDate := time.Now()
		startDate := endDate.AddDate(0, 0, -30)
		if filters.StartDate == nil {
			filters.StartDate = &startDate
		}
		if filters.EndDate == nil {
			filters.EndDate = &endDate
		}
	}

	// Generate report
	reportService := service.NewReportService()
	reportData, err := reportService.GenerateReport(scheduledReport.ReportId, filters, scheduledReport.Format)
	if err != nil {
		// Update last run status
		scheduledReport.LastStatus = "failed"
		errorMsg := err.Error()
		scheduledReport.LastError = &errorMsg
		now := time.Now()
		scheduledReport.LastRunAt = &now
		config.DB.Save(&scheduledReport)

		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate report: %v", err)})
		return
	}

	// Update last run status
	scheduledReport.LastStatus = "success"
	scheduledReport.LastError = nil
	now := time.Now()
	scheduledReport.LastRunAt = &now
	scheduledReport.RunCount++
	config.DB.Save(&scheduledReport)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    reportData,
		"message": "Scheduled report executed successfully",
	})
}

// GetReportExecutionHistory returns execution history for scheduled reports
func GetReportExecutionHistory(c *gin.Context) {
	var scheduledReports []models.ScheduledReport
	err := config.DB.Preload("Report").
		Where("last_run_at IS NOT NULL").
		Order("last_run_at DESC").
		Limit(100).
		Find(&scheduledReports).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch execution history"})
		return
	}

	// Transform data for history view
	var history []gin.H
	for _, sr := range scheduledReports {
		historyItem := gin.H{
			"id":            sr.Id,
			"report_name":   sr.Report.Name,
			"schedule_name": sr.Name,
			"last_run_at":   sr.LastRunAt,
			"status":        sr.LastStatus,
			"error":         sr.LastError,
			"run_count":     sr.RunCount,
			"format":        sr.Format,
		}
		history = append(history, historyItem)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    history,
	})
}

// GetReportCategories returns all available report categories
func GetReportCategories(c *gin.Context) {
	var categories []string
	err := config.DB.Model(&models.Report{}).
		Distinct("category").
		Where("status = ?", "active").
		Pluck("category", &categories).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    categories,
	})
}

// GetReportStats returns statistics about reports usage
func GetReportStats(c *gin.Context) {
	var stats struct {
		TotalReports          int64 `json:"total_reports"`
		ActiveReports         int64 `json:"active_reports"`
		TotalScheduledReports int64 `json:"total_scheduled_reports"`
		ActiveSchedules       int64 `json:"active_schedules"`
		ReportsRunToday       int64 `json:"reports_run_today"`
		ReportsRunThisWeek    int64 `json:"reports_run_this_week"`
	}

	// Count reports
	config.DB.Model(&models.Report{}).Count(&stats.TotalReports)
	config.DB.Model(&models.Report{}).Where("status = ?", "active").Count(&stats.ActiveReports)

	// Count scheduled reports
	config.DB.Model(&models.ScheduledReport{}).Count(&stats.TotalScheduledReports)
	config.DB.Model(&models.ScheduledReport{}).Where("status = ?", "active").Count(&stats.ActiveSchedules)

	// Count executions
	today := time.Now().Truncate(24 * time.Hour)
	weekAgo := today.AddDate(0, 0, -7)

	config.DB.Model(&models.ScheduledReport{}).
		Where("last_run_at >= ?", today).
		Count(&stats.ReportsRunToday)

	config.DB.Model(&models.ScheduledReport{}).
		Where("last_run_at >= ?", weekAgo).
		Count(&stats.ReportsRunThisWeek)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}
