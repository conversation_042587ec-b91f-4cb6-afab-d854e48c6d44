package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
)

// GetAllSettings returns all settings (backend users have access to all settings)
func GetAllSettings(c *gin.Context) {
	var settings []models.Setting
	filter := map[string]interface{}{}

	// Optional filtering by category
	if category := c.Query("category"); category != "" {
		filter["category"] = category
	}

	config.DB.Where(filter).Order("category, name").Find(&settings)
	c.JSON(http.StatusOK, gin.H{
		"data": settings,
	})
}

// GetAllowedSettings returns all settings that are allowed to be retrieved by frontend users
func GetAllowedSettings(c *gin.Context) {
	allowedSettings := models.GetAllowedSettings()

	var settings []models.Setting
	for _, settingKey := range allowedSettings {
		setting := models.GetSettingByKey(settingKey)
		if setting.Id != 0 { // Only include if setting exists
			settings = append(settings, setting)
		}
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"data": settings,
	})
}

// GetSettingByKey returns a specific setting by its key (backend has access to all)
func GetSettingByKey(c *gin.Context) {
	settingKey := c.Param("key")

	setting := models.GetSettingByKey(settingKey)
	if setting.Id == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Setting not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": setting,
	})
}

// GetSettingValue returns just the value of a specific setting (backend has access to all)
func GetSettingValue(c *gin.Context) {
	settingKey := c.Param("key")

	value := models.GetSetting(settingKey)
	if value == "" {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Setting not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"value": value,
	})
}

// UpdateSetting updates a setting value
func UpdateSetting(c *gin.Context) {
	settingKey := c.Param("key")

	var setting models.Setting
	if err := config.DB.Where("setting_key = ?", settingKey).First(&setting).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Setting not found",
		})
		return
	}

	var req struct {
		SettingValue string `json:"setting_value" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	setting.SettingValue = req.SettingValue
	if err := config.DB.Save(&setting).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Setting updated successfully",
		"data":    setting,
	})
}

// CreateSetting creates a new setting
func CreateSetting(c *gin.Context) {
	var req struct {
		Name         string  `json:"name" binding:"required"`
		SettingKey   string  `json:"setting_key" binding:"required"`
		SettingValue string  `json:"setting_value" binding:"required"`
		Category     *string `json:"category"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	setting := models.Setting{
		Name:         req.Name,
		SettingKey:   req.SettingKey,
		SettingValue: req.SettingValue,
		Category:     req.Category,
	}

	if err := config.DB.Create(&setting).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Setting created successfully",
		"data":    setting,
	})
}

// DeleteSetting deletes a setting
func DeleteSetting(c *gin.Context) {
	settingKey := c.Param("key")

	var setting models.Setting
	if err := config.DB.Where("setting_key = ?", settingKey).First(&setting).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Setting not found",
		})
		return
	}

	if err := config.DB.Delete(&setting).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Setting deleted successfully",
	})
}

// UpdateAllSettings updates multiple settings at once
func UpdateAllSettings(c *gin.Context) {
	// Parse the request body as a map of setting_key -> setting_value
	var req map[string]string
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	if len(req) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "No settings provided for update",
		})
		return
	}

	// Get all settings from database
	var allSettings []models.Setting
	if err := config.DB.Find(&allSettings).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to retrieve settings",
		})
		return
	}

	// Start a database transaction
	tx := config.DB.Begin()
	if tx.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to start transaction",
		})
		return
	}

	var updatedSettings []models.Setting
	var updatedCount int

	// Loop through all settings and check if they need to be updated
	for _, setting := range allSettings {
		if newValue, exists := req[setting.SettingKey]; exists {
			// Update the setting value
			setting.SettingValue = newValue
			if err := tx.Save(&setting).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusBadRequest, gin.H{
					"message": "Failed to update setting: " + setting.SettingKey + " - " + err.Error(),
				})
				return
			}
			updatedSettings = append(updatedSettings, setting)
			updatedCount++
		}
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to commit transaction",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":          "Settings updated successfully",
		"updated_count":    updatedCount,
		"updated_settings": updatedSettings,
	})
}
