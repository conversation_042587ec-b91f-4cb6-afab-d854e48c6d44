package main

import (
	"bytes"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"yotracker/cmd/web/backend/controllers"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func TestSetupRouter(t *testing.T) {
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}
	if os.Getenv("APP_KEY") == "" {
		os.Setenv("APP_KEY", "test-secret-key")
	}

	config.InitTestDB()
	migrations.Migrate()
	gin.SetMode(gin.TestMode)
	hub := controllers.NewHub()
	r := SetupRouter(hub)
	if r == nil {
		t.Errorf("Expected router to be not nil, got nil")
	}
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})
	//create a test user
	password := service.HashPassword("password")
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Admin",
	}
	fmt.Println(password)
	config.DB.Create(&user)
	//get test user token
	token, _ := service.GenerateToken(&user, "access")
	fmt.Println("Using Token: ", token)
	//test login
	t.Run("TestCanLoginUserWithValidDetails", func(t *testing.T) {
		reqBody := `{"email": "<EMAIL>", "password": "password"}`
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/login", bytes.NewBuffer([]byte(reqBody)))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		// Assert that the status code is 200 OK
		assert.Equal(t, http.StatusOK, w.Code)
	})
	t.Run("TestCannotLoginUserWithInvalidDetails", func(t *testing.T) {
		reqBody := `{"email": "<EMAIL>", "password": "wrongpassword"}`
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/login", bytes.NewBuffer([]byte(reqBody)))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		// Assert that the status code is 200 OK
		assert.Equal(t, http.StatusUnprocessableEntity, w.Code)
	})
	//users test
	t.Run("TestCanCreateUser", func(t *testing.T) {
		config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})
		reqBody := `{"name": "test", "email": "<EMAIL>", "password": "password","user_type": "backend","status": "active"}`
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/users", bytes.NewBuffer([]byte(reqBody)))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		// Assert that the status code is 200 OK
		assert.Equal(t, http.StatusOK, w.Code)

	})

	t.Run("TestCanUpdateUser", func(t *testing.T) {
		config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})
		testUser := models.User{
			Email:    "<EMAIL>",
			Password: password,
			Name:     "Test",
		}
		config.DB.Create(&testUser)
		fmt.Println(testUser)
		reqBody := `{"name": "Test Updated", "email": "<EMAIL>", "user_type": "backend", "status": "active"}`
		req, _ := http.NewRequest(http.MethodPut, "/api/v1/backend/users/"+fmt.Sprint(testUser.Id), bytes.NewBuffer([]byte(reqBody)))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		// Assert that the status code is 200 OK
		assert.Equal(t, http.StatusOK, w.Code)
		config.DB.First(&testUser)
		assert.Equal(t, "Test Updated", testUser.Name)
	})
}
