package main

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"os"
	"yotracker/cmd/web/frontend/controllers"
	"yotracker/cmd/web/frontend/routes"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/migrations"
)

func main() {
	//connect to db
	config.InitDB()
	//run migrations
	migrations.Migrate()
	//get env
	env := os.Getenv("APP_ENV")
	//set mode
	if env == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}
	hub := controllers.NewHub()
	go hub.Run()
	r := SetupRouter(hub)
	err := r.Run(os.Getenv("BACKEND_HOST") + ":" + os.Getenv("FRONTEND_PORT"))
	if err != nil {
		fmt.Println("Failed to start frontend server")
	}
}
func SetupRouter(hub *controllers.Hub) *gin.Engine {
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())
	r.St<PERSON>("/assets", "./assets")
	routes.FrontendRoutes(hub, r)
	return r

}
