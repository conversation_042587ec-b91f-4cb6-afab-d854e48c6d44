package routes

import (
	"yotracker/cmd/web/frontend/controllers"
	"yotracker/cmd/web/middleware"
	publiccontrollers "yotracker/cmd/web/public/controllers"

	"github.com/gin-gonic/gin"
)

func FrontendRoutes(hub *controllers.Hub, r *gin.Engine) {
	v1 := r.Group("/api/v1/frontend")
	v1.GET("", controllers.Home)
	// Public device share route
	v1.GET("/public/device/temp_location", middleware.PublicDeviceShareMiddleware(), publiccontrollers.GetSharedDeviceLocation)
	v1.POST("/login", controllers.Login)
	v1.POST("/register", controllers.Register)
	// Password reset routes (no auth required)
	v1.POST("/password-reset/request", controllers.RequestPasswordReset)
	v1.POST("/password-reset/verify", controllers.VerifyPasswordResetToken)
	v1.POST("/password-reset/confirm", controllers.ResetPassword)
	v1.Use(middleware.AuthMiddleware())
	v1.Use(middleware.CheckForClient())
	//websocket
	v1.GET("/ws/:channel", func(c *gin.Context) {
		controllers.HandleWebsocketRequest(hub, c)
	})
	//users
	users := v1.Group("/users")
	users.GET("", controllers.GetAllUsers)
	users.GET("/search", controllers.SearchUsers)
	users.GET("/:id", controllers.GetUserById)
	users.POST("", controllers.CreateUser)
	users.PUT("/:id", controllers.UpdateUser)
	users.DELETE("/:id", controllers.DeleteUser)
	users.GET("/profile", controllers.Profile)
	users.PUT("/profile", controllers.UpdateProfile)
	users.PUT("/change_password", controllers.ChangePassword)
	//roles
	roles := v1.Group("/roles")
	roles.GET("", controllers.GetAllRoles)
	roles.GET("/search", controllers.SearchRoles)
	roles.GET("/:id", controllers.GetRoleById)
	roles.POST("", controllers.CreateRole)
	roles.PUT("/:id", controllers.UpdateRole)
	roles.DELETE("/:id", controllers.DeleteRole)
	//clients
	clients := v1.Group("/clients")
	clients.PUT("", controllers.UpdateClient)
	//fleets
	fleets := v1.Group("/fleets")
	fleets.GET("", controllers.GetAllFleets)
	fleets.GET("/search", controllers.SearchFleets)
	fleets.GET("/:id", controllers.GetFleetById)
	fleets.POST("", controllers.CreateFleet)
	fleets.PUT("/:id", controllers.UpdateFleet)
	fleets.DELETE("/:id", controllers.DeleteFleet)
	//protocols
	protocols := v1.Group("/protocols")
	protocols.GET("", controllers.GetAllProtocols)
	protocols.GET("/search", controllers.SearchProtocols)
	protocols.GET("/:id", controllers.GetProtocolById)

	//device types
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("", controllers.GetAllDeviceTypes)
	deviceTypes.GET("/search", controllers.SearchDeviceTypes)
	deviceTypes.GET("/:id", controllers.GetDeviceTypeById)

	//client device
	clientDevices := v1.Group("/devices")
	clientDevices.GET("", controllers.GetAllClientDevices)
	clientDevices.GET("/search", controllers.SearchClientDevices)
	clientDevices.GET("/:id", controllers.GetClientDeviceById)
	clientDevices.PUT("/:id", controllers.UpdateClientDevice)
	clientDevices.POST("/:id/share", controllers.CreateDeviceShareToken)
	//gps data
	gpsData := v1.Group("/gps_data")
	gpsData.GET("/search", controllers.SearchGpsData)
	gpsData.GET("/last_location", controllers.GetLastLocation)
	gpsData.GET("/:id", controllers.GetGpsDataById)

	//alerts
	alerts := v1.Group("/alerts")
	alerts.GET("", controllers.GetAllAlerts)
	alerts.GET("/search", controllers.SearchAlerts)
	alerts.GET("/:id", controllers.GetAlertById)
	alerts.PATCH("/:id/mark-read", controllers.MarkAlertAsRead)
	alerts.PATCH("/:id/mark-unread", controllers.MarkAlertAsUnread)
	alerts.PATCH("/bulk/mark-read", controllers.BulkMarkAlertsAsRead)
	alerts.PATCH("/mark-all-read", controllers.MarkAllAlertsAsRead)

	//geofences
	geofences := v1.Group("/geofences")
	geofences.GET("", controllers.GetAllGeofences)
	geofences.GET("/search", controllers.SearchGeofences)
	geofences.GET("/:id", controllers.GetGeofenceById)
	geofences.POST("", controllers.CreateGeofence)
	geofences.PUT("/:id", controllers.UpdateGeofence)
	geofences.DELETE("/:id", controllers.DeleteGeofence)
	geofences.GET("/events", controllers.GetGeofenceEvents)
	geofences.GET("/device/:device_id", controllers.GetGeofencesByDevice)

	//drivers
	drivers := v1.Group("/drivers")
	drivers.GET("", controllers.GetAllDrivers)
	drivers.GET("/search", controllers.SearchDrivers)
	drivers.GET("/:id", controllers.GetDriverById)
	drivers.POST("", controllers.CreateDriver)
	drivers.PUT("/:id", controllers.UpdateDriver)
	drivers.DELETE("/:id", controllers.DeleteDriver)

	//driver device assignments
	driverAssignments := v1.Group("/driver_device_assignments")
	driverAssignments.GET("", controllers.GetAllDriverDeviceAssignments)
	driverAssignments.GET("/search", controllers.SearchDriverDeviceAssignments)
	driverAssignments.GET("/:id", controllers.GetDriverDeviceAssignmentById)
	driverAssignments.POST("", controllers.CreateDriverDeviceAssignment)
	driverAssignments.POST("/rfid", controllers.CreateDriverDeviceAssignmentByRfid)
	driverAssignments.PUT("/:id", controllers.UpdateDriverDeviceAssignment)
	driverAssignments.DELETE("/:id", controllers.DeleteDriverDeviceAssignment)
	driverAssignments.GET("/current", controllers.GetCurrentDriverAssignment)

	//command logs
	commandLogs := v1.Group("/command_logs")
	commandLogs.GET("", controllers.GetAllCommandLogs)
	commandLogs.GET("/search", controllers.SearchCommandLogs)
	commandLogs.GET("/:id", controllers.GetCommandLogById)
	commandLogs.POST("", controllers.CreateCommandLog)
	commandLogs.PUT("/:id", controllers.UpdateCommandLog)
	commandLogs.DELETE("/:id", controllers.DeleteCommandLog)
	//invoices
	invoices := v1.Group("/invoices")
	invoices.GET("", controllers.GetAllInvoices)
	invoices.GET("/search", controllers.SearchInvoices)
	invoices.GET("/:id", controllers.GetInvoiceById)
	invoices.GET("/:id/pdf", controllers.GenerateInvoicePDF)
	invoices.POST("/:id/email", controllers.SendInvoiceEmail)

	//payments
	payments := v1.Group("/payments")
	payments.GET("", controllers.GetAllPayments)
	payments.GET("/search", controllers.SearchPayments)
	payments.GET("/:id", controllers.GetPaymentById)
	payments.POST("", controllers.CreatePayment)
	//payment types
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("", controllers.GetAllPaymentTypes)
	paymentTypes.GET("/search", controllers.SearchPaymentTypes)
	paymentTypes.GET("/:id", controllers.GetPaymentTypeById)

	//currencies
	currencies := v1.Group("/currencies")
	currencies.GET("", controllers.GetAllCurrencies)
	currencies.GET("/search", controllers.SearchCurrencies)
	currencies.GET("/:id", controllers.GetCurrencyById)

	//tax rates
	taxRates := v1.Group("/tax_rates")
	taxRates.GET("", controllers.GetAllTaxRates)
	taxRates.GET("/search", controllers.SearchTaxRates)
	taxRates.GET("/:id", controllers.GetTaxRateById)

	//countries
	countries := v1.Group("/countries")
	countries.GET("", controllers.GetAllCountries)

	//settings
	settings := v1.Group("/settings")
	settings.GET("", controllers.GetAllowedSettings)
	settings.GET("/:key", controllers.GetSettingByKey)
	settings.GET("/:key/value", controllers.GetSettingValue)

	//dashboard
	dashboard := v1.Group("/dashboard")
	dashboard.GET("/dashboard_stats", controllers.GetDashboardStats)

	// Reports routes
	reports := v1.Group("/reports")
	{
		// Report management
		reports.GET("", controllers.GetReportsList)
		reports.GET("/:id", controllers.GetReportDetails)

		// Report generation and export
		reports.POST("/:id/generate", controllers.GenerateReport)
		reports.POST("/:id/export", controllers.ExportReport)

		// Scheduled reports
		reports.GET("/scheduled", controllers.GetScheduledReports)
		reports.POST("/scheduled", controllers.CreateScheduledReport)
		reports.PUT("/scheduled/:id", controllers.UpdateScheduledReport)
		reports.DELETE("/scheduled/:id", controllers.DeleteScheduledReport)
	}
}
