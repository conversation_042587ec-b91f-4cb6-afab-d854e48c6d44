package controllers

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

type UpdateClientDeviceRequest struct {
	AssetType           string           `json:"asset_type" binding:"required"`
	DriverId            *uint            `json:"driver_id,omitempty"`
	FleetId             *uint            `json:"fleet_id,omitempty"`
	Name                *string          `json:"name,omitempty"`
	PlateNumber         *string          `json:"plate_number,omitempty"`
	MaxSpeed            *float64         `json:"max_speed,omitempty"`
	MaxSpeedDuration    *int             `json:"max_speed_duration,omitempty"`
	Icon                *string          `json:"icon,omitempty"`
	Model               *string          `json:"model,omitempty"`
	Make                *string          `json:"make,omitempty"`
	ManufactureYear     *string          `json:"manufacture_year,omitempty"`
	Color               *string          `json:"color,omitempty"`
	EngineNumber        *string          `json:"engine_number,omitempty"`
	ChassisNumber       *string          `json:"chassis_number,omitempty"`
	Vin                 *string          `json:"vin,omitempty"`
	FuelType            *string          `json:"fuel_type,omitempty"`
	FuelRate            *float64         `json:"fuel_rate,omitempty"`
	FuelRateUnit        *string          `json:"fuel_rate_unit,omitempty"`
	Manufacturer        *string          `json:"manufacturer,omitempty"`
	Status              string           `json:"status,omitempty"`
	InsuranceExpiryDate *time.Time       `json:"insurance_expiry_date,omitempty" gorm:"type:date"`
	Mileage             *float64         `json:"mileage,omitempty"`
	ServiceMileage      *float64         `json:"service_mileage,omitempty"`
	LastServiceDate     *time.Time       `json:"last_service_date,omitempty" gorm:"type:date"`
	NextServiceDate     *time.Time       `json:"next_service_date,omitempty" gorm:"type:date"`
	LastServiceMileage  *float64         `json:"last_service_mileage,omitempty"`
	NextServiceMileage  *float64         `json:"next_service_mileage,omitempty"`
	Emails              *json.RawMessage `json:"emails,omitempty"`
	PhoneNumbers        *json.RawMessage `json:"phone_numbers,omitempty"`
	Description         *string          `json:"description,omitempty"`
}

func GetAllClientDevices(c *gin.Context) {
	var total int64

	filter := map[string]interface{}{}
	clientId, _ := c.Get("client_id")
	filter["client_id"] = clientId

	if fleetId := c.Query("fleet_id"); fleetId != "" {
		filter["fleet_id"] = fleetId
	}
	if deviceTypeId := c.Query("device_type_id"); deviceTypeId != "" {
		filter["device_type_id"] = deviceTypeId
	}

	// Use the efficient query helper
	preloads := []string{"DeviceType", "Fleet"}
	pagination := c.Query("no_pagination") == ""

	clientDevices, err := utils.GetClientDevicesWithLastGPS(filter, preloads, pagination, c)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to retrieve client devices",
			"error":   err.Error(),
		})
		return
	}

	// Get total count
	config.DB.Model(&models.ClientDevice{}).Where(filter).Count(&total)

	c.JSON(http.StatusOK, gin.H{
		"data":         clientDevices,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}
func GetClientDeviceById(c *gin.Context) {
	// Use the efficient query helper
	preloads := []string{"DeviceType", "DeviceType.Protocol", "Client", "Fleet"}

	clientDevice, err := utils.GetClientDeviceByIdWithLastGPS(c.Param("id"), preloads)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client device not found",
		})
		return
	}

	// Also get the complete last GPS data record for more detailed information
	var lastGPSData models.GPSData
	config.DB.Where("client_device_id = ?", clientDevice.Id).Order("gps_timestamp DESC").First(&lastGPSData)

	c.JSON(http.StatusOK, gin.H{
		"data":         clientDevice,
		"last_gps_data": lastGPSData,
	})
}
func UpdateClientDevice(c *gin.Context) {
	var req UpdateClientDeviceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	clientId, _ := c.Get("client_id")
	var clientDevice models.ClientDevice
	if err := config.DB.Where("client_id = ?", clientId).First(&clientDevice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Device not found",
		})
		return
	}
	clientDevice.Name = req.Name
	clientDevice.AssetType = req.AssetType
	clientDevice.DriverId = req.DriverId
	clientDevice.FleetId = req.FleetId
	clientDevice.PlateNumber = req.PlateNumber
	clientDevice.MaxSpeed = req.MaxSpeed
	clientDevice.Icon = req.Icon
	clientDevice.Model = req.Model
	clientDevice.Make = req.Make
	clientDevice.ManufactureYear = req.ManufactureYear
	clientDevice.Color = req.Color
	clientDevice.EngineNumber = req.EngineNumber
	clientDevice.ChassisNumber = req.ChassisNumber
	clientDevice.Vin = req.Vin
	clientDevice.FuelType = req.FuelType
	clientDevice.FuelRate = req.FuelRate
	clientDevice.FuelRateUnit = req.FuelRateUnit
	clientDevice.Manufacturer = req.Manufacturer
	clientDevice.Status = req.Status
	clientDevice.InsuranceExpiryDate = req.InsuranceExpiryDate
	clientDevice.Mileage = req.Mileage
	clientDevice.ServiceMileage = req.ServiceMileage
	clientDevice.LastServiceDate = req.LastServiceDate
	clientDevice.NextServiceDate = req.NextServiceDate
	clientDevice.LastServiceMileage = req.LastServiceMileage
	clientDevice.NextServiceMileage = req.NextServiceMileage
	clientDevice.Emails = req.Emails
	clientDevice.PhoneNumbers = req.PhoneNumbers
	clientDevice.Description = req.Description
	result := config.DB.Save(&clientDevice)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Device updated successfully"})
}
func SearchClientDevices(c *gin.Context) {
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}

	if fleetId := c.Query("fleet_id"); fleetId != "" {
		filter["fleet_id"] = fleetId
	}
	filter["client_id"] = clientId
	if deviceTypeId := c.Query("device_type_id"); deviceTypeId != "" {
		filter["device_type_id"] = deviceTypeId
	}

	// Use the efficient query helper
	preloads := []string{"DeviceType", "Client", "Fleet"}
	searchTerm := c.Query("s")

	clientDevices, err := utils.SearchClientDevicesWithLastGPS(filter, searchTerm, preloads)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to search client devices",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": clientDevices,
	})
}
