package controllers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/seed"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGetAllClientDevicesWithLastGPS(t *testing.T) {
	// Setup test database
	config.InitTestDB()
	seed.Seed()

	// Create test client
	client := models.Client{
		Name:  "Test Client",
		Email: "<EMAIL>",
	}
	config.DB.Create(&client)

	// Create test device type
	deviceType := models.DeviceType{
		Name: "Test Device Type",
	}
	config.DB.Create(&deviceType)

	// Create test client device
	clientDevice := models.ClientDevice{
		Name:         &[]string{"Test Device"}[0],
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "TEST001",
		AssetType:    "vehicle",
		Status:       "active",
	}
	config.DB.Create(&clientDevice)

	// Create test GPS data
	gpsData := models.GPSData{
		ClientDeviceId: &clientDevice.Id,
		DeviceId:       "TEST001",
		Latitude:       -17.8252,
		Longitude:      31.0335,
		Speed:          &[]float64{45.5}[0],
		GPSTimestamp:   &[]time.Time{time.Now()}[0],
		LocationName:   &[]string{"Test Location"}[0],
		IgnitionStatus: &[]bool{true}[0],
		VehicleStatus:  &[]string{"moving"}[0],
	}
	config.DB.Create(&gpsData)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	// Add middleware to set client_id
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
		c.Next()
	})
	
	router.GET("/client-devices", GetAllClientDevices)

	// Create test request
	req, _ := http.NewRequest("GET", "/client-devices", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check that data is returned
	data, exists := response["data"]
	assert.True(t, exists)

	devices, ok := data.([]interface{})
	assert.True(t, ok)
	assert.Greater(t, len(devices), 0)

	// Check that the first device has last GPS data
	if len(devices) > 0 {
		device := devices[0].(map[string]interface{})
		
		// Check for last GPS data fields
		assert.Contains(t, device, "last_latitude")
		assert.Contains(t, device, "last_longitude")
		assert.Contains(t, device, "last_gps_timestamp")
		assert.Contains(t, device, "last_speed")
		assert.Contains(t, device, "last_location_name")
		assert.Contains(t, device, "last_ignition_status")
		assert.Contains(t, device, "last_vehicle_status")

		// Verify the GPS data values
		if device["last_latitude"] != nil {
			assert.Equal(t, -17.8252, device["last_latitude"])
		}
		if device["last_longitude"] != nil {
			assert.Equal(t, 31.0335, device["last_longitude"])
		}
		if device["last_speed"] != nil {
			assert.Equal(t, 45.5, device["last_speed"])
		}
		if device["last_location_name"] != nil {
			assert.Equal(t, "Test Location", device["last_location_name"])
		}
		if device["last_ignition_status"] != nil {
			assert.Equal(t, true, device["last_ignition_status"])
		}
		if device["last_vehicle_status"] != nil {
			assert.Equal(t, "moving", device["last_vehicle_status"])
		}
	}

	// Cleanup
	config.DB.Delete(&gpsData)
	config.DB.Delete(&clientDevice)
	config.DB.Delete(&deviceType)
	config.DB.Delete(&client)
}

func TestGetClientDeviceByIdWithLastGPS(t *testing.T) {
	// Setup test database
	config.InitTestDB()
	seed.Seed()

	// Create test client
	client := models.Client{
		Name:  "Test Client",
		Email: "<EMAIL>",
	}
	config.DB.Create(&client)

	// Create test device type
	deviceType := models.DeviceType{
		Name: "Test Device Type 2",
	}
	config.DB.Create(&deviceType)

	// Create test client device
	clientDevice := models.ClientDevice{
		Name:         &[]string{"Test Device 2"}[0],
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "TEST002",
		AssetType:    "vehicle",
		Status:       "active",
	}
	config.DB.Create(&clientDevice)

	// Create test GPS data
	gpsData := models.GPSData{
		ClientDeviceId: &clientDevice.Id,
		DeviceId:       "TEST002",
		Latitude:       -17.8300,
		Longitude:      31.0400,
		Speed:          &[]float64{60.0}[0],
		GPSTimestamp:   &[]time.Time{time.Now()}[0],
		LocationName:   &[]string{"Test Location 2"}[0],
		IgnitionStatus: &[]bool{false}[0],
		VehicleStatus:  &[]string{"parked"}[0],
	}
	config.DB.Create(&gpsData)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/client-devices/:id", GetClientDeviceById)

	// Create test request
	req, _ := http.NewRequest("GET", "/client-devices/"+string(rune(clientDevice.Id)), nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check that data is returned
	data, exists := response["data"]
	assert.True(t, exists)

	// Check that last_gps_data is returned
	lastGPSData, exists := response["last_gps_data"]
	assert.True(t, exists)
	assert.NotNil(t, lastGPSData)

	device := data.(map[string]interface{})
	
	// Check for last GPS data fields in the device object
	assert.Contains(t, device, "last_latitude")
	assert.Contains(t, device, "last_longitude")
	assert.Contains(t, device, "last_gps_timestamp")
	assert.Contains(t, device, "last_speed")
	assert.Contains(t, device, "last_location_name")
	assert.Contains(t, device, "last_ignition_status")
	assert.Contains(t, device, "last_vehicle_status")

	// Cleanup
	config.DB.Delete(&gpsData)
	config.DB.Delete(&clientDevice)
	config.DB.Delete(&deviceType)
	config.DB.Delete(&client)
}
