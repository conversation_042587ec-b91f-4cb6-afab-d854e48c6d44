package controllers

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"log"
	"net/http"
	"strings"
	"time"
	"yotracker/internal/models"
)

// WebSocket upgrader
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

type Hub struct {
	clients    map[*Client]bool
	channels   map[string]map[*Client]bool
	register   chan *Client
	unregister chan *Client
	broadcast  chan Message
}

type Client struct {
	hub     *Hub
	conn    *websocket.Conn
	send    chan []byte
	channel string
}
type Message struct {
	Channel string `json:"channel"`
	Data    string `json:"data"`
}

func HandleWebsocketRequest(hub *Hub, c *gin.Context) {
	token := strings.ReplaceAll(c.<PERSON>eader("Authorization"), "Bearer ", "")
	fmt.Println(token)
	// Upgrade the connection to WebSocket
	conn, err := upgrader.Upgrade(c.<PERSON>, c.Request, nil)
	if err != nil {
		log.Printf("Upgrade error: %v", err)
		return
	}
	channel := c.Param("channel")
	client := &Client{hub: hub, conn: conn, send: make(chan []byte, 256), channel: channel}
	client.hub.register <- client

	go client.writePump()
	go client.readPump()

	// Listen and send GPS data
	for {
		var gpsData models.GPSData
		// Simulate receiving GPS data
		gpsData = models.GPSData{
			Latitude:  37.7749,
			Longitude: -122.4194,
			CreatedAt: time.Now(),
		}
		err = conn.WriteJSON(gpsData)
		if err != nil {
			log.Printf("Write error: %v", err)
			break
		}
		time.Sleep(2 * time.Second) // Simulate delay between updates
	}
}
func NewHub() *Hub {
	return &Hub{
		clients:    make(map[*Client]bool),
		channels:   make(map[string]map[*Client]bool),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		broadcast:  make(chan Message),
	}
}

func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.clients[client] = true
			if _, ok := h.channels[client.channel]; !ok {
				h.channels[client.channel] = make(map[*Client]bool)
			}
			h.channels[client.channel][client] = true

		case client := <-h.unregister:
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
				if _, ok := h.channels[client.channel][client]; ok {
					delete(h.channels[client.channel], client)
					if len(h.channels[client.channel]) == 0 {
						delete(h.channels, client.channel)
					}
				}
			}

		case message := <-h.broadcast:
			if clients, ok := h.channels[message.Channel]; ok {
				for client := range clients {
					select {
					case client.send <- []byte(message.Data):
					default:
						close(client.send)
						delete(clients, client)
					}
				}
			}
		}
	}
}
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()
	for {
		var msg Message
		err := c.conn.ReadJSON(&msg)
		if err != nil {
			log.Println("Error reading JSON:", err)
			break
		}
		c.hub.broadcast <- msg
	}
}

func (c *Client) writePump() {
	for {
		select {
		case message, ok := <-c.send:
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}
			c.conn.WriteMessage(websocket.TextMessage, message)
		}
	}
}
