package controllers

import (
	"bytes"
	"net/http"
	"strconv"
	"yotracker/config"
	"yotracker/internal/mail"
	"yotracker/internal/models"
	"yotracker/internal/templates"
	"yotracker/internal/utils"

	"github.com/SebastiaanKlippert/go-wkhtmltopdf"
	"github.com/gin-gonic/gin"
)

func GetAllInvoices(c *gin.Context) {
	var invoices []models.Invoice
	var total int64
	filter := map[string]interface{}{}
	clientId, _ := c.Get("client_id")
	filter["client_id"] = clientId
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}
	if paymentTypeId := c.Query("payment_type_id"); paymentTypeId != "" {
		filter["payment_type_id"] = paymentTypeId
	}
	if currencyId := c.Query("currency_id"); currencyId != "" {
		filter["currency_id"] = currencyId
	}

	config.DB.Scopes(utils.Paginate(c)).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Preload("Currency").Where(filter).Order("id desc").Find(&invoices)
	config.DB.Model(&models.Invoice{}).Where(filter).Count(&total)
	// Extract current_page and per_page from query params (same logic as utils.Paginate)
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}
	perPage, _ := strconv.Atoi(c.Query("per_page"))
	switch {
	case perPage > 100:
		perPage = 100
	case perPage <= 0:
		perPage = 10
	}
	c.JSON(http.StatusOK, gin.H{
		"data":         invoices,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
	})
}

func GetInvoiceById(c *gin.Context) {
	var invoice models.Invoice
	clientId, _ := c.Get("client_id")
	if err := config.DB.Where("client_id = ?", clientId).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Preload("InvoicePayments.PaymentType").Preload("InvoicePayments.Currency").Preload("Currency").Preload("PaymentType").First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": invoice,
	})
}

func SearchInvoices(c *gin.Context) {
	var invoices []models.Invoice
	filter := map[string]interface{}{}
	clientId, _ := c.Get("client_id")
	filter["client_id"] = clientId
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}
	if paymentTypeId := c.Query("payment_type_id"); paymentTypeId != "" {
		filter["payment_type_id"] = paymentTypeId
	}
	if currencyId := c.Query("currency_id"); currencyId != "" {
		filter["currency_id"] = currencyId
	}
	if isSubscription := c.Query("is_subscription"); isSubscription != "" {
		filter["is_subscription"] = isSubscription
	}

	if reference := c.Query("reference"); reference != "" {
		config.DB.Where(filter).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Preload("Currency").Where("reference LIKE ?", "%"+reference+"%").Order("id desc").Find(&invoices)
	} else {
		config.DB.Where(filter).Preload("Client").Preload("InvoiceItems").Preload("InvoicePayments").Preload("Currency").Order("id desc").Find(&invoices)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": invoices,
	})
}
func UpdateInvoiceStatus(id uint) error {
	var invoice models.Invoice
	if err := config.DB.First(&invoice, id).Error; err != nil {
		return err
	}
	var totalPayments float64
	config.DB.Model(&models.InvoicePayment{}).Where("invoice_id = ?", invoice.Id).Select("COALESCE(SUM(amount), 0)").Scan(&totalPayments)
	balance := *invoice.Amount - totalPayments
	invoice.Balance = &balance
	if balance == 0 {
		invoice.Status = "paid"
	} else if balance < 0 {
		invoice.Status = "overpaid"
	} else if balance > 0 {
		invoice.Status = "partial"
	}
	result := config.DB.Save(&invoice)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func GenerateInvoicePDF(c *gin.Context) {
	var invoice models.Invoice
	clientId, _ := c.Get("client_id")
	if err := config.DB.Where("client_id = ?", clientId).Preload("Client").Preload("Client.Country").Preload("InvoiceItems").Preload("InvoicePayments").Preload("InvoicePayments.PaymentType").Preload("Currency").Preload("PaymentType").First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}
	tmpl, err := templates.GetInvoiceTemplate()
	if err != nil {
		c.String(http.StatusInternalServerError, "Template error: %v", err)
		return
	}

	var htmlBuffer bytes.Buffer
	invoiceView := models.ToInvoiceView(invoice)
	err = tmpl.Execute(&htmlBuffer, invoiceView)
	if err != nil {
		c.String(http.StatusInternalServerError, "Template render error: %v", err)
		return
	}

	// Step 2: Generate PDF from HTML using wkhtmltopdf
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		c.String(http.StatusInternalServerError, "PDF generator error: %v", err)
		return
	}

	page := wkhtmltopdf.NewPageReader(&htmlBuffer)
	pdfg.AddPage(page)

	err = pdfg.Create()
	if err != nil {
		c.String(http.StatusInternalServerError, "PDF creation failed: %v", err)
		return
	}

	// Step 3: Serve the PDF
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", "attachment; filename=invoice.pdf")
	c.Data(http.StatusOK, "application/pdf", pdfg.Bytes())
}

func SendInvoiceEmail(c *gin.Context) {
	var invoice models.Invoice
	clientId, _ := c.Get("client_id")

	// Fetch the invoice with all necessary relationships
	if err := config.DB.Where("client_id = ?", clientId).Preload("Client").Preload("Client.Country").Preload("InvoiceItems").Preload("InvoicePayments").Preload("InvoicePayments.PaymentType").Preload("Currency").Preload("PaymentType").First(&invoice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}

	// Generate PDF content (reusing logic from GenerateInvoicePDF)
	tmpl, err := templates.GetInvoiceTemplate()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Template error: " + err.Error(),
		})
		return
	}

	var htmlBuffer bytes.Buffer
	invoiceView := models.ToInvoiceView(invoice)
	err = tmpl.Execute(&htmlBuffer, invoiceView)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Template render error: " + err.Error(),
		})
		return
	}

	// Generate PDF from HTML using wkhtmltopdf
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "PDF generator error: " + err.Error(),
		})
		return
	}

	page := wkhtmltopdf.NewPageReader(&htmlBuffer)
	pdfg.AddPage(page)

	err = pdfg.Create()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "PDF creation failed: " + err.Error(),
		})
		return
	}

	// Send email with PDF attachment
	err = mail.SendInvoiceEmail(&invoice, pdfg.Bytes())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to send email: " + err.Error(),
		})
		return
	}

	// Get invoice reference for response
	invoiceReference := "Unknown"
	if invoice.Reference != nil {
		invoiceReference = *invoice.Reference
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Invoice email sent successfully",
		"data": gin.H{
			"invoice_id":   invoice.Id,
			"reference":    invoiceReference,
			"client_email": invoice.Client.Email,
		},
	})
}
