package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func setupPasswordResetTest() {
	utils.ForceProjectRoot()

	// Set required environment variables for testing
	os.Setenv("APP_KEY", "test-secret-key")
	os.Setenv("APP_URL", "http://localhost:3000")
	os.Setenv("SENDGRID_API_KEY", "test-key")
	os.Setenv("FROM_EMAIL", "<EMAIL>")
	os.Setenv("FROM_NAME", "Test")
	os.Setenv("TESTING_DB_NAME", "testing")

	// Only set database connection variables if they're not already set (for CI compatibility)
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}

	config.InitTestDB()
	migrations.Migrate()
	gin.SetMode(gin.TestMode)
}

func createTestClientAndUser() (models.Client, models.User) {
	// Clean up any existing test data in proper order (child records first)
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	// Find and delete any client_devices associated with the test client first
	var existingClient models.Client
	if err := config.DB.Where("name = ?", "Test Client").First(&existingClient).Error; err == nil {
		// Delete client_devices first to avoid foreign key constraint
		config.DB.Where("client_id = ?", existingClient.Id).Delete(&models.ClientDevice{})
		// Now delete the client
		config.DB.Where("id = ?", existingClient.Id).Delete(&models.Client{})
	}

	// Create test client
	client := models.Client{
		Name:   "Test Client",
		Status: "active",
	}
	config.DB.Create(&client)

	// Create test user
	hashedPassword := service.HashPassword("password123")
	user := models.User{
		Name:     "Test Client User",
		Email:    "<EMAIL>",
		Password: hashedPassword,
		UserType: "client",
		ClientId: &client.Id,
	}
	config.DB.Create(&user)

	return client, user
}

func TestRequestPasswordResetFrontend(t *testing.T) {
	setupPasswordResetTest()
	client, user := createTestClientAndUser()
	defer func() {
		config.DB.Where("email = ?", user.Email).Delete(&models.User{})
		// Delete any client_devices first to avoid foreign key constraint
		config.DB.Where("client_id = ?", client.Id).Delete(&models.ClientDevice{})
		config.DB.Where("id = ?", client.Id).Delete(&models.Client{})
	}()

	router := gin.New()
	router.POST("/password-reset/request", RequestPasswordReset)

	t.Run("Valid email with active client", func(t *testing.T) {
		reqBody := PasswordResetRequest{
			Email: user.Email,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password-reset/request", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Contains(t, response["message"], "password reset link has been sent")
	})

	t.Run("Valid email with inactive client", func(t *testing.T) {
		// Update client status to inactive
		config.DB.Model(&client).Update("status", "inactive")

		reqBody := PasswordResetRequest{
			Email: user.Email,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password-reset/request", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should still return 200 for security (don't reveal client status)
		assert.Equal(t, http.StatusOK, w.Code)

		// Restore client status for other tests
		config.DB.Model(&client).Update("status", "active")
	})

	t.Run("Invalid email", func(t *testing.T) {
		reqBody := PasswordResetRequest{
			Email: "<EMAIL>",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password-reset/request", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should still return 200 for security
		assert.Equal(t, http.StatusOK, w.Code)
	})
}

func TestVerifyPasswordResetTokenFrontend(t *testing.T) {
	setupPasswordResetTest()
	client, user := createTestClientAndUser()
	defer func() {
		config.DB.Where("email = ?", user.Email).Delete(&models.User{})
		// Delete any client_devices first to avoid foreign key constraint
		config.DB.Where("client_id = ?", client.Id).Delete(&models.ClientDevice{})
		config.DB.Where("id = ?", client.Id).Delete(&models.Client{})
	}()

	router := gin.New()
	router.POST("/password-reset/verify", VerifyPasswordResetToken)

	t.Run("Valid token with active client", func(t *testing.T) {
		// Generate a valid reset token
		resetToken, err := service.GenerateToken(&user, "password_reset")
		assert.NoError(t, err)

		reqBody := PasswordResetVerify{
			Token: resetToken,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password-reset/verify", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Token is valid", response["message"])
		assert.Equal(t, user.Email, response["email"])
	})

	t.Run("Valid token with inactive client", func(t *testing.T) {
		// Update client status to inactive
		config.DB.Model(&client).Update("status", "inactive")

		resetToken, err := service.GenerateToken(&user, "password_reset")
		assert.NoError(t, err)

		reqBody := PasswordResetVerify{
			Token: resetToken,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password-reset/verify", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Restore client status
		config.DB.Model(&client).Update("status", "active")
	})

	t.Run("Invalid token", func(t *testing.T) {
		reqBody := PasswordResetVerify{
			Token: "invalid.token.here",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password-reset/verify", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestResetPasswordFrontend(t *testing.T) {
	setupPasswordResetTest()
	client, user := createTestClientAndUser()
	defer func() {
		config.DB.Where("email = ?", user.Email).Delete(&models.User{})
		// Delete any client_devices first to avoid foreign key constraint
		config.DB.Where("client_id = ?", client.Id).Delete(&models.ClientDevice{})
		config.DB.Where("id = ?", client.Id).Delete(&models.Client{})
	}()

	router := gin.New()
	router.POST("/password-reset/confirm", ResetPassword)

	t.Run("Valid password reset with active client", func(t *testing.T) {
		// Generate a valid reset token
		resetToken, err := service.GenerateToken(&user, "password_reset")
		assert.NoError(t, err)

		newPassword := "newpassword123"
		reqBody := PasswordResetConfirm{
			Token:    resetToken,
			Password: newPassword,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password-reset/confirm", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Password has been reset successfully", response["message"])

		// Verify password was actually changed
		var updatedUser models.User
		config.DB.First(&updatedUser, user.Id)
		assert.True(t, service.CheckPassword(updatedUser.Password, newPassword))
	})

	t.Run("Valid token with inactive client", func(t *testing.T) {
		// Update client status to inactive
		config.DB.Model(&client).Update("status", "inactive")

		resetToken, err := service.GenerateToken(&user, "password_reset")
		assert.NoError(t, err)

		reqBody := PasswordResetConfirm{
			Token:    resetToken,
			Password: "newpassword123",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password-reset/confirm", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Restore client status
		config.DB.Model(&client).Update("status", "active")
	})

	t.Run("Invalid token", func(t *testing.T) {
		reqBody := PasswordResetConfirm{
			Token:    "invalid.token.here",
			Password: "newpassword123",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password-reset/confirm", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
