package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/internal/models"
)

// GetAllowedSettings returns all settings that are allowed to be retrieved by frontend users
func GetAllowedSettings(c *gin.Context) {
	allowedSettings := models.GetAllowedSettings()

	var settings []models.Setting
	for _, settingKey := range allowedSettings {
		setting := models.GetSettingByKey(settingKey)
		if setting.Id != 0 { // Only include if setting exists
			settings = append(settings, setting)
		}
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
		"data": settings,
	})
}

// GetSettingByKey returns a specific setting value if it's in the allowed list
func GetSettingByKey(c *gin.Context) {
	settingKey := c.Param("key")

	// Check if the setting is allowed to be retrieved
	if !models.IsSettingAllowed(settingKey) {
		c.JSON(http.StatusForbidden, gin.H{
			"message": "Access to this setting is not allowed",
		})
		return
	}

	setting := models.GetSettingByKey(settingKey)
	if setting.Id == 0 {
		c.JSO<PERSON>(http.StatusNotFound, gin.H{
			"message": "Setting not found",
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"data": setting,
	})
}

// GetSettingValue returns just the value of a specific setting if it's allowed
func GetSettingValue(c *gin.Context) {
	settingKey := c.Param("key")

	// Check if the setting is allowed to be retrieved
	if !models.IsSettingAllowed(settingKey) {
		c.JSON(http.StatusForbidden, gin.H{
			"message": "Access to this setting is not allowed",
		})
		return
	}

	value := models.GetSetting(settingKey)
	if value == "" {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Setting not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"value": value,
	})
}
