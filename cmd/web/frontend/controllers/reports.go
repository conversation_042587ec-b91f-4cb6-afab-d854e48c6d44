package controllers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// GetReportsList returns all available reports organized by category
func GetReportsList(c *gin.Context) {
	var reports []models.Report
	err := config.DB.Where("status = ?", "active").
		Order("category, name").
		Find(&reports).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch reports"})
		return
	}

	// Group reports by category
	reportsByCategory := make(map[string][]models.Report)
	for _, report := range reports {
		reportsByCategory[report.Category] = append(reportsByCategory[report.Category], report)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"reports_by_category": reportsByCategory,
			"total_reports":       len(reports),
		},
	})
}

// GetReportDetails returns details of a specific report
func GetReportDetails(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	var report models.Report
	err = config.DB.Session(&gorm.Session{Logger: config.DB.Logger.LogMode(logger.Silent)}).
		First(&report, uint(reportID)).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    report,
	})
}

// GenerateReport generates and returns report data
func GenerateReport(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	// Parse filters from request
	var filters models.ReportFilters
	if err := c.ShouldBindJSON(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid filter parameters"})
		return
	}

	// Set default date range if not provided (last 30 days)
	if filters.StartDate == nil || filters.EndDate == nil {
		endDate := time.Now()
		startDate := endDate.AddDate(0, 0, -30)
		if filters.StartDate == nil {
			filters.StartDate = &startDate
		}
		if filters.EndDate == nil {
			filters.EndDate = &endDate
		}
	}

	// Set default pagination
	if filters.PerPage == 0 {
		filters.PerPage = 100
	}

	// Generate report
	reportService := service.NewReportService()
	reportData, err := reportService.GenerateReport(uint(reportID), filters, "json")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate report: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    reportData,
	})
}

// ExportReport exports report to PDF or Excel
func ExportReport(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	format := c.Query("format")
	if format == "" {
		format = "pdf"
	}

	// Validate format
	if format != "pdf" && format != "excel" && format != "csv" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid format. Supported: pdf, excel, csv"})
		return
	}

	// Parse filters from request body
	var filters models.ReportFilters
	if err := c.ShouldBindJSON(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid filter parameters"})
		return
	}

	// Set default date range if not provided
	if filters.StartDate == nil || filters.EndDate == nil {
		endDate := time.Now()
		startDate := endDate.AddDate(0, 0, -30)
		if filters.StartDate == nil {
			filters.StartDate = &startDate
		}
		if filters.EndDate == nil {
			filters.EndDate = &endDate
		}
	}

	// Generate report data
	reportService := service.NewReportService()
	reportData, err := reportService.GenerateReport(uint(reportID), filters, "json")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate report: %v", err)})
		return
	}

	// Export report using export service
	exportService := service.NewReportExportService()
	exportData, contentType, err := exportService.ExportReport(reportData, format)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to export report: %v", err)})
		return
	}

	// Generate filename
	timestamp := time.Now().Format("2006-01-02_15-04-05")
	filename := fmt.Sprintf("%s_%s.%s",
		sanitizeFilename(reportData.ReportInfo.Name), timestamp, format)

	// Set appropriate headers and return file
	c.Header("Content-Type", contentType)
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Length", strconv.Itoa(len(exportData)))

	c.Data(http.StatusOK, contentType, exportData)
}

// GetScheduledReports returns all scheduled reports for the user
func GetScheduledReports(c *gin.Context) {
	var scheduledReports []models.ScheduledReport
	err := config.DB.Preload("Report").
		Where("status != ?", "inactive").
		Order("created_at DESC").
		Find(&scheduledReports).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch scheduled reports"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    scheduledReports,
	})
}

// CreateScheduledReport creates a new scheduled report
func CreateScheduledReport(c *gin.Context) {
	var request models.CreateScheduledReportRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Validate report exists
	var report models.Report
	err := config.DB.First(&report, request.ReportId).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
		return
	}

	// Calculate next run time based on frequency
	nextRunAt := calculateNextRunTime(request.Frequency, request.CronPattern, request.Timezone)

	// Create scheduled report
	scheduledReport := models.ScheduledReport{
		ReportId:    request.ReportId,
		Name:        request.Name,
		Description: request.Description,
		Frequency:   request.Frequency,
		CronPattern: request.CronPattern,
		Timezone:    request.Timezone,
		Filters:     request.Filters,
		Recipients:  request.Recipients,
		Format:      request.Format,
		NextRunAt:   &nextRunAt,
		Status:      models.ScheduledReportStatusActive,
		CreatedBy:   1, // TODO: Get from authenticated user
	}

	err = config.DB.Create(&scheduledReport).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create scheduled report"})
		return
	}

	// Load the report relationship
	config.DB.Preload("Report").First(&scheduledReport, scheduledReport.Id)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    scheduledReport,
		"message": "Scheduled report created successfully",
	})
}

// UpdateScheduledReport updates an existing scheduled report
func UpdateScheduledReport(c *gin.Context) {
	scheduledReportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scheduled report ID"})
		return
	}

	var scheduledReport models.ScheduledReport
	err = config.DB.First(&scheduledReport, uint(scheduledReportID)).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Scheduled report not found"})
		return
	}

	var request models.CreateScheduledReportRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Update fields
	scheduledReport.Name = request.Name
	scheduledReport.Description = request.Description
	scheduledReport.Frequency = request.Frequency
	scheduledReport.CronPattern = request.CronPattern
	scheduledReport.Timezone = request.Timezone
	scheduledReport.Filters = request.Filters
	scheduledReport.Recipients = request.Recipients
	scheduledReport.Format = request.Format

	// Recalculate next run time
	nextRunAt := calculateNextRunTime(request.Frequency, request.CronPattern, request.Timezone)
	scheduledReport.NextRunAt = &nextRunAt

	err = config.DB.Save(&scheduledReport).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update scheduled report"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    scheduledReport,
		"message": "Scheduled report updated successfully",
	})
}

// DeleteScheduledReport deletes a scheduled report
func DeleteScheduledReport(c *gin.Context) {
	scheduledReportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scheduled report ID"})
		return
	}

	err = config.DB.Delete(&models.ScheduledReport{}, uint(scheduledReportID)).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete scheduled report"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Scheduled report deleted successfully",
	})
}

// Helper functions
func sanitizeFilename(filename string) string {
	// Remove special characters and replace spaces with underscores
	result := ""
	for _, char := range filename {
		if (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') || (char >= '0' && char <= '9') || char == '_' || char == '-' {
			result += string(char)
		} else if char == ' ' {
			result += "_"
		}
	}
	return result
}

func calculateNextRunTime(frequency, cronPattern, timezone string) time.Time {
	now := time.Now()

	switch frequency {
	case models.ReportFrequencyDaily:
		return now.AddDate(0, 0, 1)
	case models.ReportFrequencyWeekly:
		return now.AddDate(0, 0, 7)
	case models.ReportFrequencyMonthly:
		return now.AddDate(0, 1, 0)
	case models.ReportFrequencyCustom:
		// TODO: Implement cron pattern parsing
		return now.AddDate(0, 0, 1) // Default to daily
	default:
		return now.AddDate(0, 0, 1)
	}
}
