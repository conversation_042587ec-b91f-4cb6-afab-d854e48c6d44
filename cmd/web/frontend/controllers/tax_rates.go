package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
)

func GetAllTaxRates(c *gin.Context) {
	var taxRates []models.TaxRate
	filter := map[string]interface{}{}

	// Optional filtering by active status
	if active := c.Query("active"); active != "" {
		if active == "true" {
			filter["active"] = true
		} else if active == "false" {
			filter["active"] = false
		}
	}

	// Optional filtering by type
	if taxType := c.Query("type"); taxType != "" {
		filter["type"] = taxType
	}

	config.DB.Where(filter).Order("name").Find(&taxRates)
	c.JSON(http.StatusOK, gin.H{
		"data": taxRates,
	})
}

func GetTaxRateById(c *gin.Context) {
	var taxRate models.TaxRate
	if err := config.DB.First(&taxRate, c.Param("id")).Error; err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{
			"message": "Tax rate not found",
		})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"data": taxRate,
	})
}

func SearchTaxRates(c *gin.Context) {
	var taxRates []models.TaxRate
	search := c.Query("s")
	filter := map[string]interface{}{}

	// Optional filtering by active status
	if active := c.Query("active"); active != "" {
		if active == "true" {
			filter["active"] = true
		} else if active == "false" {
			filter["active"] = false
		}
	}

	query := config.DB.Where(filter)
	if search != "" {
		query = query.Where("name LIKE ? OR code LIKE ? OR description LIKE ?", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	query.Order("name").Find(&taxRates)
	c.JSON(http.StatusOK, gin.H{
		"data": taxRates,
	})
}
