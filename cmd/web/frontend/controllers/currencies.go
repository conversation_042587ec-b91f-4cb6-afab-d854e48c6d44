package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
)

func GetAllCurrencies(c *gin.Context) {
	var currencies []models.Currency
	filter := map[string]interface{}{}
	if active := c.Query("active"); active != "" {
		filter["active"] = active
	}

	config.DB.Where(filter).Find(&currencies)
	c.<PERSON>(http.StatusOK, gin.H{
		"data": currencies,
	})
}

func GetCurrencyById(c *gin.Context) {
	var currency models.Currency
	if err := config.DB.First(&currency, c.Param("id")).Error; err != nil {
		c.J<PERSON>(http.StatusNotFound, gin.H{
			"message": "Currency not found",
		})
		return
	}
	c.J<PERSON>(http.StatusOK, gin.H{
		"data": currency,
	})
}

func CreateCurrency(c *gin.Context) {
	var req models.CurrencyRequest
	if err := c.<PERSON>d<PERSON>(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"message": err.<PERSON>rror(),
		})
		return
	}
	currency := models.Currency{
		Name:              req.Name,
		Code:              req.Code,
		Symbol:            req.Symbol,
		Decimals:          req.Decimals,
		Xrate:             req.Xrate,
		InternationalCode: req.InternationalCode,
		Active:            req.Active,
		SymbolPosition:    req.SymbolPosition,
	}
	result := config.DB.Create(&currency)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Currency created successfully",
	})
}

func UpdateCurrency(c *gin.Context) {
	var req models.CurrencyRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var currency models.Currency
	if err := config.DB.First(&currency, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Currency not found",
		})
		return
	}
	currency.Name = req.Name
	currency.Code = req.Code
	currency.Symbol = req.Symbol
	currency.Decimals = req.Decimals
	currency.Xrate = req.Xrate
	currency.InternationalCode = req.InternationalCode
	currency.Active = req.Active
	currency.SymbolPosition = req.SymbolPosition
	result := config.DB.Save(&currency)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Currency updated successfully"})
}

func DeleteCurrency(c *gin.Context) {
	var currency models.Currency
	if err := config.DB.First(&currency, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Currency not found",
		})
		return
	}
	result := config.DB.Delete(&currency)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Currency deleted successfully"})
}

func SearchCurrencies(c *gin.Context) {
	var currencies []models.Currency
	config.DB.Find(&currencies, "name like ? OR code like ?", "%"+c.Query("s")+"%", "%"+c.Query("s")+"%")
	c.JSON(http.StatusOK, gin.H{
		"data": currencies,
	})
}
