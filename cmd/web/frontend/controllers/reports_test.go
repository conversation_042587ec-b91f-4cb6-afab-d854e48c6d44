package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/migrations"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type ReportsControllerTestSuite struct {
	suite.Suite
	router     *gin.Engine
	testReport models.Report
}

func (suite *ReportsControllerTestSuite) SetupSuite() {
	// Initialize test database
	config.InitTestDB()
	migrations.Migrate()

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Setup router
	suite.router = gin.New()
	suite.setupRoutes()

	// Create test report (keeping original approach for test isolation)
	suite.createTestReport()
}

func (suite *ReportsControllerTestSuite) TearDownSuite() {
	// Clean up test data
	config.DB.Where("name = ?", "Test API Report").Delete(&models.Report{})
}

func (suite *ReportsControllerTestSuite) setupRoutes() {
	api := suite.router.Group("/api/v1")
	reports := api.Group("/reports")
	{
		reports.GET("", GetReportsList)
		reports.GET("/:id", GetReportDetails)
		reports.POST("/:id/generate", GenerateReport)
		reports.POST("/:id/export", ExportReport)
		reports.GET("/scheduled", GetScheduledReports)
		reports.POST("/scheduled", CreateScheduledReport)
		reports.PUT("/scheduled/:id", UpdateScheduledReport)
		reports.DELETE("/scheduled/:id", DeleteScheduledReport)
	}
}

func (suite *ReportsControllerTestSuite) createTestReport() {
	suite.testReport = models.Report{
		Name:        "Test API Report",
		Description: "Test report for API testing",
		Category:    "Detail",
		ReportType:  "position_log",
		Status:      "active",
	}
	config.DB.Create(&suite.testReport)
}

// Test GET /reports
func (suite *ReportsControllerTestSuite) TestGetReportsList() {
	req, _ := http.NewRequest("GET", "/api/v1/reports", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.True(suite.T(), response["success"].(bool))
	assert.Contains(suite.T(), response, "data")

	data := response["data"].(map[string]interface{})
	assert.Contains(suite.T(), data, "reports_by_category")
	assert.Contains(suite.T(), data, "total_reports")

	totalReports := data["total_reports"].(float64)
	assert.Greater(suite.T(), totalReports, 0.0)
}

// Test GET /reports/:id
func (suite *ReportsControllerTestSuite) TestGetReportDetails() {
	url := fmt.Sprintf("/api/v1/reports/%d", suite.testReport.Id)
	req, _ := http.NewRequest("GET", url, nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.True(suite.T(), response["success"].(bool))
	assert.Contains(suite.T(), response, "data")

	reportData := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "Test API Report", reportData["name"])
	assert.Equal(suite.T(), "Detail", reportData["category"])
}

// Test POST /reports/:id/generate
func (suite *ReportsControllerTestSuite) TestGenerateReport() {
	filters := map[string]interface{}{
		"start_date": time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		"end_date":   time.Now().Format(time.RFC3339),
		"per_page":   10,
	}

	filtersJSON, _ := json.Marshal(filters)
	url := fmt.Sprintf("/api/v1/reports/%d/generate", suite.testReport.Id)

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(filtersJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.True(suite.T(), response["success"].(bool))
	assert.Contains(suite.T(), response, "data")

	reportData := response["data"].(map[string]interface{})
	assert.Contains(suite.T(), reportData, "report_info")
	assert.Contains(suite.T(), reportData, "metadata")
	assert.Contains(suite.T(), reportData, "data")

	metadata := reportData["metadata"].(map[string]interface{})
	assert.Contains(suite.T(), metadata, "execution_time")
	assert.Contains(suite.T(), metadata, "total_records")
	assert.Contains(suite.T(), metadata, "filtered_records")
}

// Test POST /reports/:id/export
func (suite *ReportsControllerTestSuite) TestExportReport() {
	filters := map[string]interface{}{
		"start_date": time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		"end_date":   time.Now().Format(time.RFC3339),
		"per_page":   10,
	}

	filtersJSON, _ := json.Marshal(filters)
	url := fmt.Sprintf("/api/v1/reports/%d/export?format=pdf", suite.testReport.Id)

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(filtersJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Should return PDF file or error if wkhtmltopdf not available
	if w.Code == http.StatusInternalServerError {
		// Check if it's a wkhtmltopdf error (expected in CI)
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(suite.T(), err)
		if errorMsg, ok := response["error"].(string); ok && strings.Contains(errorMsg, "wkhtmltopdf") {
			suite.T().Log("Skipping PDF test - wkhtmltopdf not available in CI environment")
			return
		}
	}

	assert.Equal(suite.T(), http.StatusOK, w.Code)
	assert.Equal(suite.T(), "application/pdf", w.Header().Get("Content-Type"))
	assert.Contains(suite.T(), w.Header().Get("Content-Disposition"), "attachment")
	assert.Greater(suite.T(), len(w.Body.Bytes()), 1000, "PDF should have substantial content")
}

// Test Excel Export
func (suite *ReportsControllerTestSuite) TestExportReportExcel() {
	filters := map[string]interface{}{
		"start_date": time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		"end_date":   time.Now().Format(time.RFC3339),
		"per_page":   10,
	}

	filtersJSON, _ := json.Marshal(filters)
	url := fmt.Sprintf("/api/v1/reports/%d/export?format=excel", suite.testReport.Id)

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(filtersJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Should return Excel file
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	assert.Equal(suite.T(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", w.Header().Get("Content-Type"))
	assert.Contains(suite.T(), w.Header().Get("Content-Disposition"), "attachment")
	assert.Greater(suite.T(), len(w.Body.Bytes()), 1000, "Excel should have substantial content")
}

// Test CSV Export
func (suite *ReportsControllerTestSuite) TestExportReportCSV() {
	filters := map[string]interface{}{
		"start_date": time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		"end_date":   time.Now().Format(time.RFC3339),
		"per_page":   10,
	}

	filtersJSON, _ := json.Marshal(filters)
	url := fmt.Sprintf("/api/v1/reports/%d/export?format=csv", suite.testReport.Id)

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(filtersJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Should return CSV file
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	assert.Equal(suite.T(), "text/csv", w.Header().Get("Content-Type"))
	assert.Contains(suite.T(), w.Header().Get("Content-Disposition"), "attachment")

	csvContent := w.Body.String()
	assert.Contains(suite.T(), csvContent, "Report,")
	assert.Contains(suite.T(), csvContent, "Description,")
}

// Test GET /reports/scheduled
func (suite *ReportsControllerTestSuite) TestGetScheduledReports() {
	req, _ := http.NewRequest("GET", "/api/v1/reports/scheduled", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.True(suite.T(), response["success"].(bool))
	assert.Contains(suite.T(), response, "data")
}

// Test POST /reports/scheduled
func (suite *ReportsControllerTestSuite) TestCreateScheduledReport() {
	recipients := []map[string]string{
		{"email": "<EMAIL>", "name": "Test User"},
	}

	scheduleRequest := map[string]interface{}{
		"report_id":   suite.testReport.Id,
		"name":        "Test Scheduled Report",
		"description": "Test scheduled report description",
		"frequency":   "daily",
		"timezone":    "UTC",
		"recipients":  recipients,
		"format":      "pdf",
	}

	requestJSON, _ := json.Marshal(scheduleRequest)

	req, _ := http.NewRequest("POST", "/api/v1/reports/scheduled", bytes.NewBuffer(requestJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.True(suite.T(), response["success"].(bool))
	assert.Contains(suite.T(), response, "data")
	assert.Contains(suite.T(), response, "message")

	scheduledReport := response["data"].(map[string]interface{})
	assert.Equal(suite.T(), "Test Scheduled Report", scheduledReport["name"])
	assert.Equal(suite.T(), "daily", scheduledReport["frequency"])

	// Clean up
	if id, ok := scheduledReport["id"].(float64); ok {
		config.DB.Delete(&models.ScheduledReport{}, uint(id))
	}
}

// Test Error Cases
func (suite *ReportsControllerTestSuite) TestErrorCases() {
	// Test invalid report ID
	req, _ := http.NewRequest("GET", "/api/v1/reports/99999", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "error")

	// Test invalid export format
	filters := map[string]interface{}{}
	filtersJSON, _ := json.Marshal(filters)
	url := fmt.Sprintf("/api/v1/reports/%d/export?format=invalid", suite.testReport.Id)

	req, _ = http.NewRequest("POST", url, bytes.NewBuffer(filtersJSON))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "error")
}

// Test Export with Invalid Report ID
func (suite *ReportsControllerTestSuite) TestExportReportInvalidID() {
	filters := map[string]interface{}{
		"start_date": time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		"end_date":   time.Now().Format(time.RFC3339),
	}

	filtersJSON, _ := json.Marshal(filters)
	url := "/api/v1/reports/99999/export?format=pdf"

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(filtersJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "error")
}

// Test Export Performance
func (suite *ReportsControllerTestSuite) TestExportPerformance() {
	filters := map[string]interface{}{
		"start_date": time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		"end_date":   time.Now().Format(time.RFC3339),
		"per_page":   100,
	}

	filtersJSON, _ := json.Marshal(filters)

	// Test CSV export performance (should be fastest)
	start := time.Now()
	url := fmt.Sprintf("/api/v1/reports/%d/export?format=csv", suite.testReport.Id)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(filtersJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	csvDuration := time.Since(start)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
	assert.Less(suite.T(), csvDuration, 5*time.Second, "CSV export should complete within 5 seconds")
}

// Test Export Filename Generation
func (suite *ReportsControllerTestSuite) TestExportFilenameGeneration() {
	filters := map[string]interface{}{
		"start_date": time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		"end_date":   time.Now().Format(time.RFC3339),
	}

	filtersJSON, _ := json.Marshal(filters)
	url := fmt.Sprintf("/api/v1/reports/%d/export?format=csv", suite.testReport.Id)

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(filtersJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	contentDisposition := w.Header().Get("Content-Disposition")
	assert.Contains(suite.T(), contentDisposition, "attachment")
	assert.Contains(suite.T(), contentDisposition, ".csv")
	// The filename format is now "Test_API_Report" instead of "test_api_report"
	assert.Contains(suite.T(), contentDisposition, "Test_API_Report")
}

// Test Input Validation
func (suite *ReportsControllerTestSuite) TestInputValidation() {
	// Test invalid JSON for generate report
	invalidJSON := []byte(`{"invalid": json}`)
	url := fmt.Sprintf("/api/v1/reports/%d/generate", suite.testReport.Id)

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(invalidJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	// Test missing required fields for scheduled report
	invalidSchedule := map[string]interface{}{
		"name": "Test", // Missing required fields
	}

	requestJSON, _ := json.Marshal(invalidSchedule)

	req, _ = http.NewRequest("POST", "/api/v1/reports/scheduled", bytes.NewBuffer(requestJSON))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

// Test Response Format
func (suite *ReportsControllerTestSuite) TestResponseFormat() {
	req, _ := http.NewRequest("GET", "/api/v1/reports", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
	assert.Equal(suite.T(), "application/json; charset=utf-8", w.Header().Get("Content-Type"))

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	// Verify standard response format
	assert.Contains(suite.T(), response, "success")
	assert.Contains(suite.T(), response, "data")
	assert.IsType(suite.T(), true, response["success"])
}

// Test Pagination
func (suite *ReportsControllerTestSuite) TestPagination() {
	filters := map[string]interface{}{
		"start_date": time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
		"end_date":   time.Now().Format(time.RFC3339),
		"per_page":   5,
		"page":       1,
	}

	filtersJSON, _ := json.Marshal(filters)
	url := fmt.Sprintf("/api/v1/reports/%d/generate", suite.testReport.Id)

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(filtersJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	reportData := response["data"].(map[string]interface{})
	metadata := reportData["metadata"].(map[string]interface{})

	// Verify pagination metadata
	assert.Contains(suite.T(), metadata, "total_records")
	assert.Contains(suite.T(), metadata, "filtered_records")
}

// Run the test suite
func TestReportsControllerTestSuite(t *testing.T) {
	suite.Run(t, new(ReportsControllerTestSuite))
}
