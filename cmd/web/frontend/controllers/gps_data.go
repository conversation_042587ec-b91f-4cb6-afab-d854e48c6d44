package controllers

import (
	"fmt"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"

	"github.com/gin-gonic/gin"
)

func SearchGpsData(c *gin.Context) {
	var gpsData []models.GPSData
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}

	config.DB.Preload("ClientDevice").Where(filter).Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = gps_data.client_device_id").Where("gps_timestamp BETWEEN ? AND ?", c.Query("start_date"), c.Query("end_date")).Order("id").Find(&gpsData)
	c.<PERSON>(http.StatusOK, gin.H{
		"data": gpsData,
	})
}
func GetLastLocation(c *gin.Context) {
	var gpsData models.GPSData
	clientId, _ := c.Get("client_id")
	config.DB.Preload("ClientDevice").Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = gps_data.client_device_id").Where("client_device_id = ?", c.Query("client_device_id")).Last(&gpsData)

	// Check if location name is empty and fetch it using Google Geocoding API
	if gpsData.LocationName == nil || *gpsData.LocationName == "" {
		geocodingService := service.NewGoogleGeocodingService()
		err := geocodingService.UpdateGPSDataLocationName(&gpsData)
		if err != nil {
			// Log the error but don't fail the request
			fmt.Printf("Failed to fetch location name for GPS data %d: %v\n", gpsData.Id, err)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": gpsData,
	})
}
func GetGpsDataById(c *gin.Context) {
	var gpsData models.GPSData
	clientId, _ := c.Get("client_id")
	if err := config.DB.Preload("ClientDevice").Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = gps_data.client_device_id").First(&gpsData, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Gps data not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": gpsData,
	})
}
