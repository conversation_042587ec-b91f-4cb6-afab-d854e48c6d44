package main

import (
	"fmt"
	"log"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/migrations"
)

func main() {
	log.Println("Processing August 2025 trip detection...")

	// Initialize database
	config.InitDB()
	migrations.Migrate()

	// Clear any existing trips from August to start fresh
	log.Println("Clearing existing August trips...")
	config.DB.Exec(`
		UPDATE gps_data
		SET trip_id = NULL
		WHERE gps_timestamp >= '2025-08-01' AND gps_timestamp < '2025-09-01'
	`)

	config.DB.Exec(`
		DELETE FROM trips
		WHERE start_time >= '2025-08-01' AND start_time < '2025-09-01'
	`)

	// Create trip detection service
	tripDetectionService := service.NewTripDetectionService()

	// Process August data with date filtering
	log.Println("Running trip detection for August 2025...")
	err := tripDetectionService.ProcessUnassignedGPSDataWithDateFilter("2025-08-01", "2025-09-01")
	if err != nil {
		log.Printf("Error processing August data: %v", err)
	} else {
		log.Println("August trip detection completed successfully")
	}

	// Show results
	showResults()
}

func showResults() {
	log.Println("\n=== AUGUST 2025 TRIP DETECTION RESULTS ===")

	// Count trips by device
	var results []struct {
		ClientDeviceId    uint    `json:"client_device_id"`
		TripCount         int     `json:"trip_count"`
		TotalDistance     float64 `json:"total_distance"`
		GPSPointsAssigned int     `json:"gps_points_assigned"`
	}

	config.DB.Raw(`
		SELECT 
			t.client_device_id,
			COUNT(t.id) as trip_count,
			COALESCE(SUM(t.distance), 0) as total_distance,
			COUNT(g.id) as gps_points_assigned
		FROM trips t
		LEFT JOIN gps_data g ON t.id = g.trip_id
		WHERE t.start_time >= '2025-08-01' AND t.start_time < '2025-09-01'
		GROUP BY t.client_device_id
		ORDER BY trip_count DESC
	`).Scan(&results)

	for _, result := range results {
		log.Printf("Device %d: %d trips, %.2fkm total, %d GPS points assigned",
			result.ClientDeviceId, result.TripCount, result.TotalDistance, result.GPSPointsAssigned)
	}

	// Show sample trips
	var sampleTrips []models.Trip
	config.DB.Where("start_time >= ? AND start_time < ?", "2025-08-01", "2025-09-01").
		Order("distance DESC").
		Limit(5).
		Find(&sampleTrips)

	log.Println("\nTop 5 longest trips:")
	for i, trip := range sampleTrips {
		duration := ""
		if trip.Duration != nil {
			hours := *trip.Duration / 3600
			minutes := (*trip.Duration % 3600) / 60
			if hours > 0 {
				duration = fmt.Sprintf("%dh %dm", hours, minutes)
			} else {
				duration = fmt.Sprintf("%dm", minutes)
			}
		}
		log.Printf("%d. Trip %d: %.2fkm, %s (Device %d)",
			i+1, trip.Id, trip.Distance, duration, trip.ClientDeviceId)
	}
}
