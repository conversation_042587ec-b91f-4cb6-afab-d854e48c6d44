package main

import (
	"fmt"
	"log"
	"os"
	"strings"
	"yotracker/examples"
)

func main() {
	fmt.Println("=== YoTracker Mail Service Examples ===")
	fmt.Println()

	// Set up test environment variables if not already set
	setupTestEnvironment()

	fmt.Println("1. Basic Mail Usage Examples:")
	fmt.Println("-----------------------------")
	examples.ExampleMailUsage()

	fmt.Println("\n2. Template Usage Examples:")
	fmt.Println("---------------------------")
	examples.ExampleTemplateUsage()

	fmt.Println("\n3. New Account Email Examples:")
	fmt.Println("------------------------------")
	examples.ExampleNewAccountEmail()

	fmt.Println("\n4. Client Welcome Email Examples:")
	fmt.Println("----------------------------------")
	examples.ExampleClientWelcomeEmail()

	fmt.Println("\n5. Service Suspension Email Examples:")
	fmt.Println("-------------------------------------")
	examples.ExampleServiceSuspensionEmail()

	fmt.Println("\n6. Invoice Reminder Email Examples:")
	fmt.Println("-----------------------------------")
	examples.ExampleInvoiceReminderEmails()

	fmt.Println("\n7. Controller Integration Examples:")
	fmt.Println("----------------------------------")
	examples.ExampleSetupMailRoutes()

	fmt.Println("\n8. Update Profile Examples:")
	fmt.Println("---------------------------")
	examples.ExampleUpdateProfile()

	fmt.Println("\n9. Client Update Examples:")
	fmt.Println("--------------------------")
	examples.ExampleClientUpdate()

	fmt.Println("\n=== All Examples Completed ===")
	fmt.Println("=== YoTracker Slack Service Demo ===")
	fmt.Println()

	fmt.Println("Running Simple Slack Example...")
	fmt.Println(strings.Repeat("=", 50))
	examples.SlackSimpleExample()

	fmt.Println()
	fmt.Println("Running Detailed Slack Example...")
	fmt.Println(strings.Repeat("=", 50))
	examples.SlackUsageExample()

	fmt.Println()
	fmt.Println("=== All Demos Complete ===")
}

func setupTestEnvironment() {
	// Set up test environment variables if not already set
	if os.Getenv("SENDGRID_API_KEY") == "" {
		os.Setenv("SENDGRID_API_KEY", "SG.test-key-for-examples")
		log.Println("Note: Using test SendGrid API key. Set SENDGRID_API_KEY for actual email sending.")
	}

	if os.Getenv("FROM_EMAIL") == "" {
		os.Setenv("FROM_EMAIL", "<EMAIL>")
	}

	if os.Getenv("FROM_NAME") == "" {
		os.Setenv("FROM_NAME", "YoTracker")
	}

	if os.Getenv("APP_URL") == "" {
		os.Setenv("APP_URL", "https://yourdomain.com")
	}

	// Set test mode to prevent actual email sending
	os.Setenv("GO_ENV", "test")

	fmt.Println("Environment configured for examples (test mode - no actual emails will be sent)")
	fmt.Println()
}
