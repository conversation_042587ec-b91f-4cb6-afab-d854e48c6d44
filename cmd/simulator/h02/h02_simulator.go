package main

import (
	"bufio"
	"fmt"
	"math/rand"
	"net"
	"strings"
	"time"
)

func main() {
	fmt.Println("Starting h02 Simulator")
	rand.Seed(time.Now().UnixNano())
	for {
		err := connectAndSimulate()
		if err != nil {
			fmt.Println("Connection Error:", err.Error())
			fmt.Println("Retrying in 5 seconds...")
			time.Sleep(5 * time.Second)
		}
	}
}
func connectAndSimulate() (err error) {
	serverAddress := "tcp-server-dev:5010"
	conn, err := net.Dial("tcp", serverAddress)
	if err != nil {
		return fmt.Errorf("failed to connect to server: %v", err)
	}
	defer conn.Close()
	disconnect := make(chan error)
	go sendLocationPacket(conn, disconnect)
	go sendAlarmPacket(conn, disconnect)
	go sendHeartbeatPacket(conn, disconnect)
	go listenForResponses(conn, disconnect)
	return <-disconnect
}
func listenForResponses(conn net.Conn, disconnect chan<- error) {

	reader := bufio.NewReader(conn)
	for {

		data, err := reader.ReadBytes('#')
		if err != nil {
			fmt.Println("Error reading from server:", err)
			disconnect <- err
			return
		}
		parts := strings.Split(string(data), ",")
		if len(parts) > 2 {
			if parts[2] == "S33" {
				fmt.Println("Received set speed alert command")
				//acknowledge the command
				command := fmt.Sprintf("*HQ,%s,V4,%s, speed %s", parts[1], parts[2], parts[3])
				_, err = conn.Write([]byte(command))
				if err != nil {
					fmt.Println("Error writing to server:", err.Error())
				}

			}
		}
		fmt.Println("Received data:", data)
		fmt.Println("Received data as a string:", string(data))
	}
}
func sendLocationPacket(conn net.Conn, disconnect chan<- error) {
	type Coord struct {
		Lat   float64
		Lon   float64
		Speed float64 // in km/h
	}

	// Simulated route with acceleration and deceleration
	coords := []Coord{
		{-17.91844, 30.94870, 0},
		{-17.91700, 30.95000, 10},
		{-17.91500, 30.95200, 20},
		{-17.91200, 30.95500, 40},
		{-17.90900, 30.96000, 60},
		{-17.90500, 30.96500, 80},
		{-17.90000, 30.97000, 50},
		{-17.89500, 30.97500, 30},
		{-17.89000, 30.98000, 10},
		{-17.88500, 30.98500, 0}, // stop
	}

	coordIndex := 0

	for {
		// Simulate random pause before sending
		pause := time.Duration(rand.Intn(10)) * time.Second
		fmt.Printf("Pausing for %v before sending...\n", pause)
		time.Sleep(pause)

		coord := coords[coordIndex%len(coords)]
		packetStr := buildH02Packet(coord.Lat, coord.Lon, coord.Speed)
		packetToSend := []byte(packetStr)

		// Debug print statements for decimal and formatted lat/lon values
		fmt.Printf("Decimal Lat: %.6f, Lon: %.6f\n", coord.Lat, coord.Lon)
		fmt.Printf("Formatted Lat: %s, Lon: %s\n", formatLat(coord.Lat), formatLon(coord.Lon))
		fmt.Println("Sending packet:", packetStr)

		_, err := conn.Write(packetToSend)
		if err != nil {
			disconnect <- err
			return
		}
		coordIndex++
		fmt.Println("Sent location packet")

		// Wait for the regular interval before next possible send
		//time.Sleep(30 * time.Second)
	}
}

func buildH02Packet(lat, lon, speed float64) string {
	latStr := formatLat(lat)
	lonStr := formatLon(lon)
	speedStr := fmt.Sprintf("%.2f", speed)
	// Static timestamp and extras for simplicity
	return fmt.Sprintf("*HQ,9171003406,V5,175144,A,%s,%s,%s,0,210325,FFFFFDFF,648,40,21005,10913,0#", latStr, lonStr, speedStr)
}

func formatLat(lat float64) string {
	dir := "N"
	if lat < 0 {
		dir = "S"
		lat = -lat
	}
	deg := int(lat)
	min := (lat - float64(deg)) * 60
	return fmt.Sprintf("%02d%06.4f,%s", deg, min, dir)
}

func formatLon(lon float64) string {
	dir := "E"
	if lon < 0 {
		dir = "W"
		lon = -lon
	}
	deg := int(lon)
	m := (lon - float64(deg)) * 60
	return fmt.Sprintf("%02d%06.4f,%s", deg, m, dir)
}
func sendAlarmPacket(conn net.Conn, disconnect chan<- error) {

}
func sendHeartbeatPacket(conn net.Conn, disconnect chan<- error) {

}
