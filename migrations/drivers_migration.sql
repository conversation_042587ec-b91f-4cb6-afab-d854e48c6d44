-- Create drivers table
CREATE TABLE IF NOT EXISTS `drivers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_by_id` bigint unsigned NOT NULL,
  `client_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `fleet_id` bigint unsigned DEFAULT NULL,
  `country_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `dob` date DEFAULT NULL,
  `gender` varchar(50) DEFAULT NULL,
  `blood_type` varchar(10) DEFAULT NULL,
  `driver_license_no` varchar(100) DEFAULT NULL,
  `license_expiry_date` date DEFAULT NULL,
  `phone_number` varchar(50) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text,
  `rfid` varchar(100) DEFAULT NULL,
  `description` text,
  `status` varchar(50) NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_drivers_client_id` (`client_id`),
  KEY `idx_drivers_user_id` (`user_id`),
  KEY `idx_drivers_fleet_id` (`fleet_id`),
  KEY `idx_drivers_country_id` (`country_id`),
  CONSTRAINT `fk_drivers_client_id` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_drivers_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_drivers_fleet_id` FOREIGN KEY (`fleet_id`) REFERENCES `fleets` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_drivers_country_id` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add driver_id column to users table if it doesn't exist
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `driver_id` bigint unsigned DEFAULT NULL;
ALTER TABLE `users` ADD INDEX IF NOT EXISTS `idx_users_driver_id` (`driver_id`);
ALTER TABLE `users` ADD CONSTRAINT IF NOT EXISTS `fk_users_driver_id` FOREIGN KEY (`driver_id`) REFERENCES `drivers` (`id`) ON DELETE SET NULL; 