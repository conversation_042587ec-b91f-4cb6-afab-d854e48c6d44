package migrations

import (
	"yotracker/config"
	"yotracker/internal/models"
)

func Migrate() {
	// Disable foreign key checks during migration to handle circular dependencies
	config.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	defer config.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

	// Migrate the schema
	config.DB.AutoMigrate(
		&models.Country{},
		&models.TaxRate{},
		&models.Currency{},
		&models.Setting{},
		&models.Alert{},
		&models.PaymentType{},
		&models.Client{},
		&models.Fleet{},
		&models.Driver{}, // Move Driver before User to fix foreign key constraint
		&models.User{},
		&models.ClientDevice{},
		&models.CommandLog{},
		&models.DeviceType{},

		&models.GPSData{},
		&models.Protocol{},

		&models.Role{},
		&models.Permission{},
		&models.RolePermission{},
		&models.ClientRole{},
		&models.Invoice{},
		&models.InvoiceItem{},
		&models.InvoicePayment{},
		&models.ClientDeviceDailyStat{},
		&models.ChatbotSession{},
		&models.Geofence{},
		&models.GeofenceEvent{},
		&models.CommunicationCampaignLog{},
		&models.DriverDeviceAssignment{},
		&models.Trip{},
		&models.DrivingBehaviorEvent{},
		&models.Report{},
		&models.ScheduledReport{},
	)

}
