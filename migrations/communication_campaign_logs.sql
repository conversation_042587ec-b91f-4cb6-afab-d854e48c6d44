-- Create communication_campaign_logs table
CREATE TABLE IF NOT EXISTS communication_campaign_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NULL,
    `to` TEXT NOT NULL COMMENT 'Recipient (phone number, email, etc.)',
    campaign_type VARCHAR(50) NOT NULL COMMENT 'sms, whatsapp, email',
    message TEXT NOT NULL COMMENT 'The actual message content',
    status VARCHAR(20) NOT NULL DEFAULT 'sent' COMMENT 'sent, failed, pending',
    error_message TEXT NULL COMMENT 'Error details if failed',
    metadata TEXT NULL COMMENT 'Additional JSON data',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_client_id (client_id),
    INDEX idx_campaign_type (campaign_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 