TRUNCATE TABLE settings;
insert into settings (name, setting_key, setting_value, category)
values ('Invoice Reference Prefix', 'invoice_reference_prefix', 'INV-', 'general'),
       ('Invoice Reference Format', 'invoice_reference_format', 'YEAR/Sequence Number (SL/2014/001)', 'general'),
       ('Generate Invoice Before Days', 'generate_invoice_before_days', '7', 'general'),
       ('Invoice Due After', 'invoice_due_after_days', '15', 'general'),
       ('Invoice Upcoming Reminder Days', 'invoice_upcoming_reminder_days', '7', 'general'),
       ('First Invoice Overdue Reminder Days', 'first_invoice_overdue_reminder_days', '2', 'general'),
       ('Second Invoice Overdue Reminder Days', 'second_invoice_overdue_reminder_days', '5', 'general'),
       ('Third Invoice Overdue Reminder Days', 'third_invoice_overdue_reminder_days', '10', 'general'),
       ('Invoice Predefined Terms & Conditions', 'invoice_terms_and_conditions',
        'Thank you for your business. We do expect payment within 30 days, so please process this invoice within that time.',
        'general'),
       ('Allow Editing Currency Exchange Rate', 'invoice_edit_exchange_rate', 'yes', 'general'),
       ('Google Maps API Key', 'google_maps_api_key', 'AIzaSyCdXVEvo2y62tP893ZkzYROL41C3DPQmuQ', 'system'),
       ('Company Name', 'company_name', 'Yotracker', 'system'),
       ('Company Address', 'company_address', 'Harare', 'system'),
       ('Company Phone', 'company_phone', '+263 *********', 'system'),
       ('Company Email', 'company_email', '<EMAIL>', 'system'),
       ('Currency', 'currency', '1', 'system'),
       ('Vat', 'vat', '1', 'system'),
       ('Company Website', 'company_website', 'https://yotracker.co.zw', 'system'),
       ('Company Tel', 'company_tel', '+263 *********', 'system'),
       ('Default Maps Provider', 'default_maps_provider', 'google', 'system'),
       ('Slack Bot Token', 'slack_bot_token', '', 'notifications'),
       ('Slack Webhook URL', 'slack_webhook_url', '', 'notifications'),
       ('Slack Default Channel', 'slack_default_channel', '#alerts', 'notifications'),
       ('Slack Alerts Enabled', 'slack_alerts_enabled', '1', 'notifications'),
       ('Slack Alert Severity Filter', 'slack_alert_severity_filter', 'warning,critical', 'notifications'),
       ('Slack Raw Data Enabled', 'slack_raw_data_enabled', '1', 'notifications'),
       ('Slack Raw Data Channel', 'slack_raw_data_channel', '#yotracker-device-logs', 'notifications'),
       ('WhatsApp Alerts Enabled', 'whatsapp_alerts_enabled', '0', 'notifications'),
       ('WhatsApp Access Token', 'whatsapp_access_token', '', 'notifications'),
       ('WhatsApp Phone Number ID', 'whatsapp_phone_number_id', '', 'notifications'),
       ('WHATSAPP_BUSINESS_ACCOUNT_ID', 'whatsapp_business_account_id', '', 'notifications'),
       ('WhatsApp Base URL', 'whatsapp_base_url', 'https://graph.facebook.com/v22.0', 'notifications'),
       ('WhatsApp Default Country Code', 'whatsapp_default_country_code', '263', 'notifications'),
       ('WhatsApp Device Alert Template', 'whatsapp_device_alert_template', 'device_alert', 'notifications'),
       ('WhatsApp Template Language', 'whatsapp_template_language', 'en', 'notifications'),
       ('Dashboard URL', 'dashboard_url', 'https://yotracker.co.zw/dashboard', 'system'),
       ('SMS Alerts Enabled', 'sms_alerts_enabled', '0', 'notifications'),
       ('Email Alerts Enabled', 'email_alerts_enabled', '0', 'notifications'),
       ('System Online', 'system_online', '1', 'system'),
       ('System Offline Message', 'system_offline_message', 'The system is currently offline for maintenance. Please try again later.', 'system'),
       ('Trip Detection Idle Timeout Minutes', 'trip_detection_idle_timeout_minutes', '5', 'trip_detection'),
       ('Trip Detection Minimum Speed Threshold', 'trip_detection_min_speed_threshold', '1', 'trip_detection'),
       ('Trip Detection Minimum Distance Meters', 'trip_detection_min_distance_meters', '100', 'trip_detection'),
       ('Trip Detection Lookahead Points', 'trip_detection_lookahead_points', '3', 'trip_detection'),
       ('Trip Detection Processing Batch Size', 'trip_detection_batch_size', '1000', 'trip_detection');
