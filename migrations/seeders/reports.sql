-- YoTracker Reports Seeder
-- Powerful Client-Focused Reports that deliver real business value
-- These reports are designed to capture clients and provide actionable insights

-- Clear existing reports and insert powerful ones
DELETE FROM reports;

INSERT INTO reports (name, description, category, report_type, status, created_at, updated_at) VALUES

-- === CORE GPS & TRACKING REPORTS ===
('Position Log Report', 'Raw GPS location data with timestamps, coordinates, speed, and device information for detailed tracking analysis.', 'Detail', 'position_log', 'active', NOW(), NOW()),

-- === TRIP & MOVEMENT REPORTS ===
('Trip Detail Report', 'Complete trip analysis with start/end locations, distance, duration, fuel consumption, and driver info.', 'Detail', 'trip_detail', 'active', NOW(), NOW()),
('Daily Trip Summary', 'Daily breakdown of trips per vehicle showing total distance, time, fuel usage, and efficiency metrics.', 'Summary', 'daily_trip_summary', 'active', NOW(), NOW()),
('Monthly Mileage Report', 'Monthly distance traveled per vehicle with cost analysis and maintenance scheduling recommendations.', 'Summary', 'monthly_mileage', 'active', NOW(), NOW()),
('Vehicle Utilization Report', 'Shows how effectively vehicles are being used - idle time vs active time, utilization percentage.', 'Summary', 'vehicle_utilization', 'active', NOW(), NOW()),
('Route Efficiency Analysis', 'Analyzes common routes for optimization opportunities, identifies inefficient paths and suggests improvements.', 'Analysis', 'route_efficiency', 'active', NOW(), NOW()),

-- === DRIVER BEHAVIOR & SAFETY REPORTS ===
('Driver Safety Scorecard', 'Comprehensive driver safety scoring based on speeding, harsh braking, acceleration, cornering events.', 'Behavior', 'driver_safety_scorecard', 'active', NOW(), NOW()),
('Speeding Violations Report', 'Detailed speeding events with location, speed exceeded, duration, and safety risk assessment.', 'Behavior', 'speeding_violations', 'active', NOW(), NOW()),
('Harsh Driving Events', 'All harsh acceleration, braking, and cornering events with severity levels and coaching recommendations.', 'Behavior', 'harsh_driving_events', 'active', NOW(), NOW()),
('Driver Performance Ranking', 'Ranks drivers by safety score, fuel efficiency, and overall performance with improvement suggestions.', 'Behavior', 'driver_performance_ranking', 'active', NOW(), NOW()),
('Fatigue & Overtime Alert', 'Identifies drivers exceeding safe driving hours, potential fatigue risks, and compliance violations.', 'Behavior', 'fatigue_overtime_alert', 'active', NOW(), NOW()),

-- === GEOFENCE & LOCATION REPORTS ===
('Geofence Activity Report', 'Detailed entry/exit times for all geofences with dwell time analysis and unauthorized access alerts.', 'Detail', 'geofence_activity', 'active', NOW(), NOW()),
('Customer Visit Report', 'Tracks visits to customer locations with arrival/departure times, duration, and service efficiency metrics.', 'Detail', 'customer_visits', 'active', NOW(), NOW()),
('Unauthorized Location Alerts', 'Vehicles detected in restricted areas or outside authorized zones with time and duration details.', 'Security', 'unauthorized_locations', 'active', NOW(), NOW()),
('Job Site Productivity', 'Time spent at job sites, productivity metrics, and billing-ready timesheet data.', 'Productivity', 'jobsite_productivity', 'active', NOW(), NOW()),

-- === COST & EFFICIENCY REPORTS ===
('Fuel Consumption Analysis', 'Detailed fuel usage per vehicle with cost breakdown, efficiency trends, and savings opportunities.', 'Cost', 'fuel_consumption_analysis', 'active', NOW(), NOW()),
('Operating Cost Report', 'Complete cost analysis including fuel, maintenance, insurance, and depreciation per vehicle/mile.', 'Cost', 'operating_cost_report', 'active', NOW(), NOW()),
('Idle Time Cost Analysis', 'Calculates cost of excessive idling with fuel waste, engine wear, and environmental impact.', 'Cost', 'idle_time_cost', 'active', NOW(), NOW()),
('Fleet ROI Dashboard', 'Return on investment analysis showing cost savings, efficiency gains, and fleet optimization results.', 'Financial', 'fleet_roi_dashboard', 'active', NOW(), NOW()),

-- === MAINTENANCE & VEHICLE HEALTH ===
('Maintenance Due Report', 'Upcoming maintenance schedules based on mileage, engine hours, and time intervals with cost estimates.', 'Maintenance', 'maintenance_due', 'active', NOW(), NOW()),
('Vehicle Health Dashboard', 'Real-time vehicle diagnostics, fault codes, battery status, and preventive maintenance alerts.', 'Maintenance', 'vehicle_health_dashboard', 'active', NOW(), NOW()),
('Breakdown & Repair History', 'Complete maintenance history with costs, downtime analysis, and reliability trends per vehicle.', 'Maintenance', 'breakdown_repair_history', 'active', NOW(), NOW()),

-- === COMPLIANCE & SECURITY REPORTS ===
('Hours of Service Compliance', 'Driver hours tracking for DOT compliance with violation alerts and logbook integration.', 'Compliance', 'hours_of_service', 'active', NOW(), NOW()),
('Vehicle Security Report', 'After-hours usage, unauthorized access, theft alerts, and security breach notifications.', 'Security', 'vehicle_security', 'active', NOW(), NOW()),
('Speed Limit Compliance', 'Compliance with posted speed limits by location with violation frequency and risk assessment.', 'Compliance', 'speed_limit_compliance', 'active', NOW(), NOW()),

-- === EXECUTIVE & MANAGEMENT REPORTS ===
('Executive Fleet Summary', 'High-level KPIs for management: total miles, costs, safety scores, efficiency metrics, and trends.', 'Executive', 'executive_fleet_summary', 'active', NOW(), NOW()),
('Fleet Performance Trends', 'Month-over-month performance analysis with predictive insights and optimization recommendations.', 'Executive', 'fleet_performance_trends', 'active', NOW(), NOW()),
('Cost Center Analysis', 'Cost breakdown by department, project, or cost center with budget variance and allocation insights.', 'Financial', 'cost_center_analysis', 'active', NOW(), NOW()),
('Environmental Impact Report', 'Carbon footprint analysis, fuel efficiency trends, and sustainability metrics for ESG reporting.', 'Environmental', 'environmental_impact', 'active', NOW(), NOW()),

-- === OPERATIONAL REPORTS ===
('Daily Operations Dashboard', 'Real-time operational overview: active vehicles, current locations, alerts, and daily performance.', 'Operations', 'daily_operations_dashboard', 'active', NOW(), NOW()),
('Vehicle Availability Report', 'Shows which vehicles are available, in use, under maintenance, or out of service with scheduling.', 'Operations', 'vehicle_availability', 'active', NOW(), NOW()),
('Emergency Response Report', 'Panic button activations, emergency stops, accident detection, and response time analysis.', 'Emergency', 'emergency_response', 'active', NOW(), NOW()),
('Asset Tracking Report', 'Location and status of all tracked assets with last known position and movement history.', 'Operations', 'asset_tracking', 'active', NOW(), NOW()),

-- === CUSTOM BUSINESS REPORTS ===
('Delivery Performance Report', 'On-time delivery rates, route optimization, customer satisfaction metrics, and delivery efficiency.', 'Business', 'delivery_performance', 'active', NOW(), NOW()),
('Service Technician Report', 'Field service efficiency: jobs completed, travel time, customer locations, and productivity metrics.', 'Business', 'service_technician', 'active', NOW(), NOW()),
('Construction Equipment Report', 'Equipment utilization, job site time, productivity metrics, and equipment ROI analysis.', 'Business', 'construction_equipment', 'active', NOW(), NOW()),
('Sales Territory Analysis', 'Sales team territory coverage, customer visit frequency, and territory optimization insights.', 'Business', 'sales_territory_analysis', 'active', NOW(), NOW()),

-- === ADDITIONAL SPECIALIZED REPORTS ===
('Driver Behavior Analysis', 'Comprehensive analysis of driver behavior patterns, trends, and improvement recommendations.', 'Behavior', 'driver_behavior_analysis', 'active', NOW(), NOW()),
('Fuel Efficiency Trends', 'Long-term fuel efficiency analysis with cost projections and optimization recommendations.', 'Cost', 'fuel_efficiency_trends', 'active', NOW(), NOW()),
('Maintenance Cost Analysis', 'Detailed breakdown of maintenance costs with predictive maintenance recommendations.', 'Maintenance', 'maintenance_cost_analysis', 'active', NOW(), NOW()),
('Fleet Productivity Report', 'Overall fleet productivity metrics with utilization and efficiency insights.', 'Operations', 'fleet_productivity', 'active', NOW(), NOW()),
('Safety Compliance Report', 'Comprehensive safety compliance tracking with regulatory requirement monitoring.', 'Compliance', 'safety_compliance', 'active', NOW(), NOW()),
('Asset Performance Report', 'Individual asset performance tracking with ROI and replacement recommendations.', 'Financial', 'asset_performance', 'active', NOW(), NOW()),
('Route Optimization Report', 'Advanced route optimization analysis with time and fuel savings recommendations.', 'Analysis', 'route_optimization', 'active', NOW(), NOW()),
('Driver Training Report', 'Driver training needs analysis based on performance and safety metrics.', 'Behavior', 'driver_training', 'active', NOW(), NOW()),
('Vehicle Lifecycle Report', 'Complete vehicle lifecycle analysis with depreciation and replacement planning.', 'Financial', 'vehicle_lifecycle', 'active', NOW(), NOW()),
('Operational Efficiency Report', 'Overall operational efficiency metrics with improvement recommendations.', 'Operations', 'operational_efficiency', 'active', NOW(), NOW());
