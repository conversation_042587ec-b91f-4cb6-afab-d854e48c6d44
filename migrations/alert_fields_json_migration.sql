-- Migration to update alert fields to support JSON arrays
-- This allows clients to have multiple WhatsApp numbers, SMS numbers, and alert emails

-- Update the column types to support JSON arrays
ALTER TABLE clients 
MODIFY COLUMN whatsapp_phone_number TEXT COMMENT 'JSON array of WhatsApp phone numbers',
MODIFY COLUMN sms_phone_number TEXT COMMENT 'JSON array of SMS phone numbers',
MODIFY COLUMN alerts_email TEXT COMMENT 'JSON array of alert email addresses';

-- Add indexes for better performance when querying these fields
CREATE INDEX idx_clients_whatsapp_phone_number ON clients(whatsapp_phone_number(100));
CREATE INDEX idx_clients_sms_phone_number ON clients(sms_phone_number(100));
CREATE INDEX idx_clients_alerts_email ON clients(alerts_email(100));

-- Update existing single values to JSON arrays format
-- This migration assumes existing values are single phone numbers/emails
-- and converts them to JSON array format

UPDATE clients 
SET whatsapp_phone_number = JSON_ARRAY(whatsapp_phone_number)
WHERE whatsapp_phone_number IS NOT NULL 
AND whatsapp_phone_number != '' 
AND whatsapp_phone_number NOT LIKE '[%]';

UPDATE clients 
SET sms_phone_number = JSON_ARRAY(sms_phone_number)
WHERE sms_phone_number IS NOT NULL 
AND sms_phone_number != '' 
AND sms_phone_number NOT LIKE '[%]';

UPDATE clients 
SET alerts_email = JSON_ARRAY(alerts_email)
WHERE alerts_email IS NOT NULL 
AND alerts_email != '' 
AND alerts_email NOT LIKE '[%]';

-- Add comments to document the new JSON structure
ALTER TABLE clients 
MODIFY COLUMN whatsapp_phone_number TEXT COMMENT 'JSON array of WhatsApp phone numbers, e.g., ["+1234567890", "+0987654321"]',
MODIFY COLUMN sms_phone_number TEXT COMMENT 'JSON array of SMS phone numbers, e.g., ["+1234567890", "+0987654321"]',
MODIFY COLUMN alerts_email TEXT COMMENT 'JSON array of alert email addresses, e.g., ["<EMAIL>", "<EMAIL>"]'; 