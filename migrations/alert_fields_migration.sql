-- Migration: Add alert fields to clients table
-- Date: 2024-01-15
-- Description: Adds WhatsApp, SMS, and Email alert preferences and contact information to clients table

-- Add alert preference fields (backend-only, can only be enabled from backend)
ALTER TABLE clients 
ADD COLUMN whatsapp_alerts_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN sms_alerts_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN email_alerts_enabled BOOLEAN DEFAULT FALSE;

-- Add alert contact information fields (can be set by frontend)
ALTER TABLE clients 
ADD COLUMN whatsapp_phone_number VARCHAR(255),
ADD COLUMN sms_phone_number VARCHAR(255),
ADD COLUMN alerts_email VARCHAR(255);

-- Add indexes for better query performance
CREATE INDEX idx_clients_whatsapp_alerts_enabled ON clients(whatsapp_alerts_enabled);
CREATE INDEX idx_clients_sms_alerts_enabled ON clients(sms_alerts_enabled);
CREATE INDEX idx_clients_email_alerts_enabled ON clients(email_alerts_enabled);

-- Add global settings for alert services
INSERT INTO settings (name, setting_key, setting_value, category) VALUES
('SMS Alerts Enabled', 'sms_alerts_enabled', 'false', 'notifications'),
('WhatsApp Alerts Enabled', 'whatsapp_alerts_enabled', 'false', 'notifications'),
('Email Alerts Enabled', 'email_alerts_enabled', 'true', 'notifications')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

-- Add comments to document the new fields
ALTER TABLE clients 
MODIFY COLUMN whatsapp_alerts_enabled BOOLEAN DEFAULT FALSE COMMENT 'Whether WhatsApp alerts are enabled for this client (backend-only)',
MODIFY COLUMN sms_alerts_enabled BOOLEAN DEFAULT FALSE COMMENT 'Whether SMS alerts are enabled for this client (backend-only)',
MODIFY COLUMN email_alerts_enabled BOOLEAN DEFAULT FALSE COMMENT 'Whether email alerts are enabled for this client (backend-only)',
MODIFY COLUMN whatsapp_phone_number VARCHAR(255) COMMENT 'Phone number for WhatsApp alerts (frontend can set)',
MODIFY COLUMN sms_phone_number VARCHAR(255) COMMENT 'Phone number for SMS alerts (frontend can set)',
MODIFY COLUMN alerts_email VARCHAR(255) COMMENT 'Email address for alerts (frontend can set, defaults to main email)'; 