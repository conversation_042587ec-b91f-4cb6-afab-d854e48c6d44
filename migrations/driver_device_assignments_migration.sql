-- Create driver_device_assignments table
CREATE TABLE IF NOT EXISTS `driver_device_assignments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_by_id` bigint unsigned NOT NULL,
  `driver_id` bigint unsigned NOT NULL,
  `client_device_id` bigint unsigned NOT NULL,
  `assignment_date` date NOT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'active',
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_driver_device_assignments_driver_id` (`driver_id`),
  KEY `idx_driver_device_assignments_client_device_id` (`client_device_id`),
  KEY `idx_driver_device_assignments_assignment_date` (`assignment_date`),
  KEY `idx_driver_device_assignments_status` (`status`),
  UNIQUE KEY `unique_active_assignment_per_device_per_date` (`client_device_id`, `assignment_date`, `status`),
  CONSTRAINT `fk_driver_device_assignments_driver_id` FOREIGN KEY (`driver_id`) REFERENCES `drivers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_driver_device_assignments_client_device_id` FOREIGN KEY (`client_device_id`) REFERENCES `client_devices` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Remove driver_id column from gps_data table if it exists
ALTER TABLE `gps_data` DROP COLUMN IF EXISTS `driver_id`; 