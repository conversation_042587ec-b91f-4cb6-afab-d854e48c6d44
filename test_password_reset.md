# Password Reset Implementation Test Guide

## Overview
This document provides a comprehensive test guide for the newly implemented password reset functionality.

## Implementation Summary

### What was implemented:

1. **JWT Token Enhancement**
   - Added "password_reset" token type to existing JWT service
   - 24-hour expiration for password reset tokens
   - Proper token type validation

2. **Backend Auth Controller** (`cmd/web/backend/controllers/auth.go`)
   - `RequestPasswordReset` - initiates password reset process
   - `VerifyPasswordResetToken` - validates reset token
   - `ResetPassword` - updates password using valid token

3. **Frontend Auth Controller** (`cmd/web/frontend/controllers/auth.go`)
   - Same functions as backend but for client users
   - Additional client status validation

4. **Routes Added**
   - Backend: `/api/v1/backend/password-reset/request`
   - Backend: `/api/v1/backend/password-reset/verify`
   - Backend: `/api/v1/backend/password-reset/confirm`
   - Frontend: `/api/v1/frontend/password-reset/request`
   - Frontend: `/api/v1/frontend/password-reset/verify`
   - Frontend: `/api/v1/frontend/password-reset/confirm`

5. **Email Integration**
   - Uses existing SendGrid integration
   - Sends password reset emails with reset links
   - Non-blocking email sending

## Testing the Implementation

### Prerequisites
1. Ensure the application is running
2. Have a valid user account in the database
3. Set environment variables:
   - `APP_URL` (for reset links)
   - `SENDGRID_API_KEY` (for email sending)
   - `FROM_EMAIL` and `FROM_NAME`

### Test Cases

#### 1. Request Password Reset (Backend User)

```bash
curl -X POST http://localhost:9000/api/v1/backend/password-reset/request \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

Expected Response:
```json
{
  "message": "If the email exists, a password reset link has been sent"
}
```

#### 2. Request Password Reset (Frontend User)

```bash
curl -X POST http://localhost:9001/api/v1/frontend/password-reset/request \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

#### 3. Verify Password Reset Token

```bash
curl -X POST http://localhost:9000/api/v1/backend/password-reset/verify \
  -H "Content-Type: application/json" \
  -d '{"token": "YOUR_RESET_TOKEN_HERE"}'
```

Expected Response (if valid):
```json
{
  "message": "Token is valid",
  "email": "<EMAIL>"
}
```

#### 4. Reset Password

```bash
curl -X POST http://localhost:9000/api/v1/backend/password-reset/confirm \
  -H "Content-Type: application/json" \
  -d '{"token": "YOUR_RESET_TOKEN_HERE", "password": "newpassword123"}'
```

Expected Response:
```json
{
  "message": "Password has been reset successfully"
}
```

### Security Features

1. **Email Enumeration Protection**: Always returns success message regardless of email existence
2. **Token Type Validation**: Password reset tokens cannot be used as access tokens and vice versa
3. **Token Expiration**: Reset tokens expire after 24 hours
4. **Client Status Validation**: Frontend users must have active client status
5. **Password Strength**: Minimum 6 characters required

### Error Scenarios

1. **Invalid Email Format**
   ```json
   {"error": "Key: 'PasswordResetRequest.Email' Error:Field validation for 'Email' failed on the 'email' tag"}
   ```

2. **Invalid Token**
   ```json
   {"error": "Invalid or expired reset token"}
   ```

3. **Short Password**
   ```json
   {"error": "Key: 'PasswordResetConfirm.Password' Error:Field validation for 'Password' failed on the 'min' tag"}
   ```

## Unit Tests

The implementation includes comprehensive unit tests:

1. **JWT Service Tests** (`internal/service/jwt_authentication_test.go`)
   - Password reset token generation and validation
   - Token type isolation

2. **Controller Unit Tests** (`cmd/web/backend/controllers/auth_unit_test.go`)
   - Input validation
   - Token generation and verification
   - Password hashing

3. **Integration Tests** (requires database)
   - Full password reset flow
   - Database interactions
   - Email sending (mocked)

## Running Tests

```bash
# Run JWT service tests
go test ./internal/service -v -run TestPasswordResetToken

# Run controller unit tests
go test ./cmd/web/backend/controllers -v -run TestPasswordReset

# Run all tests
go test ./...
```

## Email Template

The implementation uses the existing email template system with:
- Professional HTML styling
- Reset link button
- 24-hour expiration notice
- Security warning about ignoring unwanted emails

## Environment Variables Required

```env
APP_URL=http://localhost:3000
SENDGRID_API_KEY=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>
FROM_NAME=YourApp
APP_KEY=your_jwt_secret_key
```

## Database Schema

No additional database tables are required. The implementation uses JWT tokens for stateless password reset functionality.

## Next Steps

1. Test the endpoints with real email addresses
2. Integrate with frontend application
3. Add rate limiting for password reset requests
4. Consider adding password reset attempt logging
5. Add email template customization options
