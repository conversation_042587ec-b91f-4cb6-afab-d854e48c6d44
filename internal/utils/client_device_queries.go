package utils

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"yotracker/config"
	"yotracker/internal/models"
)

// BuildClientDeviceQueryWithLastGPS builds a query that efficiently includes last GPS data
// using a LEFT JOIN with a subquery to get the latest GPS record for each device
func BuildClientDeviceQueryWithLastGPS() *gorm.DB {
	// Create a subquery to get the latest GPS data for each device
	latestGPSSubquery := config.DB.Table("gps_data g1").
		Select("g1.client_device_id, g1.latitude, g1.longitude, g1.gps_timestamp, g1.speed, g1.location_name, g1.ignition_status, g1.vehicle_status").
		Joins("INNER JOIN (SELECT client_device_id, MAX(gps_timestamp) as max_timestamp FROM gps_data GROUP BY client_device_id) g2 ON g1.client_device_id = g2.client_device_id AND g1.gps_timestamp = g2.max_timestamp")

	// Build the main query with LEFT JOIN to the latest GPS data
	return config.DB.Table("client_devices").
		Select(`client_devices.*,
			latest_gps.latitude as last_latitude,
			latest_gps.longitude as last_longitude,
			latest_gps.gps_timestamp as last_gps_timestamp,
			latest_gps.speed as last_speed,
			latest_gps.location_name as last_location_name,
			latest_gps.ignition_status as last_ignition_status,
			latest_gps.vehicle_status as last_vehicle_status`).
		Joins("LEFT JOIN (?) as latest_gps ON client_devices.id = latest_gps.client_device_id", latestGPSSubquery)
}

// GetClientDevicesWithLastGPS retrieves client devices with their last GPS data efficiently
func GetClientDevicesWithLastGPS(filter map[string]interface{}, preloads []string, pagination bool, c interface{}) ([]models.ClientDevice, error) {
	var clientDevices []models.ClientDevice

	query := BuildClientDeviceQueryWithLastGPS()

	// Add preloads
	for _, preload := range preloads {
		query = query.Preload(preload)
	}

	// Add pagination if requested
	if pagination {
		if ginContext, ok := c.(*gin.Context); ok {
			query = query.Scopes(Paginate(ginContext))
		}
	}

	// Apply filters
	if len(filter) > 0 {
		query = query.Where(filter)
	}

	err := query.Find(&clientDevices).Error
	return clientDevices, err
}

// GetClientDeviceByIdWithLastGPS retrieves a single client device with last GPS data
func GetClientDeviceByIdWithLastGPS(id string, preloads []string) (models.ClientDevice, error) {
	var clientDevice models.ClientDevice

	query := BuildClientDeviceQueryWithLastGPS()

	// Add preloads
	for _, preload := range preloads {
		query = query.Preload(preload)
	}

	err := query.Where("client_devices.id = ?", id).First(&clientDevice).Error
	return clientDevice, err
}

// SearchClientDevicesWithLastGPS searches client devices with last GPS data
func SearchClientDevicesWithLastGPS(filter map[string]interface{}, searchTerm string, preloads []string) ([]models.ClientDevice, error) {
	var clientDevices []models.ClientDevice

	query := BuildClientDeviceQueryWithLastGPS()

	// Add preloads
	for _, preload := range preloads {
		query = query.Preload(preload)
	}

	// Add search conditions
	if searchTerm != "" {
		query = query.Where("client_devices.name like ? or client_devices.id like ? or client_devices.device_id like ? or client_devices.phone_number like ? or client_devices.engine_number like ? or client_devices.chassis_number like ? or client_devices.vin like ? or client_devices.plate_number like ?",
			"%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%")
	}

	// Apply filters
	if len(filter) > 0 {
		query = query.Where(filter)
	}

	err := query.Find(&clientDevices).Error
	return clientDevices, err
}
