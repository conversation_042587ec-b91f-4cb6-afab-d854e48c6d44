package utils

import (
	"log"
	"os"
	"path/filepath"
)

// ForceProjectRoot sets the working directory to the Go module root.
func ForceProjectRoot() {
	root := findProjectRoot()
	if err := os.Chdir(root); err != nil {
		log.Fatalf("Failed to change working directory to project root: %v", err)
	}
}

// findProjectRoot traverses upward to find the directory containing go.mod
func findProjectRoot() string {
	dir, _ := os.Getwd()
	for {
		if _, err := os.Stat(filepath.Join(dir, "go.mod")); err == nil {
			return dir
		}
		parent := filepath.Dir(dir)
		if parent == dir {
			log.Fatal("Could not find project root")
		}
		dir = parent
	}
}
