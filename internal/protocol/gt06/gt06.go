package gt06

import (
	"bufio"
	"bytes"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/internal/utils"
)

const MSG_LOGIN = 0x01
const MSG_POSITION_DATA = 0x22
const MSG_GPS = 0x12
const MSG_STATUS = 0x13
const MSG_STATUS_2 = 0x23
const MSG_STRING = 0x21
const MSG_STRING_2 = 0x15
const MSG_LBS_INFORMATION = 0x24
const MSG_TIMEZONE = 0x27
const MSG_ALARM = 0x26
const MSG_ALARM_2 = 0x16

const MSG_COMMAND = 0x80
const MSG_COMMAND_RESPONSE = 0x21
const MSG_WIFI_PACKET = 0x69
const MSG_GENERAL_INFORMATION = 0x94

type Parser struct {
	Reader *bufio.Reader
	Conn   net.Conn
	Client *Client
}
type Packet struct {
	DeviceId  string    `json:"device_id"`
	Timestamp time.Time `json:"timestamp"`
	Latitude  float64   `json:"latitude"`
	Longitude float64   `json:"longitude"`
	Heading   float64   `json:"heading"`
	Speed     float64   `json:"speed"`
	Position  bool      `json:"position"`
	Battery   float64   `json:"battery"`

	packetType string
}
type GPSData struct {
	IMEI         string
	Timestamp    time.Time
	Latitude     float64
	Longitude    float64
	Speed        uint8
	Course       uint16
	Satellites   uint8
	SerialNumber uint16
}
type InfoHeader struct {
	Header [2]byte
}
type LoginMessagePacket struct {
	StartByte               []byte
	PacketLength            []byte
	ProtocolNumber          []byte
	TerminalID              []byte
	InformationSerialNumber []byte
	ErrorCheck              []byte
	StopBit                 []byte
}
type GpsDataPacket struct {
	StartByte         []byte
	PacketLength      []byte
	ProtocolNumber    []byte
	Date              []byte
	Satellites        []byte
	Latitude          []byte
	Longitude         []byte
	Speed             []byte
	Course            []byte
	Mcc               []byte
	Mnc               byte
	Lac               []byte
	CellId            []byte
	Acc               byte
	DataReportingMode byte
	GPSRetransmission byte
	Mileage           []byte
	SerialNumber      []byte
	ErrorCheck        []byte
	StopBit           []byte
}
type MessageStruct struct {
	InfoHeader
	ContentLength           byte
	Reserved                [2]byte
	DeviceID                [8]byte
	InformationSerialNumber [2]byte
	ProtocolNumber          byte
	InformationContent      struct {
		DateTime  [6]byte
		Latitude  uint32
		Longitude uint32
		Speed     byte
		Course    [2]byte
		Reserved  [3]byte
		Status    [4]byte
	}
	EndMark [2]byte
}

type Heartbeat struct {
	InfoHeader
	ContentLength           byte
	VoltageDegree           byte
	GSMSignalStrengthDegree byte
	DeviceID                [8]byte
	InformationSerialNumber [2]byte
	ProtocolNumber          byte
	NumSatellites           byte
	EndMark                 [2]byte
}
type TerminalStatus struct {
	AlarmType     string
	Alarm         string
	OilCut        bool
	GPSPositioned bool
	PowerCharging bool
	ACCHigh       bool
	DefenseOn     bool
	Language      string
	Battery       float64
	GSMSignal     float64
	Latitude      float64
	Longitude     float64
	Speed         float64
	Direction     string
	Timestamp     time.Time
	Satellites    int
	Mcc           int
	Mnc           int
	Lac           int
	CellId        int
	LBSLength     int
}
type errUnsupportedPacket struct {
	packetType string
}
type Client struct {
	Conn         net.Conn
	IMEI         string
	Protocol     string
	Device       models.ClientDevice
	CommandChan  chan Command
	ExitChan     chan struct{}
	ResponseChan chan Message
}
type Command struct {
	Data         []byte
	ResponseChan chan []byte
	Timeout      time.Duration
}
type Message struct {
	Type byte
	Data []byte
}
type CourseStatus struct {
	ACC           bool
	Input2        bool
	GPSFix        bool
	LatitudeNorth bool
	LongitudeEast bool
	Course        uint16
	Direction     string
}

// Global slice to store connected clients
var clients []Client
var clientMap = make(map[net.Conn]string)
var mu sync.Mutex // Mutex to protect the clients slice
func (e errUnsupportedPacket) Is(err error) bool {
	_, isa := err.(*errUnsupportedPacket)
	return isa
}
func (e errUnsupportedPacket) Error() string {
	return fmt.Sprintf("unable to parse %q is an unsupported packet type", e.packetType)
}
func (p *Parser) HandleConnection(conn net.Conn) {
	defer func() {
		p.removeClient()
		err := conn.Close()
		if err != nil {
			return
		}
	}()
	fmt.Println("GT06 Connection accepted")
	client := Client{
		Conn:         conn,
		CommandChan:  make(chan Command),
		ExitChan:     make(chan struct{}),
		ResponseChan: make(chan Message),
	}
	p.Client = &client
	go p.readConnection(&client)
	for {
		select {
		case cmd := <-client.CommandChan:
			//fmt.Println("Received command:", cmd)
			err := p.sendToDevice(cmd.Data)
			if err != nil {
				//fmt.Println("Error sending command:", err.Error())
				cmd.ResponseChan <- []byte("FAIL")
				continue
			}
			//wait for response in a separate routine
			go func(cmd Command) {
				timer := time.NewTimer(cmd.Timeout)
				defer timer.Stop()
				for {
					select {
					case msg := <-client.ResponseChan:
						if msg.Type == MSG_COMMAND_RESPONSE {
							//fmt.Println("gt06 command response received")
							cmd.ResponseChan <- msg.Data
							//return
						} else {
							p.processMessage(&client, msg)
						}
					case <-timer.C:
						//fmt.Println("gt06 command timed out")
						cmd.ResponseChan <- []byte("FAIL")
						return
					}
				}
			}(cmd)
		case msg := <-client.ResponseChan:
			p.processMessage(&client, msg)
		case <-client.ExitChan:
			return
		}
	}
}
func (p *Parser) readConnection(client *Client) {
	reader := bufio.NewReader(client.Conn)
	var timeout = 30 * time.Second
	for {
		select {
		case <-client.ExitChan:
			p.removeClient()
			return
		default:
			time.Sleep(10 * time.Millisecond)
		}
		client.Conn.SetReadDeadline(time.Now().Add(timeout))
		data, err := readPacketUntilCRLF(reader)
		if err != nil {
			//fmt.Println("Error reading data:", err.Error())
			client.ExitChan <- struct{}{}
			//close(client.ExitChan)
			return
		}
		//fmt.Println("Raw packet:", data)
		// Check for the start identifier 0x78
		if len(data) > 4 && data[len(data)-2] == 0x0D {
			if len(data) >= 2 && data[0] == 0x78 && data[1] == 0x78 {
				client.ResponseChan <- Message{Type: data[3], Data: data}
			} else {
				//fmt.Println("Invalid start identifier")
				//client.ExitChan <- struct{}{}
				return
			}
		}
	}
}
func readPacketUntilCRLF(reader *bufio.Reader) ([]byte, error) {
	var packet []byte
	for {
		b, err := reader.ReadByte()
		if err != nil {
			return nil, err
		}
		packet = append(packet, b)
		if len(packet) >= 2 && packet[len(packet)-2] == 0x0D && packet[len(packet)-1] == 0x0A {
			break
		}
	}
	return packet, nil
}
func (p *Parser) processMessage(client *Client, msg Message) {

	switch msg.Type {
	case MSG_LOGIN:
		p.handleLoginMessage(client, msg.Data)
	case MSG_POSITION_DATA:
		p.handlePositioningMessage(msg.Data)
	case MSG_GPS:
		p.handleGPSMessage(msg.Data)
	case MSG_ALARM:
		p.handleAlarmMessage(msg.Data)
	case MSG_ALARM_2:
		p.handleAlarmMessage(msg.Data)
	case MSG_STATUS:
		p.handleStatusMessage(msg.Data)
	case MSG_STATUS_2:
		p.handleStatusMessage(msg.Data)
	case MSG_STRING:
		p.handleStringMessage(msg.Data)
	case MSG_STRING_2:
		p.handleStringMessage(msg.Data)

	default:
		//fmt.Println("Unknown or unhandled packet type:", msg.Type)
	}
}
func decodeHexStruct(reader *bufio.Reader, v interface{}) error {
	line, err := reader.ReadString('\n')
	if err != nil {
		return err
	}

	hexString := strings.TrimSpace(line)
	decoded, err := hex.DecodeString(hexString)
	if err != nil {
		return err
	}

	return decodeStruct(decoded, v)
}

func decodeStruct(data []byte, v interface{}) error {
	if len(data) != binary.Size(v) {
		return fmt.Errorf("incorrect data length")
	}
	return binary.Read(bytes.NewReader(data), binary.BigEndian, v)
}

// Function to convert BCD to IMEI
func bcdToIMEI(bcd []byte) string {
	imei := ""
	for i := 0; i < len(bcd); i++ {
		imei += fmt.Sprintf("%02X", bcd[i])
	}
	return imei
}
func getMCC(data []byte) int {

	return int(data[0])<<8 | int(data[1])
}
func getLAC(data []byte) int {
	return int(data[0])<<8 | int(data[1])
}
func getCellID(data []byte) int {

	return (int(data[0]) << 16) | (int(data[1]) << 8) | int(data[2])
}
func parseDateTime(data []byte) time.Time {
	year := int(data[0]) + 2000
	month := time.Month(data[1])
	day := int(data[2])
	hour := int(data[3])
	minute := int(data[4])
	second := int(data[5])
	return time.Date(year, month, day, hour, minute, second, 0, time.UTC)
}
func DecodeCourseStatus(data []byte) CourseStatus {
	status := data[0]
	courseByte := data[1]

	course := uint16(status&0x03)<<8 | uint16(courseByte)

	return CourseStatus{
		ACC:           (status & 0x80) != 0,
		Input2:        (status & 0x40) != 0,
		GPSFix:        (status & 0x10) != 0,
		LongitudeEast: (status & 0x08) == 0,
		LatitudeNorth: (status & 0x04) != 0,
		Course:        course,
		Direction:     utils.GetDirection(course),
	}
}

// ParseDirection parses the GPS direction bytes and returns the adjusted latitude and longitude signs
func ParseDirection(directionBytes []byte, latitude, longitude float64) (float64, float64, string) {
	if len(directionBytes) != 2 {
		panic("Direction bytes must be exactly 2 bytes")
	}
	// Extract the first and second byte
	firstByte := int(directionBytes[0])
	secondByte := uint16(directionBytes[1])
	// Parse bits in the first byte
	//for bit 3, 0 is longitude east and 1 is longitude west which is negative. For bit 2, 0 is latitude south which is negative and 1 is latitude north.
	isWest := (firstByte & 0x08) == 1  // Bit 3
	isSouth := (firstByte & 0x04) == 0 // Bit 2
	// Adjust latitude and longitude signs based on direction bits
	if isSouth {
		latitude = -latitude
	}
	if isWest {
		longitude = -longitude
	}
	direction := utils.GetDirection(secondByte)

	return latitude, longitude, direction
}
func ParseTerminalStatus(b byte) TerminalStatus {
	return TerminalStatus{
		OilCut:        (b>>7)&0x01 == 1,
		GPSPositioned: (b>>6)&0x01 == 1,
		Alarm:         decodeSimpleAlarm((b >> 3) & 0x07),
		PowerCharging: (b>>2)&0x01 == 1,
		ACCHigh:       (b>>1)&0x01 == 1,
		DefenseOn:     b&0x01 == 1,
	}
}

func decodeSimpleAlarm(code uint8) string {
	switch code {
	case 0:
		return "Normal"
	case 1:
		return "Vibration Alarm"
	case 2:
		return "Power Cut Alarm"
	case 3:
		return "Low Battery Alarm"
	case 4:
		return "SOS Call"
	default:
		return "Unknown"
	}
}

func decodeAlarmType(code byte) string {
	switch code {
	case 0x00:
		return "Normal (No alarm)"
	case 0x01:
		return "SOS"
	case 0x02:
		return "Power cut alarm"
	case 0x03:
		return "Vibration alarm"
	case 0x04:
		return "In fence alarm"
	case 0x05:
		return "Out fence alarm"
	case 0x06:
		return "Speeding alarm"
	case 0x09:
		return "Movement alarm"
	case 0x0A:
		return "Into GPS blind area alarm"
	case 0x0B:
		return "Out GPS blind area alarm"
	case 0x0C:
		return "Power on alarm"
	case 0x0E:
		return "External power low battery alarm"
	case 0x0F:
		return "External power low battery protection alarm"
	case 0x11:
		return "Power off alarm"
	case 0x13:
		return "Demolition alarm"
	case 0x14:
		return "Door alarm"
	case 0x15:
		return "Low-power shutdown alarm"
	case 0x2C:
		return "Collision alarm"
	case 0x2D:
		return "Flip alarm"
	case 0x2E:
		return "Sharp turn alarm"
	case 0x30:
		return "Harsh breaking alarm"
	case 0x29:
		return "Rapid acceleration alarm"
	default:
		return "Unknown alarm"
	}
}

func decodeLanguage(code byte) string {
	switch code {
	case 0x01:
		return "Chinese"
	case 0x02:
		return "English"
	default:
		return "Unknown"
	}
}

// calculateVoltage converts the raw voltage level byte to actual voltage in volts
func calculateVoltage(rawVoltage int) float64 {
	// According to GT06 protocol, divide by 100 to get actual voltage
	return float64(rawVoltage) / 100.0
}

// getSignalStrengthDescription provides a human-readable description of signal strength
func getSignalStrengthDescription(signalStrength int) string {
	// These thresholds are approximate and can be adjusted based on device behavior
	if signalStrength >= 200 {
		return "Excellent"
	} else if signalStrength >= 150 {
		return "Good"
	} else if signalStrength >= 100 {
		return "Fair"
	} else if signalStrength >= 50 {
		return "Poor"
	} else {
		return "Very Poor"
	}
}
func batteryPercentage(voltageLevel int) float64 {
	switch voltageLevel {
	case 0:
		return 0 // Dead
	case 1:
		return 10 // Very weak
	case 2:
		return 20 // Very low
	case 3:
		return 40 // Low, but working
	case 4:
		return 60 // Medium
	case 5:
		return 80 // High
	case 6:
		return 100 // Extreme high
	default:
		return 0
	}
}

func gsmSignalPercentage(gsmLevel int) float64 {
	switch gsmLevel {
	case 0:
		return 0 // No signal
	case 1:
		return 25 // Extreme weak
	case 2:
		return 50 // Weak
	case 3:
		return 75 // Good
	case 4:
		return 100 // Strong
	default:
		return 0
	}
}

// Function to handle login messages
func (p *Parser) handleLoginMessage(client *Client, data []byte) {
	// Extract IMEI from login message
	if len(data) < 16 {
		//log.Println("Invalid login message length")
		return
	}
	imeiBCD := data[4:12]
	imei := bcdToIMEI(imeiBCD)
	device, err := utils.FindDevice(imei)
	if err != nil {
		//fmt.Println("Device not found")
		return
	}
	// Add the client to the global clients slice and map
	mu.Lock()
	client.IMEI = imei
	client.Device = *device
	clients = append(clients, *client)
	clientMap[p.Conn] = imei
	mu.Unlock()
	utils.SetDeviceAsOnline(device)
	// Respond to login message
	p.respondToLogin(data)
	//p.processPendingCommands()
	go func() {
		slackService, err := service.NewSlackService()
		if err != nil {
			log.Printf("Failed to initialize Slack service: %v", err)
			return
		}
		hexString := hex.EncodeToString(data)
		if err := slackService.SendRawDataLog(*client.Device.Name, client.Device.DeviceId, hexString); err != nil {
			log.Printf("Failed to send Slack alert: %v", err)
		}
	}()
}

// Function to respond to login message
func (p *Parser) respondToLogin(data []byte) {
	// Construct response
	p.sendResponse(MSG_LOGIN, []byte{data[12], data[13]})
}

func (p *Parser) handlePositioningMessage(data []byte) {
	if len(data) < 39 {
		//log.Println("Invalid Positioning message length")
		return
	}
	gpsDataPacket := GpsDataPacket{
		StartByte:         data[0:2],
		PacketLength:      data[2:3],
		ProtocolNumber:    data[3:4],
		Date:              data[4:10],
		Satellites:        data[10:11],
		Latitude:          data[11:15],
		Longitude:         data[15:19],
		Speed:             data[19:20],
		Course:            data[20:22],
		Mcc:               data[22:24],
		Mnc:               data[24],
		Lac:               data[25:27],
		CellId:            data[27:30],
		Acc:               data[30],
		DataReportingMode: data[31],
		GPSRetransmission: data[32],
		SerialNumber:      data[33:35],
		ErrorCheck:        data[35:37],
		StopBit:           data[37:39],
	}
	if len(data) == 39 {
		//without mileage
		gpsDataPacket.SerialNumber = data[33:35]
		gpsDataPacket.ErrorCheck = data[35:37]
		gpsDataPacket.StopBit = data[37:39]
	} else {
		//with mileage
		gpsDataPacket.Mileage = data[33:37]
		gpsDataPacket.SerialNumber = data[37:38]
		gpsDataPacket.ErrorCheck = data[38:40]
		gpsDataPacket.StopBit = data[40:42]
	}
	// Extracting GPS data
	dateTime := gpsDataPacket.Date
	//satellites := uint8(gpsDataPacket.Satellites[0])
	latitude := binary.BigEndian.Uint32(gpsDataPacket.Latitude)
	longitude := binary.BigEndian.Uint32(gpsDataPacket.Longitude)
	speed := float64(gpsDataPacket.Speed[0])
	courseStatus := DecodeCourseStatus(gpsDataPacket.Course)
	//serialNumber := binary.BigEndian.Uint16(gpsDataPacket.SerialNumber)

	// Convert latitude and longitude to degrees
	lat := float64(latitude) / 30000.0 / 60.0
	long := float64(longitude) / 30000.0 / 60.0
	if !courseStatus.LatitudeNorth {
		lat = -lat
	}
	if !courseStatus.LongitudeEast {
		long = -long
	}
	// Extract date and time
	year := int(dateTime[0]) + 2000
	month := time.Month(dateTime[1])
	day := int(dateTime[2])
	hour := int(dateTime[3])
	minute := int(dateTime[4])
	second := int(dateTime[5])
	timestamp := time.Date(year, month, day, hour, minute, second, 0, time.UTC)
	mcc := getMCC(gpsDataPacket.Mcc)
	lac := getLAC(gpsDataPacket.Lac)
	cellId := getCellID(gpsDataPacket.CellId)
	mnc := int(gpsDataPacket.Mnc)
	ignitionStatus := courseStatus.ACC
	//save packet
	gpsData := service.GPSData{
		Timestamp:      timestamp,
		DeviceId:       p.Client.IMEI,
		Latitude:       lat,
		Longitude:      long,
		Speed:          speed,
		Direction:      courseStatus.Direction,
		Mcc:            strconv.Itoa(mcc),
		Mnc:            strconv.Itoa(mnc),
		Lac:            strconv.Itoa(lac),
		CellID:         strconv.Itoa(cellId),
		IgnitionStatus: ignitionStatus,
	}
	gpsData.SaveGPSData()
	p.processPendingCommands()
	go func() {
		slackService, err := service.NewSlackService()
		if err != nil {
			log.Printf("Failed to initialize Slack service: %v", err)
			return
		}
		hexString := hex.EncodeToString(data)
		if err := slackService.SendRawDataLog(*p.Client.Device.Name, p.Client.Device.DeviceId, hexString); err != nil {
			log.Printf("Failed to send Slack alert: %v", err)
		}
	}()
}
func (p *Parser) handleGPSMessage(data []byte) {
	if len(data) < 30 {
		//log.Println("Invalid GPS message length")
		return
	}
	// Extracting GPS data
	dateTime := data[4:10]
	latitude := binary.BigEndian.Uint32(data[11:15])
	longitude := binary.BigEndian.Uint32(data[15:19])
	speed := data[19]
	courseStatus := binary.BigEndian.Uint16(data[20:22])

	// Convert latitude and longitude to degrees
	latitudeDeg := float64(latitude) / 30000.0 / 60.0
	longitudeDeg := float64(longitude) / 30000.0 / 60.0

	// Extract date and time
	year := int(dateTime[0]) + 2000
	month := time.Month(dateTime[1])
	day := int(dateTime[2])
	hour := int(dateTime[3])
	minute := int(dateTime[4])
	second := int(dateTime[5])
	timestamp := time.Date(year, month, day, hour, minute, second, 0, time.UTC)

	gpsData := service.GPSData{
		Timestamp: timestamp,
		DeviceId:  p.Client.IMEI,
		Latitude:  latitudeDeg,
		Longitude: longitudeDeg,
		Speed:     float64(speed),
		Direction: fmt.Sprintf("%v", courseStatus),
	}
	gpsData.SaveGPSData()
}

func (p *Parser) sendResponse(msgType byte, content []byte) {
	// Construct response
	packetLength := 3 + len(content)
	response := []byte{0x78, 0x78, byte(packetLength), msgType}
	response = append(response, content...)
	crc := CRC16_ITU(response[2:])
	response = append(response, byte(crc>>8), byte(crc&0xFF), 0x0D, 0x0A)
	// Send response
	err := p.sendToDevice(response)
	if err != nil {
		//log.Println("Error responding to login message:", err)
	}
}

// Function to remove a client
func (p *Parser) removeClient() {
	mu.Lock()
	defer mu.Unlock()
	for i, client := range clients {
		if client.Conn == p.Conn {
			close(client.ExitChan)
			close(client.ResponseChan)
			close(client.CommandChan)
			utils.SetDeviceAsOffline(&client.Device)
			clients = append(clients[:i], clients[i+1:]...)
			break
		}
	}
	fmt.Println("GT06 Client disconnected with IMEI:", p.Client.IMEI)
}
func (p *Parser) processPendingCommands() {
	//go p.SetSpeedAlert()
	// Check if there are any pending commands for this device
	var commandLogs []models.CommandLog
	config.DB.Where("client_device_id = ? AND status = ?", p.Client.Device.Id, "pending").Find(&commandLogs)
	for _, commandLog := range commandLogs {
		// Process the command
		serialNumber := []byte{0x00, 0x01}
		var parameters map[string]interface{}
		commandContent := ""
		name := commandLog.Name
		switch name {
		case "cutoff_oil":
			commandContent = fmt.Sprintf("stop%v", *p.Client.Device.Password)
		case "enable_oil":
			commandContent = fmt.Sprintf("resume%v", *p.Client.Device.Password)
		case "speed_alert":
			err := json.Unmarshal([]byte(commandLog.Parameters), &parameters)
			if err != nil {
				//fmt.Println("Error unmarshalling custom command parameters:", err)
				commandLog.Status = "failed"
				config.DB.Save(&commandLog)
				return
			}

			// Safely convert speed parameter
			speed, ok := parameters["speed"].(float64)
			if !ok {
				//fmt.Println("Error: speed parameter is not a number")
				commandLog.Status = "failed"
				config.DB.Save(&commandLog)
				return
			}
			if speed < 50 {
				speed = 50
			}

			// Safely convert speed_duration parameter
			speedDuration, ok := parameters["speed_duration"].(float64)
			if !ok {
				//fmt.Println("Error: speed_duration parameter is not a number", speedDuration)
				commandLog.Status = "failed"
				config.DB.Save(&commandLog)
				return
			}
			if speedDuration < 50 {
				speedDuration = 50
			}
			commandContent = fmt.Sprintf("speed%v,%v,%v,0", *p.Client.Device.Password, int(speedDuration), int(speed))
		case "speed_alert_off":
			commandContent = fmt.Sprintf("nospeed%v", *p.Client.Device.Password)
		case "geofence":
			err := json.Unmarshal([]byte(commandLog.Parameters), &parameters)
			if err != nil {
				//fmt.Println("Error unmarshalling custom command parameters:", err)
				commandLog.Status = "failed"
				config.DB.Save(&commandLog)
				return
			}
			commandContent = fmt.Sprintf("stockadd%v 17.91840,S,30.94764,E,500", *p.Client.Device.Password)
		case "geofence_off":
			commandContent = fmt.Sprintf("nostockade%v", *p.Client.Device.Password)
		case "custom":
			err := json.Unmarshal([]byte(commandLog.Parameters), &parameters)
			if err != nil {
				//fmt.Println("Error unmarshalling custom command parameters:", err)
				commandLog.Status = "failed"
				config.DB.Save(&commandLog)
				return
			}
			commandContent = parameters["command"].(string)

		}

		if commandContent != "" {
			data := BuildCommand(commandContent, uint32(commandLog.Id), serialNumber)
			go p.sendCommand(&commandLog, data)
		}
	}
}

func (p *Parser) handleAlarmMessage(data []byte) {
	//fmt.Println("Received alarm message:", data, "length:", len(data))
	if len(data) > 40 {
		timestamp := parseDateTime(data[4:10])
		//alarmType := data[10]
		satellites := int(data[10])
		latitude := binary.BigEndian.Uint32(data[11:15])
		longitude := binary.BigEndian.Uint32(data[15:19])
		speed := float64(data[19])
		lat := float64(latitude) / 30000.0 / 60.0
		long := float64(longitude) / 30000.0 / 60.0
		courseStatus := DecodeCourseStatus(data[20:22])
		if !courseStatus.LatitudeNorth {
			lat = -lat
		}
		if !courseStatus.LongitudeEast {
			long = -long
		}
		lbsLength := int(data[22])
		mcc := getMCC(data[23:25])
		mnc := int(data[25])
		lac := getLAC(data[26:28])
		cellId := getCellID(data[28:31])

		// Get raw voltage level and signal strength values
		rawVoltageLevel := int(data[32])
		rawSignalStrength := int(data[33])

		// Calculate actual voltage and get signal strength description
		//actualVoltage := calculateVoltage(rawVoltageLevel)
		//signalStrengthDesc := getSignalStrengthDescription(rawSignalStrength)

		terminalStatus := ParseTerminalStatus(data[31])
		terminalStatus.AlarmType = decodeAlarmType(data[34])
		terminalStatus.Language = decodeLanguage(data[35])
		terminalStatus.Battery = batteryPercentage(rawVoltageLevel)
		terminalStatus.GSMSignal = gsmSignalPercentage(rawSignalStrength)
		terminalStatus.Lac = lac
		terminalStatus.CellId = cellId
		terminalStatus.Mcc = mcc
		terminalStatus.Mnc = mnc
		terminalStatus.Timestamp = timestamp
		terminalStatus.Satellites = satellites
		terminalStatus.Speed = speed
		terminalStatus.Direction = courseStatus.Direction
		terminalStatus.LBSLength = lbsLength
		terminalStatus.Latitude = lat
		terminalStatus.Longitude = long
		//save Alerts
		// Marshal terminalStatus to JSON
		additionalData, err := json.Marshal(terminalStatus)
		if err != nil {
			//fmt.Println("Error marshalling terminal status:", err)
			return
		}
		alert := models.Alert{
			ClientDeviceId: p.Client.Device.Id,
			DeviceId:       &p.Client.IMEI,
			AlertType:      terminalStatus.AlarmType,
			AlertName:      &terminalStatus.AlarmType,
			AlertTimestamp: timestamp,
			Speed:          &speed,
			Direction:      &courseStatus.Direction,
			AdditionalData: additionalData,
		}
		//save alert
		config.DB.Save(&alert)
		// Send Slack notification asynchronously
		go func() {
			slackService, err := service.NewSlackService()
			if err != nil {
				log.Printf("Failed to initialize Slack service: %v", err)
				return
			}

			if err := slackService.SendAlert(&alert, &p.Client.Device); err != nil {
				fmt.Printf("Failed to send Slack alert: %v", err)
			}
		}()
		//fmt.Println("Terminal status:", terminalStatus, "voltage:", actualVoltage, "V (raw:", rawVoltageLevel, "), signal strength:", rawSignalStrength, "(", signalStrengthDesc, ")")

		// Save voltage and signal strength to the database
		err = utils.UpdateDeviceStatus(p.Client.IMEI, terminalStatus.Battery, terminalStatus.GSMSignal)
		if err != nil {
			//fmt.Println("Error updating device status:", err)
		}
	}
}

func (p *Parser) handleStatusMessage(data []byte) {
	//fmt.Println("Received status message:", data)
	if len(data) < 15 {
		//fmt.Println("Invalid status message length")
		return
	}
	serial := data[len(data)-4 : len(data)-2]
	terminalStatus := ParseTerminalStatus(data[4])
	terminalStatus.Battery = batteryPercentage(int(data[5]))
	terminalStatus.GSMSignal = gsmSignalPercentage(int(data[6]))
	// Save voltage and signal strength to the database
	err := utils.UpdateDeviceStatus(p.Client.IMEI, terminalStatus.Battery, terminalStatus.GSMSignal)
	if err != nil {
		//fmt.Println("Error updating device status:", err)
	}
	// Respond to a status message
	p.sendResponse(MSG_STATUS, serial)

	// Process any pending commands
	p.processPendingCommands()
}

func (p *Parser) handleStringMessage(data []byte) {
	if len(data) > 16 {
		serverFlag := data[5:9]
		responseStr := string(data[9 : len(data)-8])
		//fmt.Println("string message:", responseStr)
		// Check if the response contains "failed" or "FAIL"
		commandLog := models.CommandLog{}
		config.DB.Where("id = ?", binary.BigEndian.Uint32(serverFlag)).First(&commandLog)
		commandLog.Description = &responseStr
		if strings.Contains(strings.ToLower(responseStr), "fail") || strings.Contains(strings.ToLower(responseStr), "failed") {
			commandLog.Status = "failed"
		} else {
			commandLog.Status = "completed"
		}
		config.DB.Save(&commandLog)
	}

}
func (p *Parser) SetSpeedAlert() {
	var commandLog models.CommandLog
	config.DB.Where("id = ?", 39).First(&commandLog)
	//fmt.Println("Speed Alert Command processing")
	responseChan := make(chan []byte)
	var parameters map[string]interface{}
	err := json.Unmarshal([]byte(commandLog.Parameters), &parameters)
	if err != nil {
		//fmt.Println("Error unmarshalling parameters:", err)
		commandLog.Status = "failed"
		config.DB.Save(&commandLog)
		return
	}
	//fmt.Println("Speed Alert Command parameters", parameters)
	speed, ok := parameters["speed"].(float64)
	if !ok {
		//fmt.Println("Error: speed parameter is not a number")
		commandLog.Status = "failed"
		config.DB.Save(&commandLog)
		return
	}
	if speed < 50 {
		speed = 50
	}

	// Safely convert speed_duration parameter
	speedDuration, ok := parameters["speed_duration"].(float64)
	if !ok {
		//fmt.Println("Error: speed_duration parameter is not a number", speedDuration)
		commandLog.Status = "failed"
		config.DB.Save(&commandLog)
		return
	}
	if speedDuration < 50 {
		speedDuration = 50
	}
	commandContent := fmt.Sprintf("speed%v,%v,%v,0", *p.Client.Device.Password, int(speedDuration), int(speed))
	//fmt.Println("Speed Alert Command Content", commandContent)
	serialNumber := []byte{0x00, 0x01}
	data := BuildCommand(commandContent, uint32(commandLog.Id), serialNumber)
	p.Client.CommandChan <- Command{
		Data:         data,
		ResponseChan: responseChan,
		Timeout:      60 * time.Second,
	}

	commandContent = fmt.Sprintf("stockadd%v 17.91840,S,30.94764,E,500", *p.Client.Device.Password)
	data = BuildCommand(commandContent, uint32(commandLog.Id), serialNumber)
	p.Client.CommandChan <- Command{
		Data:         data,
		ResponseChan: responseChan,
		Timeout:      60 * time.Second,
	}

	for {
		select {
		case response := <-responseChan:
			fmt.Println("Received response from speed alert command", response)
			//return
		}

	}

}

// BuildCommand constructs a GT06-compatible command.
func BuildCommand(commandContent string, serverFlag uint32, serialNumber []byte) []byte {

	commandBytes := []byte(commandContent)
	// Create a 4-byte buffer for the server flag
	serverFlagBytes := make([]byte, 4)
	binary.BigEndian.PutUint32(serverFlagBytes, serverFlag)
	informationPacket := serverFlagBytes
	informationPacket = append(informationPacket, commandBytes...)
	//prepend command length
	informationPacket = append([]byte{byte(len(informationPacket))}, informationPacket...)
	informationPacket = append(informationPacket, 0x00, 0x02)
	length := byte(5 + len(informationPacket)) // Packet Length = Protocol + Content + Serial + Checksum
	// Build packet
	packet := []byte{0x78, 0x78, length, MSG_COMMAND}
	packet = append(packet, informationPacket...)
	packet = append(packet, serialNumber...)
	crc := CRC16_ITU(packet[2:])
	packet = append(packet, byte(crc>>8), byte(crc&0xFF), 0x0D, 0x0A)

	return packet
}
func (p *Parser) sendCommand(commandLog *models.CommandLog, commandContent []byte) {
	responseChan := make(chan []byte)
	commandLog.Status = "processing"
	config.DB.Save(commandLog)
	//fmt.Println("Sending gt06 command", commandContent)
	p.Client.CommandChan <- Command{
		Data:         commandContent,
		ResponseChan: responseChan,
		Timeout:      60 * time.Second,
	}
	for {
		select {
		case response := <-responseChan:
			//fmt.Println("Received response from gt06 custom command", response)
			p.handleStringMessage(response)
			close(responseChan)
			return
		}

	}
}
func (p *Parser) sendToDevice(data []byte) error {
	if p.Conn == nil {
		return errors.New("no active connection")
	}
	//fmt.Println("Sending data to device")
	mu.Lock()
	defer mu.Unlock()
	_, err := p.Conn.Write(data)
	if err != nil {
		return err
	}
	return nil
}
