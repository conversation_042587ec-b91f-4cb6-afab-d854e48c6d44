package models

import (
	"gorm.io/datatypes"
	"time"
)

type ChatbotSession struct {
	Id        uint64         `json:"id" gorm:"primaryKey;autoIncrement"`
	ClientId  *uint64        `json:"client_id" gorm:"index"`
	UserId    *uint64        `json:"user_id"`
	RecordId  string         `json:"record_id" gorm:"size:255;index"`
	BotType   string         `json:"bot_type" gorm:"size:50;default:'whatsapp';index"`
	Payload   datatypes.JSON `json:"payload" gorm:"type:json"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
}
