package models

import (
	"encoding/json"
	"time"
)

type JSONMap map[string]interface{}
type CommandLog struct {
	Id              uint            `json:"id" gorm:"primaryKey"`
	ClientDeviceId  uint            `json:"client_device_id" gorm:"index"`
	CreatedById     *uint           `json:"created_by_id"`
	Name            string          `json:"name"`
	CommandType     string          `json:"command_type" gorm:"default:'tcp'"`
	CommandContent  *string         `json:"command_content"`
	CommandResponse *string         `json:"command_response"`
	Description     *string         `json:"description" gorm:"type:text"`
	Parameters      json.RawMessage `json:"parameters" gorm:"type:json;"`
	Status          string          `json:"status" gorm:"default:'pending'"`
	Recurring       *bool           `json:"recurring" gorm:"default:false"`
	RecurType       *string         `json:"recur_type" gorm:"default:'day'"`
	RecurFrequency  *int            `json:"recur_frequency" gorm:"default:1"`
	RecurLastDate   *time.Time      `json:"recur_last_date" gorm:"type:date"`
	RecurNextDate   *time.Time      `json:"recur_next_date" gorm:"type:date"`
	ScheduledAt     *time.Time      `json:"scheduled_at"`
	ExecutedAt      *time.Time      `json:"executed_at"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
	ClientDevice    ClientDevice    `json:"client_device"`
}
type CreateCommandLogRequest struct {
	ClientDeviceId  *uint           `json:"client_device_id,omitempty"`
	FleetId         *uint           `json:"fleet_id,omitempty"`
	DriverId        *uint           `json:"driver_id,omitempty"`
	ClientId        *uint           `json:"client_id,omitempty"`
	DeviceTypeId    *uint           `json:"device_type_id,omitempty"`
	Name            string          `json:"name" binding:"required"`
	SelectType      string          `json:"select_type" binding:"required"`
	CommandContent  *string         `json:"command_content,omitempty"`
	CommandResponse *string         `json:"command_response,omitempty"`
	CommandType     string          `json:"command_type" binding:"required"`
	Parameters      json.RawMessage `json:"parameters,omitempty"`
	Recurring       *bool           `json:"recurring,omitempty"`
	RecurType       *string         `json:"recur_type,omitempty"`
	RecurFrequency  *int            `json:"recur_frequency,omitempty"`
	RecurLastDate   *time.Time      `json:"recur_last_date,omitempty"`
	RecurNextDate   *time.Time      `json:"recur_next_date,omitempty"`
	ScheduledAt     *time.Time      `json:"scheduled_at,omitempty"`
}
type UpdateCommandLogRequest struct {
	Status          *string         `json:"status"`
	Parameters      json.RawMessage `json:"parameters"`
	CommandContent  *string         `json:"command_content,omitempty"`
	CommandResponse *string         `json:"command_response,omitempty"`
	Recurring       *bool           `json:"recurring"`
	RecurType       *string         `json:"recur_type"`
	RecurFrequency  *int            `json:"recur_frequency"`
	RecurLastDate   *time.Time      `json:"recur_last_date"`
	RecurNextDate   *time.Time      `json:"recur_next_date"`
	ScheduledAt     *time.Time      `json:"scheduled_at"`
}
