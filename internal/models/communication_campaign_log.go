package models

import (
	"time"
)

type CommunicationCampaignLog struct {
	Id           uint      `json:"id" gorm:"primaryKey"`
	ClientId     *uint     `json:"client_id" gorm:"index"`
	Client       *Client   `json:"client,omitempty"`
	To           string    `json:"to" gorm:"type:text"`            // Recipient (phone number, email, etc.)
	CampaignType string    `json:"campaign_type" gorm:"index"`     // sms, whatsapp, email
	Message      string    `json:"message" gorm:"type:text"`       // The actual message content
	Status       string    `json:"status" gorm:"default:'sent'"`   // sent, failed, pending
	ErrorMessage *string   `json:"error_message" gorm:"type:text"` // Error details if failed
	Metadata     *string   `json:"metadata" gorm:"type:text"`      // Additional JSON data
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// CommunicationCampaignLogRequest for creating logs
type CommunicationCampaignLogRequest struct {
	ClientId     *uint   `json:"client_id,omitempty"`
	To           string  `json:"to" binding:"required"`
	CampaignType string  `json:"campaign_type" binding:"required"`
	Message      string  `json:"message" binding:"required"`
	Status       string  `json:"status,omitempty"`
	ErrorMessage *string `json:"error_message,omitempty"`
	Metadata     *string `json:"metadata,omitempty"`
}

// CommunicationCampaignLogFilterRequest for filtering logs
type CommunicationCampaignLogFilterRequest struct {
	ClientId     *uint   `json:"client_id,omitempty"`
	CampaignType *string `json:"campaign_type,omitempty"`
	Status       *string `json:"status,omitempty"`
	StartDate    *string `json:"start_date,omitempty"`
	EndDate      *string `json:"end_date,omitempty"`
	Page         *int    `json:"page,omitempty"`
	Limit        *int    `json:"limit,omitempty"`
}

// CommunicationCampaignLogStatistics for analytics
type CommunicationCampaignLogStatistics struct {
	TotalMessages   int64   `json:"total_messages"`
	SmsCount        int64   `json:"sms_count"`
	WhatsappCount   int64   `json:"whatsapp_count"`
	EmailCount      int64   `json:"email_count"`
	SuccessfulCount int64   `json:"successful_count"`
	FailedCount     int64   `json:"failed_count"`
	TotalCost       float64 `json:"total_cost,omitempty"` // For future cost tracking
}

// CampaignType constants
const (
	CampaignTypeSMS      = "sms"
	CampaignTypeWhatsApp = "whatsapp"
	CampaignTypeEmail    = "email"
)

// Status constants
const (
	StatusSent    = "sent"
	StatusFailed  = "failed"
	StatusPending = "pending"
)
