package models

type ClientUser struct {
	Id       uint `json:"id" gorm:"primaryKey"`
	ClientId uint `json:"client_id" gorm:"not null"`
	UserId   uint `json:"user_id" gorm:"not null"`
}
type CreateClientUserRequest struct {
	ExistingUser    bool    `json:"existing_user" binding:"required"`
	UserId          *uint   `json:"user_id"`
	ClientId        *uint   `json:"client_id" binding:"required"`
	Name            *string `json:"name"`
	Email           *string `json:"email"`
	Password        *string `json:"password,omitempty"`
	Username        *string `json:"username,omitempty"`
	Gender          *string `json:"gender,omitempty"`
	TelegramUserId  *string `json:"telegramUserId,omitempty"`
	SlackWebhookUrl *string `json:"slack_webhook_url,omitempty"`
	Status          *string `json:"status,omitempty"`
	Description     *string `json:"description,omitempty"`
}
