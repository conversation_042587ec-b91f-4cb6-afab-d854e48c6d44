package models

import "time"

type DeviceShareToken struct {
	Id             uint         `json:"id" gorm:"primaryKey"`
	ClientDeviceId uint         `json:"client_device_id" gorm:"index"`
	ClientDevice   ClientDevice `json:"client_device"`
	Token          string       `json:"token" gorm:"unique;not null"`
	ExpiresAt      time.Time    `json:"expires_at"`
	CreatedById    uint         `json:"created_by_id"`
	CreatedBy      User         `json:"created_by"`
	Status         string       `json:"status" gorm:"default:'active'"` // active, revoked, expired
	CreatedAt      time.Time    `json:"created_at"`
	UpdatedAt      time.Time    `json:"updated_at"`
}

type CreateDeviceShareTokenRequest struct {
	ExpiresAt time.Time `json:"expires_at" binding:"required"`
}

type DeviceShareTokenResponse struct {
	Id             uint      `json:"id"`
	ClientDeviceId uint      `json:"client_device_id"`
	DeviceName     string    `json:"device_name"`
	Token          string    `json:"token"`
	ExpiresAt      time.Time `json:"expires_at"`
	Status         string    `json:"status"`
	CreatedAt      time.Time `json:"created_at"`
	ShareUrl       string    `json:"share_url"`
}
