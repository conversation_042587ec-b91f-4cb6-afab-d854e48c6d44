package models

import (
	"time"
)

type Driver struct {
	Id                uint       `json:"id" gorm:"primaryKey"`
	CreatedById       uint       `json:"created_by_id"`
	ClientId          uint       `json:"client_id" gorm:"index"`
	UserId            *uint      `json:"user_id" gorm:"index"`
	FleetId           *uint      `json:"fleet_id" gorm:"index"`
	CountryId         *uint      `json:"country_id" gorm:"index"`
	Name              string     `json:"name"`
	Dob               *time.Time `json:"dob" gorm:"type:date"`
	Gender            *string    `json:"gender"`
	BloodType         *string    `json:"blood_type"`
	DriverLicenseNo   *string    `json:"driver_license_no"`
	LicenseExpiryDate *time.Time `json:"license_expiry_date" gorm:"type:date"`
	PhoneNumber       *string    `json:"phone_number"`
	Email             *string    `json:"email"`
	Address           *string    `json:"address" gorm:"type:text"`
	Rfid              *string    `json:"rfid"`
	Description       *string    `json:"description" gorm:"type:text"`
	Status            string     `json:"status" gorm:"default:'active'"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`

	// Relations
	Client  Client   `json:"client"`
	User    *User    `json:"user,omitempty"`
	Fleet   *Fleet   `json:"fleet,omitempty"`
	Country *Country `json:"country,omitempty"`
}

type CreateDriverRequest struct {
	ClientId          uint       `json:"client_id" binding:"required"`
	FleetId           *uint      `json:"fleet_id,omitempty"`
	CountryId         *uint      `json:"country_id,omitempty"`
	Name              string     `json:"name" binding:"required"`
	Dob               *time.Time `json:"dob,omitempty"`
	Gender            *string    `json:"gender,omitempty"`
	BloodType         *string    `json:"blood_type,omitempty"`
	DriverLicenseNo   *string    `json:"driver_license_no,omitempty"`
	LicenseExpiryDate *time.Time `json:"license_expiry_date,omitempty"`
	PhoneNumber       *string    `json:"phone_number,omitempty"`
	Email             *string    `json:"email,omitempty"`
	Address           *string    `json:"address,omitempty"`
	Rfid              *string    `json:"rfid,omitempty"`
	Description       *string    `json:"description,omitempty"`
	Status            string     `json:"status" binding:"required"`
}

type UpdateDriverRequest struct {
	FleetId           *uint      `json:"fleet_id,omitempty"`
	CountryId         *uint      `json:"country_id,omitempty"`
	Name              string     `json:"name" binding:"required"`
	Dob               *time.Time `json:"dob,omitempty"`
	Gender            *string    `json:"gender,omitempty"`
	BloodType         *string    `json:"blood_type,omitempty"`
	DriverLicenseNo   *string    `json:"driver_license_no,omitempty"`
	LicenseExpiryDate *time.Time `json:"license_expiry_date,omitempty"`
	PhoneNumber       *string    `json:"phone_number,omitempty"`
	Email             *string    `json:"email,omitempty"`
	Address           *string    `json:"address,omitempty"`
	Rfid              *string    `json:"rfid,omitempty"`
	Description       *string    `json:"description,omitempty"`
	Status            string     `json:"status" binding:"required"`
}
