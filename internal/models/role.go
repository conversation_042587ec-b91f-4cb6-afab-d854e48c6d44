package models

import "time"

type Role struct {
	Id          uint      `json:"id" gorm:"primaryKey"`
	ClientId    *uint     `json:"client_id"`
	Name        string    `json:"name" gorm:"not null"`
	RoleType    *string   `json:"role_type" gorm:"default:'backend'"`
	Description *string   `json:"description"`
	IsSystem    *bool     `json:"is_system" gorm:"default:false"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}
type RoleRequest struct {
	ClientId    *uint   `json:"client_id,omitempty"`
	Name        string  `json:"name"  binding:"required"`
	RoleType    *string `json:"role_type,omitempty"`
	Description *string `json:"description,omitempty"`
}
