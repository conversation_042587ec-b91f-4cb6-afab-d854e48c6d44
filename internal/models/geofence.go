package models

import (
	"time"
)

type Geofence struct {
	Id             uint       `json:"id" gorm:"primaryKey"`
	ClientId       uint       `json:"client_id" gorm:"index"`
	Name           string     `json:"name"`
	GeofenceType   string     `json:"geofence_type" gorm:"default:'circle'"` // circle, polygon, rectangle

	// Scope definition - where this geofence applies
	AppliesTo      string     `json:"applies_to" gorm:"default:'client'"` // client, fleet, device
	FleetId        *uint      `json:"fleet_id" gorm:"index"`
	ClientDeviceId *uint      `json:"client_device_id" gorm:"index"`
	DeviceId       string     `json:"device_id"` // for backward compatibility and quick lookup

	// Event triggers - what events to generate
	TriggerEvents  string     `json:"trigger_events" gorm:"default:'both'"` // entry, exit, both

	// Circle-specific fields
	Radius         *float64   `json:"radius,omitempty"` // in meters, required for circle type
	Latitude       *float64   `json:"latitude,omitempty"` // center latitude for circle
	Longitude      *float64   `json:"longitude,omitempty"` // center longitude for circle

	// Polygon/rectangle fields
	Coordinates    *string    `json:"coordinates,omitempty" gorm:"type:text"` // JSON array of coordinates for polygon/rectangle

	Status         string     `json:"status" gorm:"default:'active'"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`

	// Relations
	Client         Client       `json:"client,omitempty"`
	Fleet          Fleet        `json:"fleet,omitempty"`
	ClientDevice   ClientDevice `json:"client_device,omitempty"`
}

type CreateGeofenceRequest struct {
	Name           string  `json:"name" binding:"required"`
	GeofenceType   string  `json:"geofence_type"` // defaults to "circle" if not provided

	// Scope definition - where this geofence applies
	AppliesTo      string  `json:"applies_to" binding:"required"` // client, fleet, device
	FleetId        *uint   `json:"fleet_id"`        // required if applies_to = "fleet"
	ClientDeviceId *uint   `json:"client_device_id"` // required if applies_to = "device"
	DeviceId       string  `json:"device_id"`       // optional, for backward compatibility

	// Event triggers - what events to generate
	TriggerEvents  string  `json:"trigger_events"`  // entry, exit, both (defaults to "both")

	// Circle-specific fields
	Radius         *float64 `json:"radius,omitempty"` // required for circle type
	Latitude       *float64 `json:"latitude,omitempty"` // required for circle type
	Longitude      *float64 `json:"longitude,omitempty"` // required for circle type

	// Polygon/rectangle fields
	Coordinates    *string  `json:"coordinates,omitempty"` // JSON array for polygon/rectangle

	Status         string  `json:"status"`
}

type UpdateGeofenceRequest struct {
	Name         string   `json:"name"`
	GeofenceType string   `json:"geofence_type"`

	// Scope definition
	AppliesTo      string  `json:"applies_to"`
	FleetId        *uint   `json:"fleet_id"`
	ClientDeviceId *uint   `json:"client_device_id"`
	DeviceId       string  `json:"device_id"`

	// Event triggers
	TriggerEvents  string  `json:"trigger_events"`

	// Circle-specific fields
	Radius       *float64 `json:"radius,omitempty"`
	Latitude     *float64 `json:"latitude,omitempty"`
	Longitude    *float64 `json:"longitude,omitempty"`

	// Polygon/rectangle fields
	Coordinates  *string  `json:"coordinates,omitempty"`

	Status       string   `json:"status"`
}

type GeofenceEvent struct {
	Id             uint      `json:"id" gorm:"primaryKey"`
	ClientDeviceId uint      `json:"client_device_id" gorm:"index"`
	GeofenceId     uint      `json:"geofence_id" gorm:"index"`
	EventType      string    `json:"event_type"` // "entry" or "exit"
	EventTimestamp time.Time `json:"event_timestamp"`
	Latitude       float64   `json:"latitude"`
	Longitude      float64   `json:"longitude"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	Geofence       Geofence  `json:"geofence,omitempty"`
	ClientDevice   ClientDevice `json:"client_device,omitempty"`
}
