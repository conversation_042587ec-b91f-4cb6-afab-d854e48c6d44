package models

import (
	"time"
)

type DriverDeviceAssignment struct {
	Id             uint       `json:"id" gorm:"primaryKey"`
	CreatedById    uint       `json:"created_by_id"`
	DriverId       uint       `json:"driver_id" gorm:"index"`
	ClientDeviceId uint       `json:"client_device_id" gorm:"index"`
	AssignmentDate time.Time  `json:"assignment_date" gorm:"type:date;index"`
	StartTime      *time.Time `json:"start_time"`
	EndTime        *time.Time `json:"end_time"`
	Status         string     `json:"status" gorm:"default:'active'"` // active, completed, cancelled
	Notes          *string    `json:"notes" gorm:"type:text"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`

	// Relations
	Driver       Driver       `json:"driver"`
	ClientDevice ClientDevice `json:"client_device"`
}

type CreateDriverDeviceAssignmentRequest struct {
	DriverId       uint       `json:"driver_id" binding:"required"`
	ClientDeviceId uint       `json:"client_device_id" binding:"required"`
	AssignmentDate time.Time  `json:"assignment_date" binding:"required"`
	StartTime      *time.Time `json:"start_time,omitempty"`
	EndTime        *time.Time `json:"end_time,omitempty"`
	Status         string     `json:"status" binding:"required"`
	Notes          *string    `json:"notes,omitempty"`
}

type UpdateDriverDeviceAssignmentRequest struct {
	DriverId       uint       `json:"driver_id" binding:"required"`
	ClientDeviceId uint       `json:"client_device_id" binding:"required"`
	AssignmentDate time.Time  `json:"assignment_date" binding:"required"`
	StartTime      *time.Time `json:"start_time,omitempty"`
	EndTime        *time.Time `json:"end_time,omitempty"`
	Status         string     `json:"status" binding:"required"`
	Notes          *string    `json:"notes,omitempty"`
}

type CreateDriverDeviceAssignmentByRfidRequest struct {
	DriverRfid     string     `json:"driver_rfid" binding:"required"`
	ClientDeviceId uint       `json:"client_device_id" binding:"required"`
	AssignmentDate time.Time  `json:"assignment_date" binding:"required"`
	StartTime      *time.Time `json:"start_time,omitempty"`
	EndTime        *time.Time `json:"end_time,omitempty"`
	Status         string     `json:"status" binding:"required"`
	Notes          *string    `json:"notes,omitempty"`
}
