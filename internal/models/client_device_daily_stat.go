package models

import "time"

type ClientDeviceDailyStat struct {
	Id             uint         `json:"id" gorm:"primaryKey"`
	ClientDeviceId uint         `json:"client_device_id" gorm:"uniqueIndex:idx_device_date"`
	Date           time.Time    `json:"date" gorm:"type:date;uniqueIndex:idx_device_date"`
	ClientDevice   ClientDevice `json:"client_device"`
	Distance       float64      `json:"distance"`
	Trips          uint         `json:"trips"`
	Alerts         uint         `json:"alerts"`
	Warnings       uint         `json:"warnings"`
	Critical       uint         `json:"critical"`
	CreatedAt      time.Time    `json:"created_at"`
	UpdatedAt      time.Time    `json:"updated_at"`
}
