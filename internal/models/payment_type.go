package models

import (
	"encoding/json"
	"time"
)

type PaymentType struct {
	Id          uint             `json:"id"`
	CreatedById *uint            `json:"created_by_id"`
	Name        string           `json:"name"`
	SystemName  *string          `json:"system_name"`
	Description *string          `json:"description" gorm:"type:text"`
	IsCash      *bool            `json:"is_cash" gorm:"default:false"`
	IsOnline    *bool            `json:"is_online" gorm:"default:false"`
	IsSystem    *bool            `json:"is_system" gorm:"default:false"`
	IsEft       *bool            `json:"is_eft" gorm:"default:false"`
	Active      bool             `json:"active" gorm:"default:true"`
	Position    *uint            `json:"position"`
	UniqueId    *string          `json:"unique_id"`
	Logo        *string          `json:"logo"`
	ReportColor *string          `json:"report_color"`
	Options     *json.RawMessage `json:"options" gorm:"type:json"`
	CreatedAt   *time.Time       `json:"created_at"`
	UpdatedAt   *time.Time       `json:"updated_at"`
}
type PaymentTypeRequest struct {
	Name        string           `json:"name" binding:"required"`
	SystemName  *string          `json:"system_name"`
	Description *string          `json:"description"`
	IsCash      *bool            `json:"is_cash"`
	IsOnline    *bool            `json:"is_online"`
	IsSystem    *bool            `json:"is_system"`
	IsEft       *bool            `json:"is_eft"`
	Active      bool             `json:"active"`
	Options     *json.RawMessage `json:"options"`
	Position    *uint            `json:"position"`
	UniqueId    *string          `json:"unique_id"`
	Logo        *string          `json:"logo"`
	ReportColor *string          `json:"report_color"`
}
