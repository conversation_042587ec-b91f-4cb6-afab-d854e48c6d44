package models

import (
	"encoding/json"
	"time"
)

type GPSData struct {
	Id             uint             `json:"id" gorm:"primaryKey"`
	ClientDeviceId *uint            `json:"client_device_id" gorm:"index"`
	ClientDevice   ClientDevice     `json:"client_device"`
	TripId         *uint            `json:"trip_id" gorm:"index"`
	GPSTimestamp   *time.Time       `json:"gps_timestamp"`
	DeviceId       string           `json:"device_id"`
	Latitude       float64          `json:"latitude"`
	Longitude      float64          `json:"longitude"`
	Altitude       *float64         `json:"altitude"`
	Speed          *float64         `json:"speed"`
	Temperature    *float64         `json:"temperature"`
	BatteryLevel   *float64         `json:"battery_level"`
	Direction      *string          `json:"direction"`
	VehicleStatus  *string          `json:"vehicle_status"`
	IgnitionStatus *bool            `json:"ignition_status"`
	CellId         *string          `json:"cell_id"`
	Mcc            *string          `json:"mcc"`
	Mnc            *string          `json:"mnc"`
	Lac            *string          `json:"lac"`
	Cid            *string          `json:"cid"`
	RawData        *string          `json:"raw_data" gorm:"type:text"`
	LocationName   *string          `json:"location_name" gorm:"type:text"`
	AdditionalData *json.RawMessage `json:"additional_data" gorm:"type:json"`
	CreatedAt      time.Time        `json:"created_at"`
	UpdatedAt      time.Time        `json:"updated_at"`
}
type GPSDataRequest struct {
	GPSTimestamp   *time.Time       `json:"gps_timestamp"`
	DeviceId       string           `json:"device_id" binding:"required"`
	Latitude       float64          `json:"latitude" binding:"required"`
	Longitude      float64          `json:"longitude" binding:"required"`
	Altitude       *float64         `json:"altitude,omitempty"`
	Speed          *float64         `json:"speed,omitempty"`
	Temperature    *float64         `json:"temperature,omitempty"`
	BatteryLevel   *float64         `json:"battery_level,omitempty"`
	Direction      *string          `json:"direction,omitempty"`
	VehicleStatus  *string          `json:"vehicle_status,omitempty"`
	IgnitionStatus *bool            `json:"ignition_status,omitempty"`
	CellId         *string          `json:"cell_id,omitempty"`
	Mcc            *string          `json:"mcc,omitempty"`
	Mnc            *string          `json:"mnc,omitempty"`
	Lac            *string          `json:"lac,omitempty"`
	Cid            *string          `json:"cid,omitempty"`
	RawData        *string          `json:"raw_data,omitempty"`
	AdditionalData *json.RawMessage `json:"additional_data,omitempty"`
}
type SearchGpsDataRequest struct {
	ClientDeviceId *uint     `json:"client_device_id" binding:"required"`
	StartDate      time.Time `json:"start_date" binding:"required"`
	EndDate        time.Time `json:"end_date" binding:"required"`
	Speed          *float64  `json:"speed,omitempty"`
}
