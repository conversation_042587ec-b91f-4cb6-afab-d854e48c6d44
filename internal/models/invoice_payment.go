package models

import "time"

type InvoicePayment struct {
	Id                 uint        `json:"id"`
	InvoiceId          uint        `json:"invoice_id"`
	PaymentTypeId      uint        `json:"payment_type_id"`
	CurrencyId         *uint       `json:"currency_id"`
	CreatedById        *uint       `json:"created_by_id"`
	TransId            *string     `json:"trans_id"`
	Amount             float64     `json:"amount"`
	BaseCurrencyAmount *float64    `json:"base_currency_amount"`
	Date               *time.Time  `json:"date"`
	Xrate              *float64    `json:"xrate" gorm:"default:1"`
	CreatedAt          *time.Time  `json:"created_at"`
	UpdatedAt          *time.Time  `json:"updated_at"`
	PaymentType        PaymentType `json:"payment_type"`
	Currency           Currency    `json:"currency"`
	Invoice            Invoice     `json:"invoice"`
}
type InvoicePaymentRequest struct {
	InvoiceId          uint       `json:"invoice_id" binding:"required"`
	PaymentTypeId      uint       `json:"payment_type_id" binding:"required"`
	CurrencyId         *uint      `json:"currency_id" binding:"required"`
	TransId            *string    `json:"trans_id,omitempty"`
	Amount             float64    `json:"amount" binding:"required"`
	BaseCurrencyAmount *float64   `json:"base_currency_amount,omitempty"`
	Date               *time.Time `json:"date" binding:"required"`
	Xrate              *float64   `json:"xrate,omitempty"`
}
