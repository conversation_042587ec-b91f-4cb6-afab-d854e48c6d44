package models

import (
	"time"
	"yotracker/config"
)

type Setting struct {
	Id           uint      `json:"id" gorm:"primaryKey"`
	Name         string    `json:"name"`
	SettingKey   string    `json:"setting_key" gorm:"unique"`
	SettingValue string    `json:"setting_value"`
	Category     *string   `json:"category" gorm:"type:varchar(255);default:general"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// AllowedSettings defines which settings can be retrieved by frontend users
var AllowedSettings = []string{
	"company_name",
	"company_address",
	"company_phone",
	"company_email",
	"company_website",
	"company_tel",
	"google_maps_api_key",
	"invoice_reference_prefix",
	"invoice_reference_format",
	"generate_invoice_before_days",
	"invoice_due_after_days",
	"invoice_upcoming_reminder_days",
	"invoice_terms_and_conditions",
	"invoice_edit_exchange_rate",
	"currency",
	"default_maps_provider",
	"slack_default_channel",
	"slack_alerts_enabled",
	"slack_alert_severity_filter",
	"slack_raw_data_enabled",
	"slack_raw_data_channel",
	"whatsapp_alerts_enabled",
	"sms_alerts_enabled",
	"email_alerts_enabled",
	"trip_detection_idle_timeout_minutes",
	"trip_detection_min_speed_threshold",
	"trip_detection_min_distance_meters",
	"trip_detection_lookahead_points",
	"trip_detection_batch_size",
}

// GetSetting returns the value of a setting by its key
func GetSetting(settingKey string) string {
	var setting Setting
	err := config.DB.Where("setting_key = ?", settingKey).First(&setting).Error
	if err != nil {
		// Return empty string if setting not found (don't log error)
		return ""
	}
	return setting.SettingValue
}

// GetSettingByKey returns the complete setting object by its key
func GetSettingByKey(settingKey string) Setting {
	var setting Setting
	config.DB.Where("setting_key = ?", settingKey).First(&setting)
	return setting
}

// GetAllowedSettings returns the list of settings that can be retrieved by frontend users
func GetAllowedSettings() []string {
	return AllowedSettings
}

// IsSettingAllowed checks if a setting key is in the allowed list
func IsSettingAllowed(settingKey string) bool {
	for _, allowedKey := range AllowedSettings {
		if allowedKey == settingKey {
			return true
		}
	}
	return false
}
