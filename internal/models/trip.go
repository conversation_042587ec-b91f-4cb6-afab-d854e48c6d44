package models

import (
	"time"
)

type Trip struct {
	Id             uint         `json:"id" gorm:"primaryKey"`
	ClientDeviceId uint         `json:"client_device_id" gorm:"index"`
	ClientDevice   ClientDevice `json:"client_device"`
	DriverId       *uint        `json:"driver_id" gorm:"index"`
	Driver         *Driver      `json:"driver,omitempty"`

	// Trip timing
	StartTime time.Time  `json:"start_time" gorm:"index"`
	EndTime   *time.Time `json:"end_time" gorm:"index"`
	Duration  *int       `json:"duration"` // Duration in seconds

	// Trip locations
	StartLatitude  float64  `json:"start_latitude"`
	StartLongitude float64  `json:"start_longitude"`
	StartLocation  *string  `json:"start_location" gorm:"type:text"`
	EndLatitude    *float64 `json:"end_latitude"`
	EndLongitude   *float64 `json:"end_longitude"`
	EndLocation    *string  `json:"end_location" gorm:"type:text"`

	// Trip metrics
	Distance float64  `json:"distance"`  // Distance in kilometers
	MaxSpeed *float64 `json:"max_speed"` // Maximum speed during trip
	AvgSpeed *float64 `json:"avg_speed"` // Average speed during trip
	IdleTime *int     `json:"idle_time"` // Total idle time in seconds

	// Trip status and metadata
	Status   string  `json:"status" gorm:"default:'active'"` // active, completed, cancelled
	TripType *string `json:"trip_type"`                      // business, personal, maintenance, etc.
	Notes    *string `json:"notes" gorm:"type:text"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type CreateTripRequest struct {
	ClientDeviceId uint      `json:"client_device_id" binding:"required"`
	DriverId       *uint     `json:"driver_id"`
	StartTime      time.Time `json:"start_time" binding:"required"`
	StartLatitude  float64   `json:"start_latitude" binding:"required"`
	StartLongitude float64   `json:"start_longitude" binding:"required"`
	StartLocation  *string   `json:"start_location"`
	TripType       *string   `json:"trip_type"`
	Notes          *string   `json:"notes"`
}

type UpdateTripRequest struct {
	DriverId     *uint      `json:"driver_id"`
	EndTime      *time.Time `json:"end_time"`
	EndLatitude  *float64   `json:"end_latitude"`
	EndLongitude *float64   `json:"end_longitude"`
	EndLocation  *string    `json:"end_location"`
	Distance     *float64   `json:"distance"`
	MaxSpeed     *float64   `json:"max_speed"`
	AvgSpeed     *float64   `json:"avg_speed"`
	IdleTime     *int       `json:"idle_time"`
	Duration     *int       `json:"duration"`
	Status       *string    `json:"status"`
	TripType     *string    `json:"trip_type"`
	Notes        *string    `json:"notes"`
}

type SearchTripsRequest struct {
	ClientDeviceId *uint      `json:"client_device_id"`
	DriverId       *uint      `json:"driver_id"`
	StartDate      *time.Time `json:"start_date"`
	EndDate        *time.Time `json:"end_date"`
	Status         *string    `json:"status"`
	TripType       *string    `json:"trip_type"`
	MinDistance    *float64   `json:"min_distance"`
	MaxDistance    *float64   `json:"max_distance"`
	MinDuration    *int       `json:"min_duration"`
	MaxDuration    *int       `json:"max_duration"`
}
