package models

type DeviceType struct {
	Id                        uint     `json:"id" gorm:"primaryKey"`
	ProtocolId                uint     `json:"protocol_id"`
	Protocol                  Protocol `json:"protocol"`
	Name                      string   `json:"name"`
	Active                    bool     `json:"active" gorm:"default:true"`
	Amount                    *float64 `json:"amount"`
	RealTimeTracking          *bool    `json:"real_time_tracking" gorm:"default:true"`
	PeriodicReporting         *bool    `json:"periodic_reporting" gorm:"default:true"`
	Geofencing                *bool    `json:"geofencing" gorm:"default:true"`
	SpeedAlerts               *bool    `json:"speed_alerts" gorm:"default:true"`
	SOSAlerts                 *bool    `json:"sos_alerts" gorm:"default:false"`
	BatteryAlerts             *bool    `json:"battery_alerts" gorm:"default:false"`
	RemoteCutoff              *bool    `json:"remote_cutoff" gorm:"default:true"`
	RemoteRestart             *bool    `json:"remote_restart" gorm:"default:false"`
	SleepMode                 *bool    `json:"sleep_mode" gorm:"default:false"`
	WakeUp                    *bool    `json:"wake_up" gorm:"default:false"`
	FirmwareUpdate            *bool    `json:"firmware_update" gorm:"default:false"`
	HistoricalDataLogging     *bool    `json:"historical_data_logging" gorm:"default:false"`
	DeviceStatusReporting     *bool    `json:"device_status_reporting" gorm:"default:false"`
	DiagnosticFunctions       *bool    `json:"diagnostic_functions" gorm:"default:false"`
	MultiNetworkSupport       *bool    `json:"multi_network_support" gorm:"default:false"`
	WeatherProofing           *bool    `json:"weather_proofing" gorm:"default:false"`
	ExtendedBatteryLife       *bool    `json:"extended_battery_life" gorm:"default:false"`
	MappingServiceIntegration *bool    `json:"mapping_service_integration" gorm:"default:false"`
	OTAConfiguration          *bool    `json:"ota_configuration" gorm:"default:false"`
	CustomCommands            *bool    `json:"custom_commands" gorm:"default:false"`
	LocationDataEncryption    *bool    `json:"location_data_encryption" gorm:"default:false"`
	VoiceCommunication        *bool    `json:"voice_communication" gorm:"default:false"`
	MotionDetection           *bool    `json:"motion_detection" gorm:"default:false"`
	TemperatureMonitoring     *bool    `json:"temperature_monitoring" gorm:"default:false"`
	TamperAlerts              *bool    `json:"tamper_alerts" gorm:"default:false"`
	CameraSupport             *bool    `json:"camera_support" gorm:"default:false"`
	AudioMonitoring           *bool    `json:"audio_monitoring" gorm:"default:false"`
	FuelMonitoring            *bool    `json:"fuel_monitoring" gorm:"default:false"`
	OBDIISupport              *bool    `json:"obdii_support" gorm:"default:false"`
	BluetoothConnectivity     *bool    `json:"bluetooth_connectivity" gorm:"default:false"`
	WifiConnectivity          *bool    `json:"wifi_connectivity" gorm:"default:false"`
	GNSSSupport               *bool    `json:"gnss_support" gorm:"default:false"`
}
type DeviceTypeRequest struct {
	ProtocolId                uint     `json:"protocol_id" binding:"required"`
	Name                      string   `json:"name"  binding:"required"`
	Amount                    *float64 `json:"amount,omitempty"`
	Active                    bool     `json:"active"`
	RealTimeTracking          *bool    `json:"real_time_tracking"`
	PeriodicReporting         *bool    `json:"periodic_reporting"`
	Geofencing                *bool    `json:"geofencing"`
	SpeedAlerts               *bool    `json:"speed_alerts"`
	SOSAlerts                 *bool    `json:"sos_alerts"`
	BatteryAlerts             *bool    `json:"battery_alerts"`
	RemoteRestart             *bool    `json:"remote_restart"`
	RemoteCutoff              *bool    `json:"remote_cutoff"`
	SleepMode                 *bool    `json:"sleep_mode"`
	WakeUp                    *bool    `json:"wake_up"`
	FirmwareUpdate            *bool    `json:"firmware_update"`
	HistoricalDataLogging     *bool    `json:"historical_data_logging"`
	DeviceStatusReporting     *bool    `json:"device_status_reporting"`
	DiagnosticFunctions       *bool    `json:"diagnostic_functions"`
	MultiNetworkSupport       *bool    `json:"multi_network_support"`
	WeatherProofing           *bool    `json:"weather_proofing"`
	ExtendedBatteryLife       *bool    `json:"extended_battery_life"`
	MappingServiceIntegration *bool    `json:"mapping_service_integration"`
	OTAConfiguration          *bool    `json:"ota_configuration"`
	CustomCommands            *bool    `json:"custom_commands"`
	LocationDataEncryption    *bool    `json:"location_data_encryption"`
	VoiceCommunication        *bool    `json:"voice_communication"`
	MotionDetection           *bool    `json:"motion_detection"`
	TemperatureMonitoring     *bool    `json:"temperature_monitoring"`
	TamperAlerts              *bool    `json:"tamper_alerts"`
	CameraSupport             *bool    `json:"camera_support"`
	AudioMonitoring           *bool    `json:"audio_monitoring"`
	FuelMonitoring            *bool    `json:"fuel_monitoring"`
	OBDIISupport              *bool    `json:"obdii_support"`
	BluetoothConnectivity     *bool    `json:"bluetooth_connectivity"`
	WifiConnectivity          *bool    `json:"wifi_connectivity"`
	GNSSSupport               *bool    `json:"gnss_support"`
}
