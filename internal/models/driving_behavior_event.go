package models

import (
	"time"
)

type DrivingBehaviorEvent struct {
	Id             uint         `json:"id" gorm:"primaryKey"`
	ClientDeviceId uint         `json:"client_device_id" gorm:"index"`
	ClientDevice   ClientDevice `json:"client_device"`
	TripId         *uint        `json:"trip_id" gorm:"index"`
	Trip           *Trip        `json:"trip,omitempty"`
	DriverId       *uint        `json:"driver_id" gorm:"index"`
	Driver         *Driver      `json:"driver,omitempty"`

	// Event details
	EventType string    `json:"event_type" gorm:"index"` // overspeed, harsh_braking, harsh_acceleration, harsh_cornering, fatigue_driving, rapid_lane_change, excessive_idling
	Timestamp time.Time `json:"timestamp" gorm:"index"`
	Latitude  float64   `json:"latitude"`
	Longitude float64   `json:"longitude"`

	// Event metrics
	Speed         *float64 `json:"speed"`          // Speed at time of event (km/h)
	Acceleration  *float64 `json:"acceleration"`   // Acceleration value (m/s²)
	BearingChange *float64 `json:"bearing_change"` // Bearing change for cornering events (degrees)
	Duration      *int     `json:"duration"`       // Duration for sustained events like idling (seconds)
	Severity      *float64 `json:"severity"`       // Severity score (1-10 scale)

	// Context
	LocationName *string  `json:"location_name"` // Human-readable location
	RoadType     *string  `json:"road_type"`     // highway, city, residential, etc.
	SpeedLimit   *float64 `json:"speed_limit"`   // Speed limit if available

	// Metadata
	ProcessedAt time.Time `json:"processed_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Driving behavior event types
const (
	EventTypeOverspeed         = "overspeed"
	EventTypeHarshBraking      = "harsh_braking"
	EventTypeHarshAcceleration = "harsh_acceleration"
	EventTypeHarshCornering    = "harsh_cornering"
	EventTypeFatigueDriving    = "fatigue_driving"
	EventTypeRapidLaneChange   = "rapid_lane_change"
	EventTypeExcessiveIdling   = "excessive_idling"
	EventTypeRecklessDriving   = "reckless_driving"
)

// Severity levels
const (
	SeverityLow      = 1.0
	SeverityMedium   = 5.0
	SeverityHigh     = 8.0
	SeverityCritical = 10.0
)

type CreateDrivingBehaviorEventRequest struct {
	ClientDeviceId uint      `json:"client_device_id" binding:"required"`
	TripId         *uint     `json:"trip_id"`
	DriverId       *uint     `json:"driver_id"`
	EventType      string    `json:"event_type" binding:"required"`
	Timestamp      time.Time `json:"timestamp" binding:"required"`
	Latitude       float64   `json:"latitude" binding:"required"`
	Longitude      float64   `json:"longitude" binding:"required"`
	Speed          *float64  `json:"speed"`
	Acceleration   *float64  `json:"acceleration"`
	BearingChange  *float64  `json:"bearing_change"`
	Duration       *int      `json:"duration"`
	Severity       *float64  `json:"severity"`
	LocationName   *string   `json:"location_name"`
	RoadType       *string   `json:"road_type"`
	SpeedLimit     *float64  `json:"speed_limit"`
}

type SearchDrivingBehaviorEventsRequest struct {
	ClientDeviceId *uint      `json:"client_device_id"`
	TripId         *uint      `json:"trip_id"`
	DriverId       *uint      `json:"driver_id"`
	EventType      *string    `json:"event_type"`
	StartDate      *time.Time `json:"start_date"`
	EndDate        *time.Time `json:"end_date"`
	MinSeverity    *float64   `json:"min_severity"`
	MaxSeverity    *float64   `json:"max_severity"`
	RoadType       *string    `json:"road_type"`
}

type DrivingBehaviorSummary struct {
	ClientDeviceId   uint           `json:"client_device_id"`
	DriverId         *uint          `json:"driver_id"`
	Period           string         `json:"period"` // daily, weekly, monthly
	TotalEvents      int            `json:"total_events"`
	EventsByType     map[string]int `json:"events_by_type"`
	EventsBySeverity map[string]int `json:"events_by_severity"`
	SafetyScore      float64        `json:"safety_score"` // 0-100 scale
	TotalDistance    float64        `json:"total_distance"`
	TotalDrivingTime int            `json:"total_driving_time"`
	EventsPerKm      float64        `json:"events_per_km"`
	EventsPerHour    float64        `json:"events_per_hour"`
}
