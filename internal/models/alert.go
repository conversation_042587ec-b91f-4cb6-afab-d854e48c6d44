package models

import (
	"encoding/json"
	"time"
)

type Alert struct {
	Id             uint            `json:"id" gorm:"primaryKey"`
	ClientDeviceId uint            `json:"client_device_id" gorm:"index"`
	DeviceId       *string         `json:"device_id"`
	AlertType      string          `json:"alert_type"`
	AlertName      *string         `json:"alert_name"`
	AlertLevel     *string         `json:"alert_level" gorm:"type:varchar(255);default:'warning'"`
	Message        *string         `json:"message" gorm:"type:text"`
	RawData        *string         `json:"raw_data" gorm:"type:text"`
	AdditionalData json.RawMessage `json:"additional_data" gorm:"type:json"`
	Speed          *float64        `json:"speed"`
	Direction      *string         `json:"direction"`
	AlertTimestamp time.Time       `json:"alert_timestamp"`
	Read           *bool           `json:"read" gorm:"default:false"`
	ReadAt         *time.Time      `json:"read_at"`
	CreatedAt      time.Time       `json:"created_at"`
	UpdatedAt      time.Time       `json:"updated_at"`
	ClientDevice   ClientDevice    `json:"client_device"`
}
type CreateAlertRequest struct {
	ClientDeviceId uint            `json:"client_device_id" binding:"required"`
	DeviceId       *string         `json:"device_id"`
	AlertType      string          `json:"alert_type" binding:"required"`
	AlertName      *string         `json:"alert_name"`
	AlertLevel     *string         `json:"alert_level"`
	Message        *string         `json:"message"`
	RawData        *string         `json:"raw_data"`
	AdditionalData json.RawMessage `json:"additional_data"`
	Speed          *float64        `json:"speed"`
	Direction      *string         `json:"direction"`
	AlertTimestamp time.Time       `json:"alert_timestamp"`
	Read           *bool           `json:"read"`
	ReadAt         *time.Time      `json:"read_at"`
}
type UpdateAlertRequest struct {
	Read   *bool      `json:"read"`
	ReadAt *time.Time `json:"read_at"`
}
