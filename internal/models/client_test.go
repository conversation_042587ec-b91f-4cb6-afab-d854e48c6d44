package models

import (
	"encoding/json"
	"testing"
)

func TestClient_GetWhatsappPhoneNumbers(t *testing.T) {
	client := &Client{}

	// Test empty/nil values
	numbers := client.GetWhatsappPhoneNumbers()
	if len(numbers) != 0 {
		t.<PERSON>rrorf("Expected empty slice for nil WhatsappPhoneNumber, got %v", numbers)
	}

	// Test single value (non-JSON)
	singleValue := "1234567890"
	client.WhatsappPhoneNumber = &singleValue
	numbers = client.GetWhatsappPhoneNumbers()
	if len(numbers) != 1 || numbers[0] != "1234567890" {
		t.<PERSON><PERSON><PERSON>("Expected [1234567890] for single value, got %v", numbers)
	}

	// Test JSON array
	jsonArray := `["1234567890", "0987654321", "+1234567890"]`
	client.WhatsappPhoneNumber = &jsonArray
	numbers = client.GetWhatsappPhoneNumbers()
	expected := []string{"1234567890", "0987654321", "+1234567890"}
	if len(numbers) != 3 {
		t.<PERSON><PERSON>("Expected 3 numbers, got %d", len(numbers))
	}
	for i, num := range expected {
		if numbers[i] != num {
			t.Errorf("Expected %s at position %d, got %s", num, i, numbers[i])
		}
	}
}

func TestClient_SetWhatsappPhoneNumbers(t *testing.T) {
	client := &Client{}

	// Test setting empty slice
	client.SetWhatsappPhoneNumbers([]string{})
	if client.WhatsappPhoneNumber != nil {
		t.Errorf("Expected nil for empty slice, got %v", client.WhatsappPhoneNumber)
	}

	// Test setting single number
	numbers := []string{"1234567890"}
	client.SetWhatsappPhoneNumbers(numbers)
	if client.WhatsappPhoneNumber == nil {
		t.Error("Expected non-nil WhatsappPhoneNumber")
	}

	var result []string
	err := json.Unmarshal([]byte(*client.WhatsappPhoneNumber), &result)
	if err != nil {
		t.Errorf("Failed to unmarshal JSON: %v", err)
	}
	if len(result) != 1 || result[0] != "1234567890" {
		t.Errorf("Expected [1234567890], got %v", result)
	}

	// Test setting multiple numbers
	numbers = []string{"1234567890", "0987654321", "+1234567890"}
	client.SetWhatsappPhoneNumbers(numbers)
	err = json.Unmarshal([]byte(*client.WhatsappPhoneNumber), &result)
	if err != nil {
		t.Errorf("Failed to unmarshal JSON: %v", err)
	}
	expected := []string{"1234567890", "0987654321", "+1234567890"}
	if len(result) != 3 {
		t.Errorf("Expected 3 numbers, got %d", len(result))
	}
	for i, num := range expected {
		if result[i] != num {
			t.Errorf("Expected %s at position %d, got %s", num, i, result[i])
		}
	}
}

func TestClient_GetSmsPhoneNumbers(t *testing.T) {
	client := &Client{}

	// Test empty/nil values
	numbers := client.GetSmsPhoneNumbers()
	if len(numbers) != 0 {
		t.Errorf("Expected empty slice for nil SmsPhoneNumber, got %v", numbers)
	}

	// Test single value (non-JSON)
	singleValue := "1234567890"
	client.SmsPhoneNumber = &singleValue
	numbers = client.GetSmsPhoneNumbers()
	if len(numbers) != 1 || numbers[0] != "1234567890" {
		t.Errorf("Expected [1234567890] for single value, got %v", numbers)
	}

	// Test JSON array
	jsonArray := `["1234567890", "0987654321"]`
	client.SmsPhoneNumber = &jsonArray
	numbers = client.GetSmsPhoneNumbers()
	expected := []string{"1234567890", "0987654321"}
	if len(numbers) != 2 {
		t.Errorf("Expected 2 numbers, got %d", len(numbers))
	}
	for i, num := range expected {
		if numbers[i] != num {
			t.Errorf("Expected %s at position %d, got %s", num, i, numbers[i])
		}
	}
}

func TestClient_SetSmsPhoneNumbers(t *testing.T) {
	client := &Client{}

	// Test setting empty slice
	client.SetSmsPhoneNumbers([]string{})
	if client.SmsPhoneNumber != nil {
		t.Errorf("Expected nil for empty slice, got %v", client.SmsPhoneNumber)
	}

	// Test setting multiple numbers
	numbers := []string{"1234567890", "0987654321"}
	client.SetSmsPhoneNumbers(numbers)
	if client.SmsPhoneNumber == nil {
		t.Error("Expected non-nil SmsPhoneNumber")
	}

	var result []string
	err := json.Unmarshal([]byte(*client.SmsPhoneNumber), &result)
	if err != nil {
		t.Errorf("Failed to unmarshal JSON: %v", err)
	}
	expected := []string{"1234567890", "0987654321"}
	if len(result) != 2 {
		t.Errorf("Expected 2 numbers, got %d", len(result))
	}
	for i, num := range expected {
		if result[i] != num {
			t.Errorf("Expected %s at position %d, got %s", num, i, result[i])
		}
	}
}

func TestClient_GetAlertEmails(t *testing.T) {
	client := &Client{}

	// Test empty/nil values
	emails := client.GetAlertEmails()
	if len(emails) != 0 {
		t.Errorf("Expected empty slice for nil AlertsEmail, got %v", emails)
	}

	// Test single value (non-JSON)
	singleValue := "<EMAIL>"
	client.AlertsEmail = &singleValue
	emails = client.GetAlertEmails()
	if len(emails) != 1 || emails[0] != "<EMAIL>" {
		t.Errorf("Expected [<EMAIL>] for single value, got %v", emails)
	}

	// Test JSON array
	jsonArray := `["<EMAIL>", "<EMAIL>", "<EMAIL>"]`
	client.AlertsEmail = &jsonArray
	emails = client.GetAlertEmails()
	expected := []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"}
	if len(emails) != 3 {
		t.Errorf("Expected 3 emails, got %d", len(emails))
	}
	for i, email := range expected {
		if emails[i] != email {
			t.Errorf("Expected %s at position %d, got %s", email, i, emails[i])
		}
	}
}

func TestClient_SetAlertEmails(t *testing.T) {
	client := &Client{}

	// Test setting empty slice
	client.SetAlertEmails([]string{})
	if client.AlertsEmail != nil {
		t.Errorf("Expected nil for empty slice, got %v", client.AlertsEmail)
	}

	// Test setting multiple emails
	emails := []string{"<EMAIL>", "<EMAIL>"}
	client.SetAlertEmails(emails)
	if client.AlertsEmail == nil {
		t.Error("Expected non-nil AlertsEmail")
	}

	var result []string
	err := json.Unmarshal([]byte(*client.AlertsEmail), &result)
	if err != nil {
		t.Errorf("Failed to unmarshal JSON: %v", err)
	}
	expected := []string{"<EMAIL>", "<EMAIL>"}
	if len(result) != 2 {
		t.Errorf("Expected 2 emails, got %d", len(result))
	}
	for i, email := range expected {
		if result[i] != email {
			t.Errorf("Expected %s at position %d, got %s", email, i, result[i])
		}
	}
}

func TestClient_AddRemoveMethods(t *testing.T) {
	client := &Client{}

	// Test adding WhatsApp numbers
	client.AddWhatsappPhoneNumber("1234567890")
	client.AddWhatsappPhoneNumber("0987654321")
	client.AddWhatsappPhoneNumber("1234567890") // Duplicate should be ignored

	numbers := client.GetWhatsappPhoneNumbers()
	if len(numbers) != 2 {
		t.Errorf("Expected 2 unique numbers, got %d", len(numbers))
	}

	// Test removing WhatsApp number
	client.RemoveWhatsappPhoneNumber("1234567890")
	numbers = client.GetWhatsappPhoneNumbers()
	if len(numbers) != 1 || numbers[0] != "0987654321" {
		t.Errorf("Expected [0987654321] after removal, got %v", numbers)
	}

	// Test adding SMS numbers
	client.AddSmsPhoneNumber("1111111111")
	client.AddSmsPhoneNumber("2222222222")

	smsNumbers := client.GetSmsPhoneNumbers()
	if len(smsNumbers) != 2 {
		t.Errorf("Expected 2 SMS numbers, got %d", len(smsNumbers))
	}

	// Test adding alert emails
	client.AddAlertEmail("<EMAIL>")
	client.AddAlertEmail("<EMAIL>")
	client.AddAlertEmail("<EMAIL>") // Duplicate should be ignored

	alertEmails := client.GetAlertEmails()
	if len(alertEmails) != 2 {
		t.Errorf("Expected 2 unique emails, got %d", len(alertEmails))
	}

	// Test removing alert email
	client.RemoveAlertEmail("<EMAIL>")
	alertEmails = client.GetAlertEmails()
	if len(alertEmails) != 1 || alertEmails[0] != "<EMAIL>" {
		t.Errorf("Expected [<EMAIL>] after removal, got %v", alertEmails)
	}
}
