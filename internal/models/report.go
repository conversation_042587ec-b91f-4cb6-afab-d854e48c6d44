package models

import (
	"encoding/json"
	"time"
)

type Report struct {
	Id          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"unique;not null"`
	Description string `json:"description" gorm:"type:text"`
	Category    string `json:"category" gorm:"index"`        // Detail, Summary, Maintenance, Behavior, Management
	Status      string `json:"status" gorm:"default:active"` // active, inactive

	// Report configuration
	ReportType     string          `json:"report_type"`                      // position_log, trip_detail, etc.
	DefaultFilters json.RawMessage `json:"default_filters" gorm:"type:json"` // Default filter parameters

	// Metadata
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type ScheduledReport struct {
	Id       uint   `json:"id" gorm:"primaryKey"`
	ReportId uint   `json:"report_id" gorm:"index"`
	Report   Report `json:"report"`

	// Scheduling details
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      string `json:"status" gorm:"default:active"` // active, inactive, paused

	// Schedule configuration
	Frequency   string `json:"frequency"`    // daily, weekly, monthly, custom
	CronPattern string `json:"cron_pattern"` // For custom schedules
	Timezone    string `json:"timezone" gorm:"default:UTC"`

	// Report parameters
	Filters    json.RawMessage `json:"filters" gorm:"type:json"`    // Report-specific filters
	Recipients json.RawMessage `json:"recipients" gorm:"type:json"` // Email recipients
	Format     string          `json:"format" gorm:"default:pdf"`   // pdf, excel, csv

	// Execution tracking
	LastRunAt  *time.Time `json:"last_run_at"`
	NextRunAt  *time.Time `json:"next_run_at"`
	LastStatus string     `json:"last_status"` // success, failed, running
	LastError  *string    `json:"last_error"`
	RunCount   int        `json:"run_count" gorm:"default:0"`

	// Metadata
	CreatedBy uint      `json:"created_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Report filter structures
type ReportFilters struct {
	// Time filters
	StartDate *time.Time `json:"start_date"`
	EndDate   *time.Time `json:"end_date"`

	// Entity filters
	ClientDeviceIds []uint `json:"client_device_ids,omitempty"`
	DriverIds       []uint `json:"driver_ids,omitempty"`
	FleetIds        []uint `json:"fleet_ids,omitempty"`

	// Behavior filters
	EventTypes  []string `json:"event_types,omitempty"`
	MinSeverity *float64 `json:"min_severity,omitempty"`
	MaxSeverity *float64 `json:"max_severity,omitempty"`

	// Trip filters
	MinDistance *float64 `json:"min_distance,omitempty"`
	MaxDistance *float64 `json:"max_distance,omitempty"`
	MinDuration *int     `json:"min_duration,omitempty"`
	MaxDuration *int     `json:"max_duration,omitempty"`

	// Speed filters
	MinSpeed   *float64 `json:"min_speed,omitempty"`
	MaxSpeed   *float64 `json:"max_speed,omitempty"`
	SpeedLimit *float64 `json:"speed_limit,omitempty"`

	// Location filters
	GeofenceIds  []uint  `json:"geofence_ids,omitempty"`
	LocationName *string `json:"location_name,omitempty"`

	// Grouping options
	GroupBy  []string `json:"group_by,omitempty"` // device, driver, date, hour, etc.
	OrderBy  string   `json:"order_by,omitempty"`
	OrderDir string   `json:"order_dir,omitempty"` // asc, desc

	// Pagination
	Page    int `json:"page,omitempty"`
	PerPage int `json:"per_page,omitempty"`
}

// Report request structures
type CreateReportRequest struct {
	Name           string          `json:"name" binding:"required"`
	Description    string          `json:"description"`
	Category       string          `json:"category" binding:"required"`
	ReportType     string          `json:"report_type" binding:"required"`
	DefaultFilters json.RawMessage `json:"default_filters,omitempty"`
}

type UpdateReportRequest struct {
	Name           *string         `json:"name,omitempty"`
	Description    *string         `json:"description,omitempty"`
	Category       *string         `json:"category,omitempty"`
	Status         *string         `json:"status,omitempty"`
	DefaultFilters json.RawMessage `json:"default_filters,omitempty"`
}

type CreateScheduledReportRequest struct {
	ReportId    uint            `json:"report_id" binding:"required"`
	Name        string          `json:"name" binding:"required"`
	Description string          `json:"description"`
	Frequency   string          `json:"frequency" binding:"required"`
	CronPattern string          `json:"cron_pattern,omitempty"`
	Timezone    string          `json:"timezone"`
	Filters     json.RawMessage `json:"filters,omitempty"`
	Recipients  json.RawMessage `json:"recipients" binding:"required"`
	Format      string          `json:"format"`
}

type GenerateReportRequest struct {
	ReportId uint            `json:"report_id" binding:"required"`
	Format   string          `json:"format" binding:"required"` // pdf, excel, csv, json
	Filters  json.RawMessage `json:"filters,omitempty"`
}

// Report response structures
type ReportData struct {
	ReportInfo Report         `json:"report_info"`
	Filters    ReportFilters  `json:"filters"`
	Data       interface{}    `json:"data"`
	Summary    interface{}    `json:"summary,omitempty"`
	Metadata   ReportMetadata `json:"metadata"`
}

type ReportMetadata struct {
	GeneratedAt     time.Time `json:"generated_at"`
	GeneratedBy     *uint     `json:"generated_by,omitempty"`
	TotalRecords    int       `json:"total_records"`
	FilteredRecords int       `json:"filtered_records"`
	ExecutionTime   string    `json:"execution_time"`
	Format          string    `json:"format"`
}

// Recipient structure for scheduled reports
type ReportRecipient struct {
	Email string `json:"email"`
	Name  string `json:"name,omitempty"`
}

// Report categories
const (
	ReportCategoryDetail      = "Detail"
	ReportCategorySummary     = "Summary"
	ReportCategoryMaintenance = "Maintenance"
	ReportCategoryBehavior    = "Behavior"
	ReportCategoryManagement  = "Management"
)

// Report types
const (
	ReportTypePositionLog          = "position_log"
	ReportTypePositionLogDriver    = "position_log_driver"
	ReportTypeTripDetail           = "trip_detail"
	ReportTypeTripDetailDelta      = "trip_detail_delta"
	ReportTypeTripMileage          = "trip_mileage"
	ReportTypeStopDetail           = "stop_detail"
	ReportTypeIdleDetail           = "idle_detail"
	ReportTypeSpeedingDetail       = "speeding_detail"
	ReportTypeDoorDetail           = "door_detail"
	ReportTypeExceptionDetail      = "exception_detail"
	ReportTypeDestinationArrival   = "destination_arrival"
	ReportTypeDestinationZoneIn    = "destination_zone_in"
	ReportTypeLastLocation         = "last_location"
	ReportTypeLastLocationHistory  = "last_location_history"
	ReportTypeCargoDetail          = "cargo_detail"
	ReportTypeCargoFuelConsumption = "cargo_fuel_consumption"

	// Summary reports
	ReportTypeTripSummary                = "trip_summary"
	ReportTypeMileageDaySummary          = "mileage_day_summary"
	ReportTypeTripDaySummary             = "trip_day_summary"
	ReportTypeWorkingTimeDaySummary      = "working_time_day_summary"
	ReportTypeFleetProductiveDaySummary  = "fleet_productive_day_summary"
	ReportTypeAfterHourMonthSummary      = "after_hour_month_summary"
	ReportTypeMileageMonthSummary        = "mileage_month_summary"
	ReportTypeMileageAchievingSummary    = "mileage_achieving_summary"
	ReportTypeFuelEstimationMonthSummary = "fuel_estimation_month_summary"
	ReportTypeFuelEstimationHourSummary  = "fuel_estimation_hour_summary"
	ReportTypeVehicleUsageSummary        = "vehicle_usage_summary"
	ReportTypeExceptionSummary           = "exception_summary"
	ReportTypeDriverExceptionSummary     = "driver_exception_summary"
	ReportTypeSpeedingExceptionSummary   = "speeding_exception_summary"
	ReportTypeDriverPerformanceSummary   = "driver_performance_summary"
	ReportTypeVehiclePerformanceSummary  = "vehicle_performance_summary"
	ReportTypeVehicleOnlineSummary       = "vehicle_online_summary"
	ReportTypeCargoDaySummaryFuel        = "cargo_day_summary_fuel"
	ReportTypeOBDReport                  = "obd_report"

	// Maintenance reports
	ReportTypeFuelEstimation          = "fuel_estimation"
	ReportTypeFuelEstimationHour      = "fuel_estimation_hour"
	ReportTypeMaintenanceSchedule     = "maintenance_schedule"
	ReportTypeMaintenanceScheduleHour = "maintenance_schedule_hour"
	ReportTypeMaintenanceDetail       = "maintenance_detail"
	ReportTypeMaintenanceSummary      = "maintenance_summary"

	// Behavior reports
	ReportTypeGeofenceSpeedingDetail = "geofence_speeding_detail"
	ReportTypeFatigueReport          = "fatigue_report"
	ReportTypeViolationStayReport    = "violation_stay_report"
	ReportTypeRouteDeviationReport   = "route_deviation_report"
	ReportTypeRashDriveDetail        = "rash_drive_detail"

	// Management reports
	ReportTypeVehicleAliveReport  = "vehicle_alive_report"
	ReportTypeVehicleAliveTrackit = "vehicle_alive_trackit"
	ReportTypeLoginHistoryDetail  = "login_history_detail"
	ReportTypeLoginHistorySummary = "login_history_summary"
)

// Report statuses
const (
	ReportStatusActive   = "active"
	ReportStatusInactive = "inactive"
)

// Scheduled report statuses
const (
	ScheduledReportStatusActive   = "active"
	ScheduledReportStatusInactive = "inactive"
	ScheduledReportStatusPaused   = "paused"
)

// Report frequencies
const (
	ReportFrequencyDaily   = "daily"
	ReportFrequencyWeekly  = "weekly"
	ReportFrequencyMonthly = "monthly"
	ReportFrequencyCustom  = "custom"
)

// Report formats
const (
	ReportFormatPDF   = "pdf"
	ReportFormatExcel = "excel"
	ReportFormatCSV   = "csv"
	ReportFormatJSON  = "json"
)
