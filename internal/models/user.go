package models

import "time"

type User struct {
	Id                     uint       `json:"id" gorm:"primaryKey"`
	CreatedById            uint       `json:"created_by_id"`
	ClientId               *uint      `json:"client_id"`
	DriverId               *uint      `json:"driver_id" gorm:"index"`
	Name                   string     `json:"name"`
	Email                  string     `json:"email" gorm:"unique"`
	Username               *string    `json:"username" gorm:"unique"`
	EmailVerifiedAt        *string    `json:"email_verified_at"`
	Password               string     `json:"password"`
	UserType               string     `json:"user_type" gorm:"default:'backend'"`
	TwoFactorSecret        *string    `json:"two_factor_secret" gorm:"type:text"`
	TwoFactorRecoveryCodes *string    `json:"two_factor_recovery_codes" gorm:"type:text"`
	RememberToken          *string    `json:"remember_token"`
	LastLoginDate          *time.Time `json:"last_login_date"`
	Gender                 *string    `json:"gender"`
	TelegramUserId         *string    `json:"telegram_user_id"`
	SlackWebhookUrl        *string    `json:"slack_webhook_url"`
	Status                 *string    `json:"status"`
	Token                  *string    `json:"token"`
	Description            *string    `json:"description" gorm:"type:text"`
	CreatedAt              time.Time  `json:"created_at"`
	UpdatedAt              time.Time  `json:"updated_at"`
	Client                 Client     `json:"client"`
	Driver                 *Driver    `json:"driver,omitempty"`
}
type CreateUserRequest struct {
	ClientId        *uint   `json:"client_id,omitempty"`
	UserType        string  `json:"user_type" binding:"required"`
	Name            string  `json:"name" binding:"required"`
	Email           string  `json:"email" binding:"required,email"`
	Password        string  `json:"password" binding:"required,min=6"`
	Username        *string `json:"username,omitempty"`
	Gender          *string `json:"gender,omitempty"`
	TelegramUserId  *string `json:"telegramUserId,omitempty"`
	SlackWebhookUrl *string `json:"slack_webhook_url,omitempty"`
	Status          *string `json:"status" binding:"required"`
	Description     *string `json:"description,omitempty"`
}
type UpdateUserRequest struct {
	ClientId        *uint   `json:"client_id,omitempty"`
	UserType        string  `json:"user_type" binding:"required"`
	Name            string  `json:"name" binding:"required"`
	Email           string  `json:"email" binding:"required,email"`
	Password        string  `json:"password,omitempty"`
	Username        *string `json:"username,omitempty"`
	Gender          *string `json:"gender,omitempty"`
	TelegramUserId  *string `json:"telegram_user_id,omitempty"`
	SlackWebhookUrl *string `json:"slack_webhook_url,omitempty"`
	Status          *string `json:"status" binding:"required"`
	Description     *string `json:"description,omitempty"`
}
type ChangePasswordRequest struct {
	CurrentPassword      string `json:"current_password" binding:"required"`
	Password             string `json:"password" binding:"required,min=6"`
	PasswordConfirmation string `json:"password_confirmation" binding:"required"`
}
type UpdateProfileRequest struct {
	Name            string  `json:"name" binding:"required"`
	Email           string  `json:"email" binding:"required,email"`
	Username        *string `json:"username,omitempty"`
	Gender          *string `json:"gender,omitempty"`
	TelegramUserId  *string `json:"telegram_user_id,omitempty"`
	SlackWebhookUrl *string `json:"slack_webhook_url,omitempty"`
	Description     *string `json:"description,omitempty"`
}
