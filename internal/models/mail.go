package models

import (
	"errors"
	"strings"
)

// Mail represents an email message
type Mail struct {
	To          []string     `json:"to" binding:"required"`
	CC          []string     `json:"cc,omitempty"`
	BCC         []string     `json:"bcc,omitempty"`
	ReplyTo     string       `json:"reply_to,omitempty"`
	Subject     string       `json:"subject" binding:"required"`
	HTMLBody    string       `json:"html_body,omitempty"`
	TextBody    string       `json:"text_body,omitempty"`
	Attachments []Attachment `json:"attachments,omitempty"`
}

// Attachment represents an email attachment
type Attachment struct {
	Filename    string `json:"filename" binding:"required"`
	Content     []byte `json:"content" binding:"required"`
	ContentType string `json:"content_type,omitempty"`
}

// MailRequest represents the request structure for sending emails
type MailRequest struct {
	To      []string `json:"to" binding:"required"`
	CC      []string `json:"cc,omitempty"`
	BCC     []string `json:"bcc,omitempty"`
	ReplyTo string   `json:"reply_to,omitempty"`
	Subject string   `json:"subject" binding:"required"`
	Message string   `json:"message" binding:"required"`
	IsHTML  bool     `json:"is_html,omitempty"`
}

// Validate validates the mail structure
func (m *Mail) Validate() error {
	if len(m.To) == 0 {
		return errors.New("at least one recipient is required")
	}

	if strings.TrimSpace(m.Subject) == "" {
		return errors.New("subject is required")
	}

	if strings.TrimSpace(m.HTMLBody) == "" && strings.TrimSpace(m.TextBody) == "" {
		return errors.New("either HTML body or text body is required")
	}

	// Validate email addresses
	for _, email := range m.To {
		if !isValidEmail(email) {
			return errors.New("invalid email address in 'to' field: " + email)
		}
	}

	for _, email := range m.CC {
		if !isValidEmail(email) {
			return errors.New("invalid email address in 'cc' field: " + email)
		}
	}

	for _, email := range m.BCC {
		if !isValidEmail(email) {
			return errors.New("invalid email address in 'bcc' field: " + email)
		}
	}

	if m.ReplyTo != "" && !isValidEmail(m.ReplyTo) {
		return errors.New("invalid reply-to email address")
	}

	return nil
}

// isValidEmail performs basic email validation
func isValidEmail(email string) bool {
	email = strings.TrimSpace(email)
	if email == "" {
		return false
	}

	// Basic email validation - contains @ and has parts before and after
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}

	if len(parts[0]) == 0 || len(parts[1]) == 0 {
		return false
	}

	// Check for domain part having at least one dot
	if !strings.Contains(parts[1], ".") {
		return false
	}

	return true
}

// SetHTMLBody sets the HTML body content
func (m *Mail) SetHTMLBody(body string) {
	m.HTMLBody = body
}

// SetTextBody sets the text body content
func (m *Mail) SetTextBody(body string) {
	m.TextBody = body
}

// AddAttachment adds an attachment to the mail
func (m *Mail) AddAttachment(filename string, content []byte, contentType string) {
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	attachment := Attachment{
		Filename:    filename,
		Content:     content,
		ContentType: contentType,
	}

	m.Attachments = append(m.Attachments, attachment)
}

// NewMail creates a new Mail instance
func NewMail(to []string, subject string) *Mail {
	return &Mail{
		To:          to,
		Subject:     subject,
		Attachments: make([]Attachment, 0),
	}
}

// NewMailFromRequest creates a Mail instance from MailRequest
func NewMailFromRequest(req *MailRequest) *Mail {
	mail := &Mail{
		To:          req.To,
		CC:          req.CC,
		BCC:         req.BCC,
		ReplyTo:     req.ReplyTo,
		Subject:     req.Subject,
		Attachments: make([]Attachment, 0),
	}

	if req.IsHTML {
		mail.HTMLBody = req.Message
	} else {
		mail.TextBody = req.Message
	}

	return mail
}
