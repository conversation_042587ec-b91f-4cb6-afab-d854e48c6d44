package models

import (
	"encoding/json"
	"time"
)

type Client struct {
	Id                    uint       `json:"id" gorm:"primaryKey"`
	CreatedById           uint       `json:"created_by_id"`
	ReferredById          *uint      `json:"referred_by_id"`
	BranchId              *uint      `json:"branch_id"`
	StaffId               *uint      `json:"staff_id"`
	CountryId             *uint      `json:"country_id"`
	CurrencyId            *uint      `json:"currency_id"`
	ClientType            string     `json:"client_type"`
	Gender                *string    `json:"gender"`
	Name                  string     `json:"name"`
	Email                 string     `json:"email"  gorm:"unique"`
	Company               *string    `json:"company"`
	State                 *string    `json:"state"`
	City                  *string    `json:"city"`
	Town                  *string    `json:"town"`
	Address               *string    `json:"address" gorm:"type:text"`
	PhoneNumber           string     `json:"phone_number"`
	Status                string     `json:"status" gorm:"default:'active'"`
	Description           *string    `json:"description" gorm:"type:text"`
	MapsProvider          *string    `json:"maps_provider" gorm:"default:'google'"`
	DefaultLandingPage    *string    `json:"default_landing_page" gorm:"default:'dashboard'"`
	BillingCycle          *string    `json:"billing_cycle" gorm:"default:'monthly'"`
	BillingDay            *uint      `json:"billing_day" gorm:"default:5"`
	IsLifetime            *bool      `json:"is_lifetime" gorm:"default:false"`
	NextBillingDate       *time.Time `json:"next_billing_date" gorm:"type:date"`
	LastBilledAt          *time.Time `json:"last_billed_at" gorm:"default:null"`
	SuspendedAt           *time.Time `json:"suspended_at" gorm:"default:null"`
	WhatsappAlertsEnabled *bool      `json:"whatsapp_alerts_enabled" gorm:"default:false"`
	SmsAlertsEnabled      *bool      `json:"sms_alerts_enabled" gorm:"default:false"`
	EmailAlertsEnabled    *bool      `json:"email_alerts_enabled" gorm:"default:false"`
	WhatsappPhoneNumber   *string    `json:"whatsapp_phone_number" gorm:"type:text"` // JSON array of phone numbers
	SmsPhoneNumber        *string    `json:"sms_phone_number" gorm:"type:text"`      // JSON array of phone numbers
	AlertsEmail           *string    `json:"alerts_email" gorm:"type:text"`          // JSON array of email addresses
	CreatedAt             time.Time  `json:"created_at"`
	UpdatedAt             time.Time  `json:"updated_at"`
	Country               Country    `json:"country"`
	Currency              Currency   `json:"currency"`
}

// GetWhatsappPhoneNumbers returns the WhatsApp phone numbers as a slice
func (c *Client) GetWhatsappPhoneNumbers() []string {
	if c.WhatsappPhoneNumber == nil || *c.WhatsappPhoneNumber == "" {
		return []string{}
	}

	var numbers []string
	if err := json.Unmarshal([]byte(*c.WhatsappPhoneNumber), &numbers); err != nil {
		// If JSON unmarshaling fails, treat as single value
		return []string{*c.WhatsappPhoneNumber}
	}
	return numbers
}

// SetWhatsappPhoneNumbers sets the WhatsApp phone numbers from a slice
func (c *Client) SetWhatsappPhoneNumbers(numbers []string) {
	if len(numbers) == 0 {
		c.WhatsappPhoneNumber = nil
		return
	}

	data, err := json.Marshal(numbers)
	if err != nil {
		// Fallback to first number if marshaling fails
		if len(numbers) > 0 {
			c.WhatsappPhoneNumber = &numbers[0]
		}
		return
	}

	jsonStr := string(data)
	c.WhatsappPhoneNumber = &jsonStr
}

// GetSmsPhoneNumbers returns the SMS phone numbers as a slice
func (c *Client) GetSmsPhoneNumbers() []string {
	if c.SmsPhoneNumber == nil || *c.SmsPhoneNumber == "" {
		return []string{}
	}

	var numbers []string
	if err := json.Unmarshal([]byte(*c.SmsPhoneNumber), &numbers); err != nil {
		// If JSON unmarshaling fails, treat as single value
		return []string{*c.SmsPhoneNumber}
	}
	return numbers
}

// SetSmsPhoneNumbers sets the SMS phone numbers from a slice
func (c *Client) SetSmsPhoneNumbers(numbers []string) {
	if len(numbers) == 0 {
		c.SmsPhoneNumber = nil
		return
	}

	data, err := json.Marshal(numbers)
	if err != nil {
		// Fallback to first number if marshaling fails
		if len(numbers) > 0 {
			c.SmsPhoneNumber = &numbers[0]
		}
		return
	}

	jsonStr := string(data)
	c.SmsPhoneNumber = &jsonStr
}

// GetAlertEmails returns the alert email addresses as a slice
func (c *Client) GetAlertEmails() []string {
	if c.AlertsEmail == nil || *c.AlertsEmail == "" {
		return []string{}
	}

	var emails []string
	if err := json.Unmarshal([]byte(*c.AlertsEmail), &emails); err != nil {
		// If JSON unmarshaling fails, treat as single value
		return []string{*c.AlertsEmail}
	}
	return emails
}

// SetAlertEmails sets the alert email addresses from a slice
func (c *Client) SetAlertEmails(emails []string) {
	if len(emails) == 0 {
		c.AlertsEmail = nil
		return
	}

	data, err := json.Marshal(emails)
	if err != nil {
		// Fallback to first email if marshaling fails
		if len(emails) > 0 {
			c.AlertsEmail = &emails[0]
		}
		return
	}

	jsonStr := string(data)
	c.AlertsEmail = &jsonStr
}

// AddWhatsappPhoneNumber adds a phone number to the WhatsApp list
func (c *Client) AddWhatsappPhoneNumber(phoneNumber string) {
	numbers := c.GetWhatsappPhoneNumbers()

	// Check if number already exists
	for _, num := range numbers {
		if num == phoneNumber {
			return // Already exists
		}
	}

	numbers = append(numbers, phoneNumber)
	c.SetWhatsappPhoneNumbers(numbers)
}

// AddSmsPhoneNumber adds a phone number to the SMS list
func (c *Client) AddSmsPhoneNumber(phoneNumber string) {
	numbers := c.GetSmsPhoneNumbers()

	// Check if number already exists
	for _, num := range numbers {
		if num == phoneNumber {
			return // Already exists
		}
	}

	numbers = append(numbers, phoneNumber)
	c.SetSmsPhoneNumbers(numbers)
}

// AddAlertEmail adds an email to the alerts list
func (c *Client) AddAlertEmail(email string) {
	emails := c.GetAlertEmails()

	// Check if email already exists
	for _, em := range emails {
		if em == email {
			return // Already exists
		}
	}

	emails = append(emails, email)
	c.SetAlertEmails(emails)
}

// RemoveWhatsappPhoneNumber removes a phone number from the WhatsApp list
func (c *Client) RemoveWhatsappPhoneNumber(phoneNumber string) {
	numbers := c.GetWhatsappPhoneNumbers()

	for i, num := range numbers {
		if num == phoneNumber {
			numbers = append(numbers[:i], numbers[i+1:]...)
			break
		}
	}

	c.SetWhatsappPhoneNumbers(numbers)
}

// RemoveSmsPhoneNumber removes a phone number from the SMS list
func (c *Client) RemoveSmsPhoneNumber(phoneNumber string) {
	numbers := c.GetSmsPhoneNumbers()

	for i, num := range numbers {
		if num == phoneNumber {
			numbers = append(numbers[:i], numbers[i+1:]...)
			break
		}
	}

	c.SetSmsPhoneNumbers(numbers)
}

// RemoveAlertEmail removes an email from the alerts list
func (c *Client) RemoveAlertEmail(email string) {
	emails := c.GetAlertEmails()

	for i, em := range emails {
		if em == email {
			emails = append(emails[:i], emails[i+1:]...)
			break
		}
	}

	c.SetAlertEmails(emails)
}

type ClientRequest struct {
	ReferredById          *uint      `json:"referred_by_id,omitempty"`
	StaffId               *uint      `json:"staff_id,omitempty"`
	CountryId             *uint      `json:"country_id,omitempty"`
	CurrencyId            *uint      `json:"currency_id,omitempty"`
	ClientType            string     `json:"client_type" binding:"required"`
	Gender                string     `json:"gender,omitempty"`
	Name                  string     `json:"name"  binding:"required"`
	Email                 string     `json:"email"   binding:"required,email"`
	Company               string     `json:"company,omitempty"`
	State                 string     `json:"state,omitempty"`
	City                  string     `json:"city,omitempty"`
	Town                  string     `json:"town,omitempty"`
	Address               string     `json:"address,omitempty"`
	PhoneNumber           string     `json:"phone_number" binding:"required"`
	Status                string     `json:"status" binding:"required"`
	Description           string     `json:"description,omitempty"`
	BillingCycle          string     `json:"billing_cycle,omitempty"`
	BillingDay            uint       `json:"billing_day,omitempty"`
	IsLifetime            bool       `json:"is_lifetime,omitempty"`
	NextBillingDate       *time.Time `json:"next_billing_date,omitempty"`
	MapsProvider          *string    `json:"maps_provider,omitempty"`
	DefaultLandingPage    *string    `json:"default_landing_page,omitempty"`
	WhatsappAlertsEnabled *bool      `json:"whatsapp_alerts_enabled,omitempty"`
	SmsAlertsEnabled      *bool      `json:"sms_alerts_enabled,omitempty"`
	EmailAlertsEnabled    *bool      `json:"email_alerts_enabled,omitempty"`
	WhatsappPhoneNumber   *string    `json:"whatsapp_phone_number,omitempty"`
	SmsPhoneNumber        *string    `json:"sms_phone_number,omitempty"`
	AlertsEmail           *string    `json:"alerts_email,omitempty"`
}
type ClientFilterRequest struct {
	StaffId    *uint   `json:"staff_id,omitempty"`
	CountryId  *uint   `json:"country_id,omitempty"`
	ClientType *string `json:"client_type,omitempty"`
}
type UpdateClientRequest struct {
	Name                string  `json:"name" binding:"required"`
	Email               string  `json:"email" binding:"required,email"`
	Company             *string `json:"company,omitempty"`
	State               *string `json:"state,omitempty"`
	City                *string `json:"city,omitempty"`
	Town                *string `json:"town,omitempty"`
	Address             *string `json:"address,omitempty"`
	CountryId           *uint   `json:"country_id,omitempty"`
	PhoneNumber         string  `json:"phone_number" binding:"required"`
	Gender              *string `json:"gender,omitempty"`
	Description         *string `json:"description,omitempty"`
	MapsProvider        *string `json:"maps_provider,omitempty"`
	DefaultLandingPage  *string `json:"default_landing_page,omitempty"`
	WhatsappPhoneNumber *string `json:"whatsapp_phone_number,omitempty"`
	SmsPhoneNumber      *string `json:"sms_phone_number,omitempty"`
	AlertsEmail         *string `json:"alerts_email,omitempty"`
}
