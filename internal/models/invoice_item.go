package models

import "time"

type InvoiceItem struct {
	Id                    uint       `json:"id"`
	InvoiceId             uint       `json:"invoice_id"`
	ClientDeviceId        *uint      `json:"client_device_id"`
	TaxRateId             *uint      `json:"tax_rate_id"`
	Name                  *string    `json:"name"`
	Description           *string    `json:"description" gorm:"type:text"`
	Quantity              *uint      `json:"quantity" gorm:"default:1"`
	ItemPosition          *uint      `json:"item_position"`
	UnitCost              *float64   `json:"unit_cost"`
	Discount              *float64   `json:"discount"`
	DiscountType          *string    `json:"discount_type" gorm:"default:'percentage'"`
	DiscountAmount        *float64   `json:"discount_amount"`
	TaxAmount             *float64   `json:"tax_amount"`
	Total                 *float64   `json:"total"`
	BaseCurrencyUnitCost  *float64   `json:"base_currency_unit_cost"`
	BaseCurrencyTaxAmount *float64   `json:"base_currency_tax_amount"`
	BaseCurrencyTotal     *float64   `json:"base_currency_total"`
	CreatedAt             *time.Time `json:"created_at"`
	UpdatedAt             *time.Time `json:"updated_at"`
}
type InvoiceItemRequest struct {
	TaxRateId             *uint    `json:"tax_rate_id,omitempty"`
	ClientDeviceId        *uint    `json:"client_device_id,omitempty"`
	Name                  *string  `json:"name,omitempty"`
	Description           *string  `json:"description,omitempty" gorm:"type:text"`
	Quantity              *uint    `json:"quantity" gorm:"default:1"`
	ItemPosition          *uint    `json:"item_position,omitempty"`
	UnitCost              *float64 `json:"unit_cost,omitempty"`
	BaseCurrencyUnitCost  *float64 `json:"base_currency_unit_cost,omitempty"`
	Discount              *float64 `json:"discount,omitempty"`
	DiscountType          *string  `json:"discount_type,omitempty" gorm:"default:'percentage'"`
	DiscountAmount        *float64 `json:"discount_amount,omitempty"`
	TaxAmount             *float64 `json:"tax_amount,omitempty"`
	BaseCurrencyTaxAmount *float64 `json:"base_currency_tax_amount,omitempty"`
	Total                 *float64 `json:"total,omitempty"`
	BaseCurrencyTotal     *float64 `json:"base_currency_total,omitempty"`
}
