package mail

import (
	"os"
	"testing"
	"yotracker/internal/models"
)

func TestSendEmailHelpers(t *testing.T) {
	// Set up test environment variables
	os.Setenv("SENDGRID_API_KEY", "test-key")
	os.Setenv("FROM_EMAIL", "<EMAIL>")
	os.Setenv("FROM_NAME", "Test Sender")
	os.Setenv("TESTING_DB_NAME", "testing") // This triggers test mode

	// Test SendEmail function exists and can be called
	// Note: In test mode, emails are mocked and should not return errors
	err := SendEmail([]string{"<EMAIL>"}, "Test Subject", "Test Message", false)
	if err != nil {
		t.Errorf("Expected no error in test mode, but got: %v", err)
	}

	// Test SendEmailWithCC function exists
	err = SendEmailWithCC(
		[]string{"<EMAIL>"},
		[]string{"<EMAIL>"},
		"Test Subject",
		"Test Message",
		false,
	)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Expected no error in test mode, but got: %v", err)
	}

	// Test SendWelcomeEmail function exists
	err = SendWelcomeEmail("<EMAIL>", "Test User")
	if err != nil {
		t.Errorf("Expected no error in test mode, but got: %v", err)
	}

	// Test SendPasswordResetEmail function exists
	err = SendPasswordResetEmail("<EMAIL>", "Test User", "http://example.com/reset")
	if err != nil {
		t.Errorf("Expected no error in test mode, but got: %v", err)
	}

	// Test SendNotificationEmail function exists
	err = SendNotificationEmail("<EMAIL>", "Test User", "Test notification message")
	if err != nil {
		t.Errorf("Expected no error in test mode, but got: %v", err)
	}

	// Test SendDeviceAlert function exists
	err = SendDeviceAlert("<EMAIL>", "DEV001", "Low Battery", "Battery is low")
	if err != nil {
		t.Errorf("Expected no error in test mode, but got: %v", err)
	}

	// Test SendInvoiceEmail function exists
	pdfContent := []byte("fake pdf content")
	mockInvoice := &models.Invoice{
		Id:        1,
		Reference: func() *string { s := "INV001"; return &s }(),
		Client: models.Client{
			Name:  "Test User",
			Email: "<EMAIL>",
		},
	}
	err = SendInvoiceEmail(mockInvoice, pdfContent)
	if err != nil {
		t.Errorf("Expected no error in test mode, but got: %v", err)
	}

	// Test SendBulkEmail function exists
	err = SendBulkEmail([]string{"<EMAIL>", "<EMAIL>"}, "Bulk Subject", "Bulk Message", false)
	if err != nil {
		t.Errorf("Expected no error in test mode, but got: %v", err)
	}

	// Test SendBulkEmailIndividual function exists
	err = SendBulkEmailIndividual([]string{"<EMAIL>", "<EMAIL>"}, "Individual Subject", "Individual Message", false)
	if err != nil {
		t.Errorf("Expected no error in test mode, but got: %v", err)
	}
}

func TestHelperFunctionsWithoutEnvVars(t *testing.T) {
	// Clear environment variables
	os.Unsetenv("SENDGRID_API_KEY")
	os.Unsetenv("FROM_EMAIL")
	os.Unsetenv("FROM_NAME")
	os.Unsetenv("TESTING_DB_NAME") // Ensure we're not in test mode

	// Test that functions return appropriate errors when env vars are missing
	err := SendEmail([]string{"<EMAIL>"}, "Test Subject", "Test Message", false)
	if err == nil {
		t.Error("Expected error when environment variables are missing")
	}

	if err != nil && err.Error() != "failed to initialize mail service: SENDGRID_API_KEY environment variable is required" {
		t.Errorf("Expected specific error message, got: %v", err)
	}
}
