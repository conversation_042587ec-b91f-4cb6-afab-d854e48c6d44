package mail

import (
	"bytes"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/internal/templates"

	"github.com/Sebastiaan<PERSON><PERSON>pert/go-wkhtmltopdf"
)

// SendEmail sends a simple email using the mail service
func SendEmail(to []string, subject, message string, isHTML bool) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping email to %v", to)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendSimpleMail(to, subject, message, isHTML)
}

// SendEmailWithCC sends an email with CC recipients
func SendEmailWithCC(to []string, cc []string, subject, message string, isHTML bool) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping email with CC to %v", to)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendMailWithCC(to, cc, subject, message, isHTML)
}

// SendEmailWithAttachment sends an email with an attachment
func SendEmailWithAttachment(to []string, subject, message string, isHTML bool, filename string, content []byte, contentType string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendMailWithAttachment(to, subject, message, isHTML, filename, content, contentType)
}

// SendWelcomeEmail sends a welcome email to new users
func SendWelcomeEmail(to string, name string) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping welcome email to %s", to)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendWelcomeEmail(to, name)
}

// SendPasswordResetEmail sends a password reset email
func SendPasswordResetEmail(to string, name string, resetLink string) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping password reset email to %s", to)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendPasswordResetEmail(to, name, resetLink)
}

// SendNewAccountEmail sends a new account creation notification with credentials
func SendNewAccountEmail(to string, name string, password string) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping new account email to %s", to)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	loginURL := mailService.NewEmailTemplate().AppURL + "/login"
	return mailService.SendNewAccountEmail(to, name, password, loginURL)
}

// SendNotificationEmail sends a general notification email using template
func SendNotificationEmail(userEmail, userName, message string) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping notification email to %s", userEmail)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendTemplatedEmail(userEmail, "YoTracker Notification", func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Hello %s,", userName)).
			AddContent(message)
	})
}

// SendDeviceAlert sends a device alert email using template
func SendDeviceAlert(userEmail, deviceId, alertType, alertMessage string) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping device alert email to %s", userEmail)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	subject := fmt.Sprintf("Device Alert: %s - %s", deviceId, alertType)

	return mailService.SendTemplatedEmail(userEmail, subject, func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting("Device Alert").
			AddContent(
				fmt.Sprintf("Device ID: %s", deviceId),
				fmt.Sprintf("Alert Type: %s", alertType),
				fmt.Sprintf("Message: %s", alertMessage),
				"Please check your device status in the YoTracker dashboard.",
			).
			SetAction("View Dashboard", template.AppURL+"/dashboard")
	})
}

// SendInvoiceEmail sends an invoice email with attachment using template
func SendInvoiceEmail(invoice *models.Invoice, pdfContent []byte) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping invoice email to %s", invoice.Client.Email)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}
	invoiceId := strconv.Itoa(int(invoice.Id))
	invoiceReference := *invoice.Reference
	userName := invoice.Client.Name
	userEmail := invoice.Client.Email
	subject := fmt.Sprintf("Invoice %s - YoTracker", invoiceReference)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Dear %s,", userName)).
		AddContent(
			fmt.Sprintf("A new invoice has been generated for you: Invoice %s", invoiceReference),
			"Please find your invoice attached.",
			"Thank you for your business!",
		).
		SetAction("View Invoice", template.AppURL+"/invoices/"+invoiceId)

	htmlBody := template.GenerateHTML()
	textBody := template.GenerateText()

	mailObj := models.NewMail([]string{userEmail}, subject)
	mailObj.SetHTMLBody(htmlBody)
	mailObj.SetTextBody(textBody)

	// Add PDF attachment
	filename := fmt.Sprintf("invoice_%s.pdf", *invoice.Reference)
	mailObj.AddAttachment(filename, pdfContent, "application/pdf")
	if invoice.Status == "draft" {
		invoice.Status = "pending"
		invoice.Sent = func() *bool { b := true; return &b }()
		invoice.SentAt = func() *time.Time { t := time.Now(); return &t }()
		config.DB.Save(&invoice)

	}
	return mailService.SendMail(mailObj)
}

// SendBulkEmail sends the same email to multiple recipients
func SendBulkEmail(recipients []string, subject, message string, isHTML bool) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping bulk email to %v", recipients)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Send to all recipients at once (they'll all see each other's emails)
	// If you want to send individual emails, use SendBulkEmailIndividual instead
	return mailService.SendSimpleMail(recipients, subject, message, isHTML)
}

// SendBulkEmailIndividual sends individual emails to multiple recipients
func SendBulkEmailIndividual(recipients []string, subject, message string, isHTML bool) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping individual bulk emails to %v", recipients)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Send individual emails to each recipient
	for _, recipient := range recipients {
		err := mailService.SendSimpleMail([]string{recipient}, subject, message, isHTML)
		if err != nil {
			return fmt.Errorf("failed to send email to %s: %v", recipient, err)
		}
	}

	return nil
}

// SendInvoicePaymentEmail sends a payment confirmation email using template
func SendInvoicePaymentEmail(payment *models.InvoicePayment) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Extract necessary information
	invoiceId := strconv.Itoa(int(payment.Invoice.Id))
	invoiceReference := *payment.Invoice.Reference
	userName := payment.Invoice.Client.Name
	userEmail := payment.Invoice.Client.Email
	paymentAmount := payment.Amount
	paymentType := payment.PaymentType.Name
	currency := payment.Currency.Symbol

	// Format payment date
	paymentDate := "N/A"
	if payment.Date != nil {
		paymentDate = payment.Date.Format("January 2, 2006")
	}

	// Format transaction ID
	transactionId := "N/A"
	if payment.TransId != nil {
		transactionId = *payment.TransId
	}

	subject := fmt.Sprintf("Payment Confirmation - Invoice %s", invoiceReference)

	return mailService.SendTemplatedEmail(userEmail, subject, func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Dear %s,", userName)).
			AddContent(
				fmt.Sprintf("We have successfully received your payment for Invoice %s.", invoiceReference),
				"Payment Details:",
				fmt.Sprintf("• Amount: %s%.2f", currency, paymentAmount),
				fmt.Sprintf("• Payment Method: %s", paymentType),
				fmt.Sprintf("• Payment Date: %s", paymentDate),
				fmt.Sprintf("• Transaction ID: %s", transactionId),
				"",
				"Thank you for your prompt payment. Your account has been updated accordingly.",
			).
			SetAction("View Invoice", template.AppURL+"/invoices/"+invoiceId)
	})
}

// SendServiceSuspensionEmail sends a service suspension notification email
func SendServiceSuspensionEmail(client models.Client, overdueInvoices []models.Invoice) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping service suspension email to %s", client.Email)
		return nil
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Calculate total overdue amount
	var totalOverdue float64
	var invoiceReferences []string
	for _, invoice := range overdueInvoices {
		if invoice.Balance != nil {
			totalOverdue += *invoice.Balance
		}
		if invoice.Reference != nil {
			invoiceReferences = append(invoiceReferences, *invoice.Reference)
		}
	}

	// Get currency symbol (use first invoice's currency)
	currency := "$"
	if len(overdueInvoices) > 0 && overdueInvoices[0].CurrencyId != nil {
		currency = overdueInvoices[0].Currency.Symbol
	}

	subject := "Service Suspension Notice - Overdue Payment Required"

	return mailService.SendTemplatedEmail(client.Email, subject, func(template *service.EmailTemplate) *service.EmailTemplate {
		content := []string{
			"We regret to inform you that your service has been suspended due to overdue payments.",
			"",
			"Overdue Invoice Details:",
		}

		// Add invoice details
		for _, invoice := range overdueInvoices {
			invoiceRef := "N/A"
			if invoice.Reference != nil {
				invoiceRef = *invoice.Reference
			}

			dueDate := "N/A"
			if invoice.DueDate != nil {
				dueDate = invoice.DueDate.Format("January 2, 2006")
			}

			balance := 0.0
			if invoice.Balance != nil {
				balance = *invoice.Balance
			}

			content = append(content, fmt.Sprintf("• Invoice %s - Due: %s - Amount: %s%.2f",
				invoiceRef, dueDate, currency, balance))
		}

		content = append(content,
			"",
			fmt.Sprintf("Total Outstanding Amount: %s%.2f", currency, totalOverdue),
			"",
			"To restore your service, please make payment immediately.",
			"Once payment is received and processed, your service will be reactivated.",
			"",
			"If you have any questions or need assistance with payment, please contact our support team immediately.",
		)

		return template.
			SetGreeting(fmt.Sprintf("Dear %s,", client.Name)).
			AddContent(content...).
			SetAction("Make Payment", template.AppURL+"/invoices")
	})
}

// SendFirstOverdueReminderEmail sends a gentle first overdue reminder with invoice attachment
func SendFirstOverdueReminderEmail(invoice models.Invoice) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping first overdue reminder email to %s", invoice.Client.Email)
		return nil
	}

	// Generate PDF content for the invoice
	pdfContent, err := generateInvoicePDF(invoice)
	if err != nil {
		log.Printf("Failed to generate PDF for invoice %s: %v", *invoice.Reference, err)
		// Continue without attachment if PDF generation fails
		return sendFirstOverdueReminderWithoutAttachment(invoice)
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Get invoice details
	invoiceRef := "N/A"
	if invoice.Reference != nil {
		invoiceRef = *invoice.Reference
	}

	dueDate := "N/A"
	if invoice.DueDate != nil {
		dueDate = invoice.DueDate.Format("January 2, 2006")
	}

	balance := 0.0
	if invoice.Balance != nil {
		balance = *invoice.Balance
	}

	currency := "$"
	if invoice.CurrencyId != nil {
		currency = invoice.Currency.Symbol
	}

	invoiceId := strconv.Itoa(int(invoice.Id))
	subject := fmt.Sprintf("Payment Reminder - Invoice %s", invoiceRef)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Dear %s,", invoice.Client.Name)).
		AddContent(
			"We hope this message finds you well.",
			"",
			fmt.Sprintf("This is a friendly reminder that Invoice %s was due on %s.", invoiceRef, dueDate),
			"",
			"Invoice Details:",
			fmt.Sprintf("• Invoice Number: %s", invoiceRef),
			fmt.Sprintf("• Due Date: %s", dueDate),
			fmt.Sprintf("• Outstanding Amount: %s%.2f", currency, balance),
			"",
			"Please find your invoice attached for your reference.",
			"",
			"If you have already made this payment, please disregard this reminder.",
			"If you have any questions about this invoice, please don't hesitate to contact us.",
			"",
			"Thank you for your business!",
		).
		SetAction("View Invoice", template.AppURL+"/invoices/"+invoiceId)

	htmlBody := template.GenerateHTML()
	textBody := template.GenerateText()

	mailObj := models.NewMail([]string{invoice.Client.Email}, subject)
	mailObj.SetHTMLBody(htmlBody)
	mailObj.SetTextBody(textBody)

	// Add PDF attachment
	filename := fmt.Sprintf("invoice_%s.pdf", invoiceRef)
	mailObj.AddAttachment(filename, pdfContent, "application/pdf")

	return mailService.SendMail(mailObj)
}

// SendSecondOverdueReminderEmail sends a more urgent second overdue reminder with invoice attachment
func SendSecondOverdueReminderEmail(invoice models.Invoice) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping second overdue reminder email to %s", invoice.Client.Email)
		return nil
	}

	// Generate PDF content for the invoice
	pdfContent, err := generateInvoicePDF(invoice)
	if err != nil {
		log.Printf("Failed to generate PDF for invoice %s: %v", *invoice.Reference, err)
		// Continue without attachment if PDF generation fails
		return sendSecondOverdueReminderWithoutAttachment(invoice)
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Get invoice details
	invoiceRef := "N/A"
	if invoice.Reference != nil {
		invoiceRef = *invoice.Reference
	}

	dueDate := "N/A"
	if invoice.DueDate != nil {
		dueDate = invoice.DueDate.Format("January 2, 2006")
	}

	balance := 0.0
	if invoice.Balance != nil {
		balance = *invoice.Balance
	}

	currency := "$"
	if invoice.CurrencyId != nil {
		currency = invoice.Currency.Symbol
	}

	invoiceId := strconv.Itoa(int(invoice.Id))
	subject := fmt.Sprintf("URGENT: Payment Overdue - Invoice %s", invoiceRef)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Dear %s,", invoice.Client.Name)).
		AddContent(
			"This is an urgent reminder regarding your overdue payment.",
			"",
			fmt.Sprintf("Invoice %s was due on %s and remains unpaid.", invoiceRef, dueDate),
			"",
			"Invoice Details:",
			fmt.Sprintf("• Invoice Number: %s", invoiceRef),
			fmt.Sprintf("• Due Date: %s", dueDate),
			fmt.Sprintf("• Outstanding Amount: %s%.2f", currency, balance),
			"",
			"Please find your invoice attached for your reference.",
			"",
			"⚠️ IMPORTANT: Please arrange payment immediately to avoid any service interruption.",
			"",
			"If you are experiencing difficulties with payment, please contact us immediately to discuss payment arrangements.",
			"If payment has already been made, please provide us with the transaction details.",
			"",
			"We appreciate your immediate attention to this matter.",
		).
		SetAction("Pay Now", template.AppURL+"/invoices/"+invoiceId)

	htmlBody := template.GenerateHTML()
	textBody := template.GenerateText()

	mailObj := models.NewMail([]string{invoice.Client.Email}, subject)
	mailObj.SetHTMLBody(htmlBody)
	mailObj.SetTextBody(textBody)

	// Add PDF attachment
	filename := fmt.Sprintf("invoice_%s.pdf", invoiceRef)
	mailObj.AddAttachment(filename, pdfContent, "application/pdf")

	return mailService.SendMail(mailObj)
}

// SendThirdOverdueReminderEmail sends a final warning before suspension with invoice attachment
func SendThirdOverdueReminderEmail(invoice models.Invoice) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping third overdue reminder email to %s", invoice.Client.Email)
		return nil
	}

	// Generate PDF content for the invoice
	pdfContent, err := generateInvoicePDF(invoice)
	if err != nil {
		log.Printf("Failed to generate PDF for invoice %s: %v", *invoice.Reference, err)
		// Continue without attachment if PDF generation fails
		return sendThirdOverdueReminderWithoutAttachment(invoice)
	}

	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Get invoice details
	invoiceRef := "N/A"
	if invoice.Reference != nil {
		invoiceRef = *invoice.Reference
	}

	dueDate := "N/A"
	if invoice.DueDate != nil {
		dueDate = invoice.DueDate.Format("January 2, 2006")
	}

	balance := 0.0
	if invoice.Balance != nil {
		balance = *invoice.Balance
	}

	currency := "$"
	if invoice.CurrencyId != nil {
		currency = invoice.Currency.Symbol
	}

	invoiceId := strconv.Itoa(int(invoice.Id))
	subject := fmt.Sprintf("FINAL NOTICE: Payment Required - Invoice %s", invoiceRef)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Dear %s,", invoice.Client.Name)).
		AddContent(
			"🚨 FINAL NOTICE - IMMEDIATE ACTION REQUIRED",
			"",
			fmt.Sprintf("This is our final reminder regarding Invoice %s, which was due on %s.", invoiceRef, dueDate),
			"",
			"Invoice Details:",
			fmt.Sprintf("• Invoice Number: %s", invoiceRef),
			fmt.Sprintf("• Due Date: %s", dueDate),
			fmt.Sprintf("• Outstanding Amount: %s%.2f", currency, balance),
			"",
			"Please find your invoice attached for your reference.",
			"",
			"⚠️ WARNING: Failure to pay this invoice immediately may result in:",
			"• Service suspension",
			"• Additional late fees",
			"• Collection proceedings",
			"• Account termination",
			"",
			"If payment is not received within 24 hours, your account may be suspended.",
			"",
			"If you are unable to pay the full amount, please contact us immediately to discuss payment arrangements.",
			"If payment has been made, please provide transaction details immediately.",
			"",
			"This is your final opportunity to resolve this matter before further action is taken.",
		).
		SetAction("PAY IMMEDIATELY", template.AppURL+"/invoices/"+invoiceId)

	htmlBody := template.GenerateHTML()
	textBody := template.GenerateText()

	mailObj := models.NewMail([]string{invoice.Client.Email}, subject)
	mailObj.SetHTMLBody(htmlBody)
	mailObj.SetTextBody(textBody)

	// Add PDF attachment
	filename := fmt.Sprintf("invoice_%s.pdf", invoiceRef)
	mailObj.AddAttachment(filename, pdfContent, "application/pdf")

	return mailService.SendMail(mailObj)
}

// generateInvoicePDF generates a PDF for the given invoice
func generateInvoicePDF(invoice models.Invoice) ([]byte, error) {
	// Generate PDF using the same logic as in the controllers
	tmpl, err := templates.GetInvoiceTemplate()
	if err != nil {
		return nil, fmt.Errorf("template error: %v", err)
	}

	var htmlBuffer bytes.Buffer
	invoiceView := models.ToInvoiceView(invoice)
	err = tmpl.Execute(&htmlBuffer, invoiceView)
	if err != nil {
		return nil, fmt.Errorf("template render error: %v", err)
	}

	// Generate PDF from HTML using wkhtmltopdf
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return nil, fmt.Errorf("PDF generator error: %v", err)
	}

	page := wkhtmltopdf.NewPageReader(&htmlBuffer)
	pdfg.AddPage(page)

	err = pdfg.Create()
	if err != nil {
		return nil, fmt.Errorf("PDF creation failed: %v", err)
	}

	return pdfg.Bytes(), nil
}

// sendFirstOverdueReminderWithoutAttachment sends the first reminder without PDF attachment as fallback
func sendFirstOverdueReminderWithoutAttachment(invoice models.Invoice) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Get invoice details
	invoiceRef := "N/A"
	if invoice.Reference != nil {
		invoiceRef = *invoice.Reference
	}

	dueDate := "N/A"
	if invoice.DueDate != nil {
		dueDate = invoice.DueDate.Format("January 2, 2006")
	}

	balance := 0.0
	if invoice.Balance != nil {
		balance = *invoice.Balance
	}

	currency := "$"
	if invoice.CurrencyId != nil {
		currency = invoice.Currency.Symbol
	}

	invoiceId := strconv.Itoa(int(invoice.Id))
	subject := fmt.Sprintf("Payment Reminder - Invoice %s", invoiceRef)

	return mailService.SendTemplatedEmail(invoice.Client.Email, subject, func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Dear %s,", invoice.Client.Name)).
			AddContent(
				"We hope this message finds you well.",
				"",
				fmt.Sprintf("This is a friendly reminder that Invoice %s was due on %s.", invoiceRef, dueDate),
				"",
				"Invoice Details:",
				fmt.Sprintf("• Invoice Number: %s", invoiceRef),
				fmt.Sprintf("• Due Date: %s", dueDate),
				fmt.Sprintf("• Outstanding Amount: %s%.2f", currency, balance),
				"",
				"If you have already made this payment, please disregard this reminder.",
				"If you have any questions about this invoice, please don't hesitate to contact us.",
				"",
				"Thank you for your business!",
			).
			SetAction("View Invoice", template.AppURL+"/invoices/"+invoiceId)
	})
}

// sendSecondOverdueReminderWithoutAttachment sends the second reminder without PDF attachment as fallback
func sendSecondOverdueReminderWithoutAttachment(invoice models.Invoice) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Get invoice details
	invoiceRef := "N/A"
	if invoice.Reference != nil {
		invoiceRef = *invoice.Reference
	}

	dueDate := "N/A"
	if invoice.DueDate != nil {
		dueDate = invoice.DueDate.Format("January 2, 2006")
	}

	balance := 0.0
	if invoice.Balance != nil {
		balance = *invoice.Balance
	}

	currency := "$"
	if invoice.CurrencyId != nil {
		currency = invoice.Currency.Symbol
	}

	invoiceId := strconv.Itoa(int(invoice.Id))
	subject := fmt.Sprintf("URGENT: Payment Overdue - Invoice %s", invoiceRef)

	return mailService.SendTemplatedEmail(invoice.Client.Email, subject, func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Dear %s,", invoice.Client.Name)).
			AddContent(
				"This is an urgent reminder regarding your overdue payment.",
				"",
				fmt.Sprintf("Invoice %s was due on %s and remains unpaid.", invoiceRef, dueDate),
				"",
				"Invoice Details:",
				fmt.Sprintf("• Invoice Number: %s", invoiceRef),
				fmt.Sprintf("• Due Date: %s", dueDate),
				fmt.Sprintf("• Outstanding Amount: %s%.2f", currency, balance),
				"",
				"⚠️ IMPORTANT: Please arrange payment immediately to avoid any service interruption.",
				"",
				"If you are experiencing difficulties with payment, please contact us immediately to discuss payment arrangements.",
				"If payment has already been made, please provide us with the transaction details.",
				"",
				"We appreciate your immediate attention to this matter.",
			).
			SetAction("Pay Now", template.AppURL+"/invoices/"+invoiceId)
	})
}

// sendThirdOverdueReminderWithoutAttachment sends the third reminder without PDF attachment as fallback
func sendThirdOverdueReminderWithoutAttachment(invoice models.Invoice) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Get invoice details
	invoiceRef := "N/A"
	if invoice.Reference != nil {
		invoiceRef = *invoice.Reference
	}

	dueDate := "N/A"
	if invoice.DueDate != nil {
		dueDate = invoice.DueDate.Format("January 2, 2006")
	}

	balance := 0.0
	if invoice.Balance != nil {
		balance = *invoice.Balance
	}

	currency := "$"
	if invoice.CurrencyId != nil {
		currency = invoice.Currency.Symbol
	}

	invoiceId := strconv.Itoa(int(invoice.Id))
	subject := fmt.Sprintf("FINAL NOTICE: Payment Required - Invoice %s", invoiceRef)

	return mailService.SendTemplatedEmail(invoice.Client.Email, subject, func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Dear %s,", invoice.Client.Name)).
			AddContent(
				"🚨 FINAL NOTICE - IMMEDIATE ACTION REQUIRED",
				"",
				fmt.Sprintf("This is our final reminder regarding Invoice %s, which was due on %s.", invoiceRef, dueDate),
				"",
				"Invoice Details:",
				fmt.Sprintf("• Invoice Number: %s", invoiceRef),
				fmt.Sprintf("• Due Date: %s", dueDate),
				fmt.Sprintf("• Outstanding Amount: %s%.2f", currency, balance),
				"",
				"⚠️ WARNING: Failure to pay this invoice immediately may result in:",
				"• Service suspension",
				"• Additional late fees",
				"• Collection proceedings",
				"• Account termination",
				"",
				"If payment is not received within 24 hours, your account may be suspended.",
				"",
				"If you are unable to pay the full amount, please contact us immediately to discuss payment arrangements.",
				"If payment has been made, please provide transaction details immediately.",
				"",
				"This is your final opportunity to resolve this matter before further action is taken.",
			).
			SetAction("PAY IMMEDIATELY", template.AppURL+"/invoices/"+invoiceId)
	})
}


