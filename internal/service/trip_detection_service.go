package service

import (
	"fmt"
	"log"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"

	"gorm.io/gorm"
)

// TripDetectionService handles trip detection logic
type TripDetectionService struct {
	idleTimeoutMinutes int
	minSpeedThreshold  float64
	minDistanceMeters  float64
	lookaheadPoints    int
	batchSize          int
}

// NewTripDetectionService creates a new trip detection service with settings
func NewTripDetectionService() *TripDetectionService {
	service := &TripDetectionService{}
	service.loadSettings()
	return service
}

// loadSettings loads trip detection settings from database
func (s *TripDetectionService) loadSettings() {
	// Load idle timeout (default: 5 minutes)
	if timeoutStr := models.GetSetting("trip_detection_idle_timeout_minutes"); timeoutStr != "" {
		if timeout, err := strconv.Atoi(timeoutStr); err == nil {
			s.idleTimeoutMinutes = timeout
		} else {
			s.idleTimeoutMinutes = 5
		}
	} else {
		s.idleTimeoutMinutes = 5
	}

	// Load minimum speed threshold (default: 1 km/h)
	if speedStr := models.GetSetting("trip_detection_min_speed_threshold"); speedStr != "" {
		if speed, err := strconv.ParseFloat(speedStr, 64); err == nil {
			s.minSpeedThreshold = speed
		} else {
			s.minSpeedThreshold = 1.0
		}
	} else {
		s.minSpeedThreshold = 1.0
	}

	// Load minimum distance (default: 100 meters)
	if distStr := models.GetSetting("trip_detection_min_distance_meters"); distStr != "" {
		if dist, err := strconv.ParseFloat(distStr, 64); err == nil {
			s.minDistanceMeters = dist
		} else {
			s.minDistanceMeters = 100.0
		}
	} else {
		s.minDistanceMeters = 100.0
	}

	// Load lookahead points (default: 3)
	if lookaheadStr := models.GetSetting("trip_detection_lookahead_points"); lookaheadStr != "" {
		if lookahead, err := strconv.Atoi(lookaheadStr); err == nil {
			s.lookaheadPoints = lookahead
		} else {
			s.lookaheadPoints = 3
		}
	} else {
		s.lookaheadPoints = 3
	}

	// Load batch size (default: 1000)
	if batchStr := models.GetSetting("trip_detection_batch_size"); batchStr != "" {
		if batch, err := strconv.Atoi(batchStr); err == nil {
			s.batchSize = batch
		} else {
			s.batchSize = 1000
		}
	} else {
		s.batchSize = 1000
	}

	log.Printf("Trip detection settings loaded: idle_timeout=%dm, min_speed=%.1f, min_distance=%.0fm, lookahead=%d, batch=%d",
		s.idleTimeoutMinutes, s.minSpeedThreshold, s.minDistanceMeters, s.lookaheadPoints, s.batchSize)
	log.Printf("Trip detection will use ignition status when available for more accurate idle time detection")
}

// ProcessUnassignedGPSData processes GPS data that hasn't been assigned to trips
func (s *TripDetectionService) ProcessUnassignedGPSData() error {
	return s.ProcessUnassignedGPSDataWithDateFilter("", "")
}

// ProcessUnassignedGPSDataWithDateFilter processes GPS data within a date range
func (s *TripDetectionService) ProcessUnassignedGPSDataWithDateFilter(startDate, endDate string) error {
	log.Println("Starting trip detection processing...")

	// Get all devices that have unprocessed GPS data
	var deviceIds []uint
	var query string
	if startDate != "" && endDate != "" {
		query = `
			SELECT DISTINCT client_device_id
			FROM gps_data
			WHERE trip_id IS NULL
			AND client_device_id IS NOT NULL
			AND gps_timestamp >= '` + startDate + `'
			AND gps_timestamp < '` + endDate + `'
			ORDER BY client_device_id
		`
		log.Printf("Processing GPS data from %s to %s", startDate, endDate)
	} else {
		query = `
			SELECT DISTINCT client_device_id
			FROM gps_data
			WHERE trip_id IS NULL
			AND client_device_id IS NOT NULL
			ORDER BY client_device_id
		`
	}

	err := config.DB.Raw(query).Scan(&deviceIds).Error

	if err != nil {
		return fmt.Errorf("failed to get devices with unprocessed GPS data: %v", err)
	}

	log.Printf("Found %d devices with unprocessed GPS data", len(deviceIds))

	// Process each device separately
	for _, deviceId := range deviceIds {
		err := s.processDeviceGPSDataWithDateFilter(deviceId, startDate, endDate)
		if err != nil {
			log.Printf("Error processing device %d: %v", deviceId, err)
			continue
		}
	}

	log.Println("Trip detection processing completed")
	return nil
}

// processDeviceGPSData processes unassigned GPS data for a specific device
func (s *TripDetectionService) processDeviceGPSData(deviceId uint) error {
	return s.processDeviceGPSDataWithDateFilter(deviceId, "", "")
}

// processDeviceGPSDataWithDateFilter processes unassigned GPS data for a specific device within date range
func (s *TripDetectionService) processDeviceGPSDataWithDateFilter(deviceId uint, startDate, endDate string) error {
	log.Printf("Processing GPS data for device %d", deviceId)

	// Get unprocessed GPS data for this device, ordered by timestamp
	var gpsData []models.GPSData
	query := config.DB.Where("client_device_id = ? AND trip_id IS NULL", deviceId)

	// Add date filtering if specified
	if startDate != "" && endDate != "" {
		query = query.Where("gps_timestamp >= ? AND gps_timestamp < ?", startDate, endDate)
	}

	err := query.Order("gps_timestamp ASC").
		Limit(s.batchSize).
		Find(&gpsData).Error

	if err != nil {
		return fmt.Errorf("failed to fetch GPS data for device %d: %v", deviceId, err)
	}

	if len(gpsData) == 0 {
		return nil
	}

	log.Printf("Processing %d GPS points for device %d", len(gpsData), deviceId)

	// Process the GPS data to detect trips
	trips, err := s.detectTrips(gpsData)
	if err != nil {
		return fmt.Errorf("failed to detect trips for device %d: %v", deviceId, err)
	}

	log.Printf("Detected %d trips for device %d", len(trips), deviceId)

	// Save detected trips and update GPS data
	successfulTrips := 0
	for _, trip := range trips {
		err := s.saveTripAndUpdateGPSData(trip)
		if err != nil {
			log.Printf("Failed to save trip for device %d: %v", deviceId, err)
			continue
		}
		successfulTrips++
	}

	if successfulTrips > 0 {
		log.Printf("Successfully saved %d/%d trips for device %d", successfulTrips, len(trips), deviceId)
	}

	return nil
}

// TripCandidate represents a potential trip during detection
type TripCandidate struct {
	DeviceId   uint
	StartIndex int
	EndIndex   int
	GPSPoints  []models.GPSData
	StartTime  time.Time
	EndTime    time.Time
	Distance   float64
	MaxSpeed   float64
	TotalSpeed float64
	SpeedCount int
	IdleTime   int
}

// detectTrips analyzes GPS data and returns detected trips
func (s *TripDetectionService) detectTrips(gpsData []models.GPSData) ([]TripCandidate, error) {
	if len(gpsData) == 0 {
		return nil, nil
	}

	var trips []TripCandidate
	var currentTrip *TripCandidate

	for i, point := range gpsData {
		speed := 0.0
		if point.Speed != nil {
			speed = *point.Speed
		}

		// Check if this point should start a new trip
		if currentTrip == nil && s.shouldStartTrip(point, speed, gpsData, i) {
			currentTrip = &TripCandidate{
				DeviceId:   *point.ClientDeviceId,
				StartIndex: i,
				GPSPoints:  []models.GPSData{point},
				StartTime:  *point.GPSTimestamp,
				MaxSpeed:   speed,
				TotalSpeed: speed,
				SpeedCount: 1,
			}
			continue
		}

		// If we have an active trip, check if it should continue or end
		if currentTrip != nil {
			// Add point to current trip
			currentTrip.GPSPoints = append(currentTrip.GPSPoints, point)
			currentTrip.EndIndex = i
			currentTrip.EndTime = *point.GPSTimestamp

			// Update trip metrics
			if speed > currentTrip.MaxSpeed {
				currentTrip.MaxSpeed = speed
			}
			if speed > 0 {
				currentTrip.TotalSpeed += speed
				currentTrip.SpeedCount++
			}

			// Calculate distance from previous point
			if len(currentTrip.GPSPoints) > 1 {
				prevPoint := currentTrip.GPSPoints[len(currentTrip.GPSPoints)-2]
				distance := utils.HaversineDistance(prevPoint.Latitude, prevPoint.Longitude, point.Latitude, point.Longitude)
				currentTrip.Distance += distance
			}

			// Check if trip should end
			if s.shouldEndTrip(currentTrip, gpsData, i) {
				// Validate trip before adding
				if s.isValidTrip(currentTrip) {
					trips = append(trips, *currentTrip)
				}
				currentTrip = nil
			}
		}
	}

	// Handle case where trip is still active at end of data
	if currentTrip != nil && s.isValidTrip(currentTrip) {
		trips = append(trips, *currentTrip)
	}

	return trips, nil
}

// shouldStartTrip determines if a trip should start at this GPS point
func (s *TripDetectionService) shouldStartTrip(point models.GPSData, speed float64, allData []models.GPSData, index int) bool {
	// Primary signal: Ignition ON (most reliable for supported devices)
	if point.IgnitionStatus != nil && *point.IgnitionStatus {
		log.Printf("Trip start detected: Ignition ON at %v", point.GPSTimestamp)
		return true
	}

	// Fallback for devices without ignition status: speed > threshold
	if point.IgnitionStatus == nil && speed > s.minSpeedThreshold {
		return true
	}

	// Secondary signal: significant movement detected (for devices without ignition)
	if point.IgnitionStatus == nil && index > 0 {
		prevPoint := allData[index-1]
		distance := utils.HaversineDistance(prevPoint.Latitude, prevPoint.Longitude, point.Latitude, point.Longitude)

		// Convert distance from km to meters
		distanceMeters := distance * 1000

		// Check if movement is significant and recent
		if distanceMeters > s.minDistanceMeters {
			timeDiff := point.GPSTimestamp.Sub(*prevPoint.GPSTimestamp)
			if timeDiff <= 2*time.Minute { // Movement within 2 minutes
				return true
			}
		}
	}

	return false
}

// shouldEndTrip determines if a trip should end at this GPS point
func (s *TripDetectionService) shouldEndTrip(trip *TripCandidate, allData []models.GPSData, currentIndex int) bool {
	currentPoint := allData[currentIndex]
	speed := 0.0
	if currentPoint.Speed != nil {
		speed = *currentPoint.Speed
	}

	// Primary signal: Ignition OFF (most reliable for supported devices)
	if currentPoint.IgnitionStatus != nil && !*currentPoint.IgnitionStatus {
		log.Printf("Trip end detected: Ignition OFF at %v", currentPoint.GPSTimestamp)
		return true
	}

	// Fallback for devices without ignition status: use speed and time-based detection
	if currentPoint.IgnitionStatus == nil {
		// If speed is 0, check for idle timeout
		if speed == 0 {
			// Look ahead to see if movement resumes soon
			if s.hasMovementAhead(allData, currentIndex) {
				return false
			}

			// Check if we've been idle for too long
			idleDuration := s.calculateIdleDuration(trip, currentIndex)
			if idleDuration >= time.Duration(s.idleTimeoutMinutes)*time.Minute {
				return true
			}
		}
	}

	return false
}

// hasMovementAhead checks if there's movement in the next few GPS points
func (s *TripDetectionService) hasMovementAhead(allData []models.GPSData, currentIndex int) bool {
	endIndex := currentIndex + s.lookaheadPoints
	if endIndex >= len(allData) {
		endIndex = len(allData) - 1
	}

	for i := currentIndex + 1; i <= endIndex; i++ {
		// Check ignition status first (most reliable)
		if allData[i].IgnitionStatus != nil && *allData[i].IgnitionStatus {
			return true
		}

		// Fallback to speed-based detection for devices without ignition
		if allData[i].IgnitionStatus == nil && allData[i].Speed != nil && *allData[i].Speed > s.minSpeedThreshold {
			return true
		}

		// Also check for significant position change (for devices without ignition)
		if allData[i].IgnitionStatus == nil && i > currentIndex {
			distance := utils.HaversineDistance(
				allData[currentIndex].Latitude, allData[currentIndex].Longitude,
				allData[i].Latitude, allData[i].Longitude,
			)
			if distance*1000 > s.minDistanceMeters {
				return true
			}
		}
	}

	return false
}

// calculateIdleDuration calculates how long the vehicle has been idle
func (s *TripDetectionService) calculateIdleDuration(trip *TripCandidate, currentIndex int) time.Duration {
	if len(trip.GPSPoints) == 0 {
		return 0
	}

	// Find the last point with movement (prioritize ignition status if available)
	lastMovementTime := trip.StartTime
	for i := len(trip.GPSPoints) - 1; i >= 0; i-- {
		point := trip.GPSPoints[i]

		// If ignition status is available, use it
		if point.IgnitionStatus != nil {
			if *point.IgnitionStatus {
				lastMovementTime = *point.GPSTimestamp
				break
			}
		} else {
			// Fallback to speed-based detection
			if point.Speed != nil && *point.Speed > s.minSpeedThreshold {
				lastMovementTime = *point.GPSTimestamp
				break
			}
		}
	}

	currentTime := *trip.GPSPoints[len(trip.GPSPoints)-1].GPSTimestamp
	return currentTime.Sub(lastMovementTime)
}

// isValidTrip validates if a detected trip meets minimum criteria
func (s *TripDetectionService) isValidTrip(trip *TripCandidate) bool {
	// Minimum duration (e.g., 30 seconds)
	duration := trip.EndTime.Sub(trip.StartTime)
	if duration < 30*time.Second {
		return false
	}

	// Minimum distance (convert meters to km)
	minDistanceKm := s.minDistanceMeters / 1000.0
	if trip.Distance < minDistanceKm {
		return false
	}

	// Must have at least 2 GPS points
	if len(trip.GPSPoints) < 2 {
		return false
	}

	return true
}

// calculateTripIdleTime calculates total idle time during a trip using ignition status
func (s *TripDetectionService) calculateTripIdleTime(gpsPoints []models.GPSData) int {
	if len(gpsPoints) < 2 {
		return 0
	}

	var totalIdleTime time.Duration
	var idleStartTime *time.Time

	for _, point := range gpsPoints {
		// Skip if no timestamp
		if point.GPSTimestamp == nil {
			continue
		}

		// Use ignition status if available, otherwise fall back to speed
		isIdle := false
		if point.IgnitionStatus != nil {
			// Ignition OFF = idle
			isIdle = !*point.IgnitionStatus
		} else if point.Speed != nil {
			// Speed = 0 = idle (fallback for devices without ignition)
			isIdle = *point.Speed == 0
		}

		if isIdle {
			// Start tracking idle time if not already tracking
			if idleStartTime == nil {
				idleStartTime = point.GPSTimestamp
			}
		} else {
			// End idle period if we were tracking one
			if idleStartTime != nil {
				idleDuration := point.GPSTimestamp.Sub(*idleStartTime)
				totalIdleTime += idleDuration
				idleStartTime = nil
			}
		}
	}

	// Handle case where trip ends while still idle
	if idleStartTime != nil && len(gpsPoints) > 0 {
		lastPoint := gpsPoints[len(gpsPoints)-1]
		if lastPoint.GPSTimestamp != nil {
			idleDuration := lastPoint.GPSTimestamp.Sub(*idleStartTime)
			totalIdleTime += idleDuration
		}
	}

	return int(totalIdleTime.Seconds())
}

// saveTripAndUpdateGPSData saves a detected trip and updates GPS data with trip_id
func (s *TripDetectionService) saveTripAndUpdateGPSData(trip TripCandidate) error {
	// Start transaction
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get start and end locations using reverse geocoding
	startLocation, _ := s.getLocationName(trip.GPSPoints[0].Latitude, trip.GPSPoints[0].Longitude)
	endLocation, _ := s.getLocationName(trip.GPSPoints[len(trip.GPSPoints)-1].Latitude, trip.GPSPoints[len(trip.GPSPoints)-1].Longitude)

	// Calculate average speed
	avgSpeed := 0.0
	if trip.SpeedCount > 0 {
		avgSpeed = trip.TotalSpeed / float64(trip.SpeedCount)
	}

	// Calculate duration in seconds
	duration := int(trip.EndTime.Sub(trip.StartTime).Seconds())

	// Calculate idle time using ignition status (more accurate than speed-based)
	idleTime := s.calculateTripIdleTime(trip.GPSPoints)

	// Get current driver assignment for the device (optional - don't fail if not found)
	var driverId *uint
	if driver, err := GetCurrentDriverForDevice(trip.DeviceId, trip.StartTime); err == nil && driver != nil {
		driverId = &driver.Id
	}
	// Note: It's normal for trips to not have driver assignments, especially for older GPS data

	// Create the trip record
	deviceTrip := models.Trip{
		ClientDeviceId: trip.DeviceId,
		DriverId:       driverId,
		StartTime:      trip.StartTime,
		EndTime:        &trip.EndTime,
		Duration:       &duration,
		StartLatitude:  trip.GPSPoints[0].Latitude,
		StartLongitude: trip.GPSPoints[0].Longitude,
		StartLocation:  startLocation,
		EndLatitude:    &trip.GPSPoints[len(trip.GPSPoints)-1].Latitude,
		EndLongitude:   &trip.GPSPoints[len(trip.GPSPoints)-1].Longitude,
		EndLocation:    endLocation,
		Distance:       trip.Distance,
		MaxSpeed:       &trip.MaxSpeed,
		AvgSpeed:       &avgSpeed,
		IdleTime:       &idleTime,
		Status:         "completed",
	}

	// Save the trip
	if err := tx.Create(&deviceTrip).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create trip: %v", err)
	}

	// Update ALL GPS data points within the trip timeframe with trip_id
	// This ensures complete trip replay data including stationary points
	if err := tx.Model(&models.GPSData{}).
		Where("client_device_id = ? AND gps_timestamp >= ? AND gps_timestamp <= ? AND trip_id IS NULL",
			deviceTrip.ClientDeviceId, deviceTrip.StartTime, *deviceTrip.EndTime).
		Update("trip_id", deviceTrip.Id).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update GPS data with trip_id: %v", err)
	}

	// Update daily stats
	if err := s.updateDailyStatsForTrip(tx, deviceTrip); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update daily stats: %v", err)
	}

	// Send trip notifications if enabled
	go s.sendTripNotifications(deviceTrip)

	// Process driving behavior for this trip
	go s.processDrivingBehavior(deviceTrip)

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit trip transaction: %v", err)
	}

	log.Printf("Created trip %d for device %d: %.2fkm, %ds duration",
		deviceTrip.Id, deviceTrip.ClientDeviceId, deviceTrip.Distance, duration)

	return nil
}

// getLocationName gets location name for coordinates using reverse geocoding
func (s *TripDetectionService) getLocationName(latitude, longitude float64) (*string, error) {
	geocodingService := NewReverseGeocodingService()
	locationName, err := geocodingService.GetLocationName(latitude, longitude)
	if err != nil {
		return nil, err
	}
	return &locationName, nil
}

// updateDailyStatsForTrip increments trip count in daily stats
func (s *TripDetectionService) updateDailyStatsForTrip(tx *gorm.DB, trip models.Trip) error {
	// Get the date for the trip start
	date := trip.StartTime.Truncate(24 * time.Hour)

	// Use MySQL's ON DUPLICATE KEY UPDATE for atomic upsert
	result := tx.Exec(`
		INSERT INTO client_device_daily_stats (client_device_id, date, distance, trips, alerts, warnings, critical, created_at, updated_at)
		VALUES (?, ?, 0, 1, 0, 0, 0, NOW(), NOW())
		ON DUPLICATE KEY UPDATE
			trips = trips + 1,
			updated_at = NOW()
	`, trip.ClientDeviceId, date)

	return result.Error
}

// sendTripNotifications sends trip start/end notifications if enabled
func (s *TripDetectionService) sendTripNotifications(trip models.Trip) {
	// Get client device with settings
	var clientDevice models.ClientDevice
	err := config.DB.Preload("Client").First(&clientDevice, trip.ClientDeviceId).Error
	if err != nil {
		log.Printf("Failed to load client device for trip notifications: %v", err)
		return
	}

	// Check if trip events are enabled
	if !clientDevice.TripStartEvents && !clientDevice.TripEndEvents {
		return
	}

	// Create trip start alert
	if clientDevice.TripStartEvents {
		startAlert := models.Alert{
			ClientDeviceId: trip.ClientDeviceId,
			DeviceId:       &clientDevice.DeviceId,
			AlertType:      "trip_start",
			AlertName:      stringPtrTrip("Trip Started"),
			AlertLevel:     stringPtrTrip("info"),
			Message:        stringPtrTrip(fmt.Sprintf("Trip started at %s", formatLocationName(trip.StartLocation))),
			AlertTimestamp: trip.StartTime,
		}

		if err := config.DB.Create(&startAlert).Error; err != nil {
			log.Printf("Failed to create trip start alert: %v", err)
		}
	}

	// Create trip end alert
	if clientDevice.TripEndEvents && trip.EndTime != nil {
		endAlert := models.Alert{
			ClientDeviceId: trip.ClientDeviceId,
			DeviceId:       &clientDevice.DeviceId,
			AlertType:      "trip_end",
			AlertName:      stringPtrTrip("Trip Ended"),
			AlertLevel:     stringPtrTrip("info"),
			Message: stringPtrTrip(fmt.Sprintf("Trip ended at %s. Distance: %.2fkm, Duration: %s",
				formatLocationName(trip.EndLocation), trip.Distance, formatDuration(trip.Duration))),
			AlertTimestamp: *trip.EndTime,
		}

		if err := config.DB.Create(&endAlert).Error; err != nil {
			log.Printf("Failed to create trip end alert: %v", err)
		}
	}
}

// Helper functions
func stringPtrTrip(s string) *string {
	return &s
}

func formatLocationName(location *string) string {
	if location != nil && *location != "" {
		return *location
	}
	return "Unknown Location"
}

func formatDuration(durationPtr *int) string {
	if durationPtr == nil {
		return "Unknown"
	}
	duration := time.Duration(*durationPtr) * time.Second
	hours := int(duration.Hours())
	minutes := int(duration.Minutes()) % 60

	if hours > 0 {
		return fmt.Sprintf("%dh %dm", hours, minutes)
	}
	return fmt.Sprintf("%dm", minutes)
}

// processDrivingBehavior processes driving behavior for a completed trip
func (s *TripDetectionService) processDrivingBehavior(trip models.Trip) {
	behaviorService := NewDrivingBehaviorService()
	err := behaviorService.ProcessTripBehavior(trip)
	if err != nil {
		log.Printf("Failed to process driving behavior for trip %d: %v", trip.Id, err)
	}
}
