package service

import (
	"fmt"
	"log"
	"yotracker/internal/models"
)

// SendMultiChannelAlert sends alerts through all enabled channels for a client
func SendMultiChannelAlert(client *models.Client, alert *models.Alert, clientDevice *models.ClientDevice) error {
	return SendAlertToClient(client, alert, clientDevice)
}

// SendSMSAlert sends an SMS alert to a client
func SendSMSAlert(client *models.Client, alert *models.Alert, clientDevice *models.ClientDevice) error {
	smsService, err := NewTwilioSMSService()
	if err != nil {
		return fmt.Errorf("failed to initialize SMS service: %v", err)
	}

	return smsService.SendAlert(client, alert, clientDevice)
}

// SendWhatsAppAlert sends a WhatsApp alert to a client
func SendWhatsAppAlert(client *models.Client, alert *models.Alert, clientDevice *models.ClientDevice) error {
	whatsappService, err := NewWhatsAppService()
	if err != nil {
		return fmt.Errorf("failed to initialize WhatsApp service: %v", err)
	}

	return whatsappService.SendAlert(client, alert, clientDevice)
}

// SendEmailAlert sends an email alert to a client
func SendEmailAlert(client *models.Client, alert *models.Alert, clientDevice *models.ClientDevice) error {
	emailService, err := NewEmailAlertService()
	if err != nil {
		return fmt.Errorf("failed to initialize email service: %v", err)
	}

	return emailService.SendAlert(client, alert, clientDevice)
}

// SendAlertToClientByType sends a specific type of alert to a client
func SendAlertToClientByType(client *models.Client, alert *models.Alert, clientDevice *models.ClientDevice, alertType string) error {
	switch alertType {
	case "sms":
		return SendSMSAlert(client, alert, clientDevice)
	case "whatsapp":
		return SendWhatsAppAlert(client, alert, clientDevice)
	case "email":
		return SendEmailAlert(client, alert, clientDevice)
	case "all":
		return SendMultiChannelAlert(client, alert, clientDevice)
	default:
		return fmt.Errorf("unsupported alert type: %s", alertType)
	}
}

// SendDeviceAlertWithSlack sends both email and Slack notifications
func SendDeviceAlertWithSlack(userEmail, deviceId, alertType, alertMessage string, alert *models.Alert, clientDevice *models.ClientDevice) error {
	// Send email alert using the existing mail helper
	// Note: This would need to be implemented in the mail package to avoid import cycles

	// Send Slack notification
	slackService, err := NewSlackService()
	if err != nil {
		log.Printf("Failed to initialize Slack service: %v", err)
		return err
	}

	if err := slackService.SendAlert(alert, clientDevice); err != nil {
		log.Printf("Failed to send Slack alert: %v", err)
		return err
	}

	return nil
}
