package service

import (
	"os"
	"strings"
	"testing"
	"yotracker/internal/models"
)

// isValidEmail performs basic email validation (copied from models/mail.go for testing)
func isValidEmail(email string) bool {
	email = strings.TrimSpace(email)
	if email == "" {
		return false
	}

	// Basic email validation - contains @ and has parts before and after
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}

	if len(parts[0]) == 0 || len(parts[1]) == 0 {
		return false
	}

	// Check for domain part having at least one dot
	if !strings.Contains(parts[1], ".") {
		return false
	}

	return true
}

func TestNewMailService(t *testing.T) {
	// Test without environment variables
	os.Unsetenv("SENDGRID_API_KEY")
	os.Unsetenv("FROM_EMAIL")

	_, err := NewMailService()
	if err == nil {
		t.Error("Expected error when SENDGRID_API_KEY is not set")
	}

	// Test with SENDGRID_API_KEY but without FROM_EMAIL
	os.Setenv("SENDGRID_API_KEY", "test-key")
	_, err = NewMailService()
	if err == nil {
		t.Error("Expected error when FROM_EMAIL is not set")
	}

	// Test with both environment variables
	os.Setenv("FROM_EMAIL", "<EMAIL>")
	os.Setenv("FROM_NAME", "Test Sender")

	service, err := NewMailService()
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if service == nil {
		t.Error("Expected service to be created")
	}

	if service.fromEmail != "<EMAIL>" {
		t.Errorf("Expected fromEmail to be '<EMAIL>', got: %s", service.fromEmail)
	}

	if service.fromName != "Test Sender" {
		t.Errorf("Expected fromName to be 'Test Sender', got: %s", service.fromName)
	}

	// Test with default FROM_NAME
	os.Unsetenv("FROM_NAME")
	service, err = NewMailService()
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if service.fromName != "YoTracker" {
		t.Errorf("Expected default fromName to be 'YoTracker', got: %s", service.fromName)
	}
}

func TestMailValidation(t *testing.T) {
	// Test valid mail
	mail := models.NewMail([]string{"<EMAIL>"}, "Test Subject")
	mail.SetTextBody("Test message")

	err := mail.Validate()
	if err != nil {
		t.Errorf("Expected no error for valid mail, got: %v", err)
	}

	// Test mail without recipients
	mail = &models.Mail{
		Subject:  "Test Subject",
		TextBody: "Test message",
	}

	err = mail.Validate()
	if err == nil {
		t.Error("Expected error for mail without recipients")
	}

	// Test mail without subject
	mail = &models.Mail{
		To:       []string{"<EMAIL>"},
		TextBody: "Test message",
	}

	err = mail.Validate()
	if err == nil {
		t.Error("Expected error for mail without subject")
	}

	// Test mail without body
	mail = &models.Mail{
		To:      []string{"<EMAIL>"},
		Subject: "Test Subject",
	}

	err = mail.Validate()
	if err == nil {
		t.Error("Expected error for mail without body")
	}

	// Test mail with invalid email
	mail = &models.Mail{
		To:       []string{"invalid-email"},
		Subject:  "Test Subject",
		TextBody: "Test message",
	}

	err = mail.Validate()
	if err == nil {
		t.Error("Expected error for mail with invalid email")
	}
}

func TestEmailValidation(t *testing.T) {
	testCases := []struct {
		email    string
		expected bool
	}{
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"invalid-email", false},
		{"@example.com", false},
		{"test@", false},
		{"", false},
		{"test.example.com", false},
		{"test@example", false},
	}

	for _, tc := range testCases {
		result := isValidEmail(tc.email)
		if result != tc.expected {
			t.Errorf("For email '%s', expected %v, got %v", tc.email, tc.expected, result)
		}
	}
}

func TestMailMethods(t *testing.T) {
	mail := models.NewMail([]string{"<EMAIL>"}, "Test Subject")

	// Test SetHTMLBody
	mail.SetHTMLBody("<h1>Test HTML</h1>")
	if mail.HTMLBody != "<h1>Test HTML</h1>" {
		t.Errorf("Expected HTML body to be set")
	}

	// Test SetTextBody
	mail.SetTextBody("Test text")
	if mail.TextBody != "Test text" {
		t.Errorf("Expected text body to be set")
	}

	// Test AddAttachment
	content := []byte("test content")
	mail.AddAttachment("test.txt", content, "text/plain")

	if len(mail.Attachments) != 1 {
		t.Errorf("Expected 1 attachment, got %d", len(mail.Attachments))
	}

	attachment := mail.Attachments[0]
	if attachment.Filename != "test.txt" {
		t.Errorf("Expected filename 'test.txt', got '%s'", attachment.Filename)
	}

	if attachment.ContentType != "text/plain" {
		t.Errorf("Expected content type 'text/plain', got '%s'", attachment.ContentType)
	}

	// Test AddAttachment with default content type
	mail.AddAttachment("test.bin", content, "")
	if len(mail.Attachments) != 2 {
		t.Errorf("Expected 2 attachments, got %d", len(mail.Attachments))
	}

	if mail.Attachments[1].ContentType != "application/octet-stream" {
		t.Errorf("Expected default content type 'application/octet-stream', got '%s'", mail.Attachments[1].ContentType)
	}
}

func TestNewMailFromRequest(t *testing.T) {
	req := &models.MailRequest{
		To:      []string{"<EMAIL>"},
		CC:      []string{"<EMAIL>"},
		Subject: "Test Subject",
		Message: "Test message",
		IsHTML:  true,
	}

	mail := models.NewMailFromRequest(req)

	if len(mail.To) != 1 || mail.To[0] != "<EMAIL>" {
		t.Errorf("Expected To to be set correctly")
	}

	if len(mail.CC) != 1 || mail.CC[0] != "<EMAIL>" {
		t.Errorf("Expected CC to be set correctly")
	}

	if mail.Subject != "Test Subject" {
		t.Errorf("Expected subject to be set correctly")
	}

	if mail.HTMLBody != "Test message" {
		t.Errorf("Expected HTML body to be set when IsHTML is true")
	}

	if mail.TextBody != "" {
		t.Errorf("Expected text body to be empty when IsHTML is true")
	}

	// Test with IsHTML false
	req.IsHTML = false
	mail = models.NewMailFromRequest(req)

	if mail.TextBody != "Test message" {
		t.Errorf("Expected text body to be set when IsHTML is false")
	}

	if mail.HTMLBody != "" {
		t.Errorf("Expected HTML body to be empty when IsHTML is false")
	}
}
