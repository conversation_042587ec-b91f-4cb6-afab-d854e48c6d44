package service

import (
	"fmt"
	"log"
	"math"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

// DrivingBehaviorService handles driving behavior detection and analysis
type DrivingBehaviorService struct {
	// Thresholds for behavior detection
	harshAccelThreshold      float64 // m/s²
	harshBrakeThreshold      float64 // m/s²
	harshCorneringThreshold  float64 // degrees per second at speed
	fatigueDrivingThreshold  int     // minutes
	excessiveIdlingThreshold int     // minutes
	rapidLaneChangeThreshold float64 // bearing variance threshold
}

// NewDrivingBehaviorService creates a new driving behavior service
func NewDrivingBehaviorService() *DrivingBehaviorService {
	return &DrivingBehaviorService{
		harshAccelThreshold:      3.0,  // 3 m/s² acceleration
		harshBrakeThreshold:      -4.5, // -4.5 m/s² deceleration
		harshCorneringThreshold:  45.0, // 45 degrees/second at speed
		fatigueDrivingThreshold:  240,  // 4 hours
		excessiveIdlingThreshold: 10,   // 10 minutes
		rapidLaneChangeThreshold: 30.0, // 30 degrees variance
	}
}

// ProcessTripBehavior analyzes driving behavior for a completed trip
func (s *DrivingBehaviorService) ProcessTripBehavior(trip models.Trip) error {
	log.Printf("Processing driving behavior for trip %d", trip.Id)

	// Get all GPS points for this trip
	var gpsPoints []models.GPSData
	err := config.DB.Where("trip_id = ?", trip.Id).
		Order("gps_timestamp ASC").
		Find(&gpsPoints).Error
	if err != nil {
		return fmt.Errorf("failed to fetch GPS points for trip %d: %v", trip.Id, err)
	}

	if len(gpsPoints) < 2 {
		log.Printf("Trip %d has insufficient GPS points for behavior analysis", trip.Id)
		return nil
	}

	// Get max speed for this device (device -> fleet -> default)
	maxSpeed, err := s.getMaxSpeedForDevice(trip.ClientDeviceId)
	if err != nil {
		log.Printf("Warning: Could not get max speed for device %d, using default 120 km/h", trip.ClientDeviceId)
		maxSpeed = 120.0 // Default speed limit
	}

	// Get current driver for context
	var driverId *uint
	if trip.DriverId != nil {
		driverId = trip.DriverId
	}

	// Detect various driving behaviors
	events := s.detectDrivingBehaviors(gpsPoints, trip.Id, trip.ClientDeviceId, driverId, maxSpeed)

	// Save detected events
	for _, event := range events {
		err := s.saveDrivingBehaviorEvent(event)
		if err != nil {
			log.Printf("Failed to save driving behavior event: %v", err)
		}
	}

	log.Printf("Detected %d driving behavior events for trip %d", len(events), trip.Id)
	return nil
}

// getMaxSpeedForDevice gets max speed: device -> fleet -> default (120 km/h)
func (s *DrivingBehaviorService) getMaxSpeedForDevice(deviceId uint) (float64, error) {
	// First try to get from client device
	var clientDevice models.ClientDevice
	err := config.DB.Preload("Fleet").First(&clientDevice, deviceId).Error
	if err != nil {
		return 120.0, err
	}

	// Check device max speed first
	if clientDevice.MaxSpeed != nil && *clientDevice.MaxSpeed > 0 {
		return *clientDevice.MaxSpeed, nil
	}

	// Check fleet max speed if device is part of a fleet
	if clientDevice.FleetId != nil {
		var fleet models.Fleet
		err := config.DB.First(&fleet, *clientDevice.FleetId).Error
		if err == nil && fleet.MaxSpeed != nil && *fleet.MaxSpeed > 0 {
			return *fleet.MaxSpeed, nil
		}
	}

	// Default to 120 km/h if no specific limit is set
	return 120.0, nil
}

// detectDrivingBehaviors analyzes GPS points and detects various driving behaviors
func (s *DrivingBehaviorService) detectDrivingBehaviors(points []models.GPSData, tripId uint, deviceId uint, driverId *uint, maxSpeed float64) []models.DrivingBehaviorEvent {
	var events []models.DrivingBehaviorEvent

	// Track for pattern detection
	var bearingChanges []float64
	var idleStartTime *time.Time
	var lastBearing *float64

	for i := 1; i < len(points); i++ {
		prev := points[i-1]
		curr := points[i]

		// Skip if missing essential data
		if prev.GPSTimestamp == nil || curr.GPSTimestamp == nil {
			continue
		}

		timeDiff := curr.GPSTimestamp.Sub(*prev.GPSTimestamp).Seconds()
		if timeDiff <= 0 || timeDiff > 300 { // Skip if time diff is invalid or > 5 minutes
			continue
		}

		// Get speeds (default to 0 if nil)
		prevSpeed := 0.0
		currSpeed := 0.0
		if prev.Speed != nil {
			prevSpeed = *prev.Speed
		}
		if curr.Speed != nil {
			currSpeed = *curr.Speed
		}

		// 1. Overspeed Detection
		if currSpeed > maxSpeed {
			severity := s.calculateOverspeedSeverity(currSpeed, maxSpeed)
			events = append(events, s.createBehaviorEvent(
				deviceId, &tripId, driverId, models.EventTypeOverspeed,
				*curr.GPSTimestamp, curr.Latitude, curr.Longitude,
				&currSpeed, nil, nil, nil, &severity,
			))
		}

		// 2. Acceleration/Braking Detection
		if timeDiff > 0 {
			// Convert km/h to m/s for acceleration calculation
			prevSpeedMS := prevSpeed / 3.6
			currSpeedMS := currSpeed / 3.6
			acceleration := (currSpeedMS - prevSpeedMS) / timeDiff

			// Harsh Acceleration
			if acceleration >= s.harshAccelThreshold {
				severity := s.calculateAccelerationSeverity(acceleration, s.harshAccelThreshold)
				events = append(events, s.createBehaviorEvent(
					deviceId, &tripId, driverId, models.EventTypeHarshAcceleration,
					*curr.GPSTimestamp, curr.Latitude, curr.Longitude,
					&currSpeed, &acceleration, nil, nil, &severity,
				))
			}

			// Harsh Braking
			if acceleration <= s.harshBrakeThreshold {
				severity := s.calculateBrakingSeverity(acceleration, s.harshBrakeThreshold)
				events = append(events, s.createBehaviorEvent(
					deviceId, &tripId, driverId, models.EventTypeHarshBraking,
					*curr.GPSTimestamp, curr.Latitude, curr.Longitude,
					&currSpeed, &acceleration, nil, nil, &severity,
				))
			}
		}

		// 3. Cornering Detection
		bearing := utils.CalculateBearing(prev.Latitude, prev.Longitude, curr.Latitude, curr.Longitude)
		if lastBearing != nil && currSpeed > 30 { // Only check cornering at speed > 30 km/h
			bearingChange := utils.NormalizeBearingDifference(*lastBearing, bearing)
			bearingChanges = append(bearingChanges, math.Abs(bearingChange))

			if math.Abs(bearingChange) > s.harshCorneringThreshold && timeDiff > 0 {
				bearingRate := math.Abs(bearingChange) / timeDiff
				if bearingRate > 15 { // degrees per second
					severity := s.calculateCorneringSeverity(bearingRate)
					events = append(events, s.createBehaviorEvent(
						deviceId, &tripId, driverId, models.EventTypeHarshCornering,
						*curr.GPSTimestamp, curr.Latitude, curr.Longitude,
						&currSpeed, nil, &bearingChange, nil, &severity,
					))
				}
			}
		}
		lastBearing = &bearing

		// 4. Idling Detection
		if currSpeed == 0 {
			if idleStartTime == nil {
				idleStartTime = curr.GPSTimestamp
			}
		} else {
			if idleStartTime != nil {
				idleDuration := curr.GPSTimestamp.Sub(*idleStartTime)
				if idleDuration.Minutes() > float64(s.excessiveIdlingThreshold) {
					severity := s.calculateIdlingSeverity(int(idleDuration.Minutes()))
					durationSec := int(idleDuration.Seconds())
					events = append(events, s.createBehaviorEvent(
						deviceId, &tripId, driverId, models.EventTypeExcessiveIdling,
						*idleStartTime, prev.Latitude, prev.Longitude,
						nil, nil, nil, &durationSec, &severity,
					))
				}
				idleStartTime = nil
			}
		}
	}

	// 5. Rapid Lane Change Detection (analyze bearing variance over windows)
	events = append(events, s.detectRapidLaneChanges(points, tripId, deviceId, driverId)...)

	// 6. Fatigue Driving Detection
	if len(points) > 0 {
		tripDuration := points[len(points)-1].GPSTimestamp.Sub(*points[0].GPSTimestamp)
		if tripDuration.Minutes() > float64(s.fatigueDrivingThreshold) {
			severity := s.calculateFatigueSeverity(int(tripDuration.Minutes()))
			events = append(events, s.createBehaviorEvent(
				deviceId, &tripId, driverId, models.EventTypeFatigueDriving,
				*points[len(points)-1].GPSTimestamp, points[len(points)-1].Latitude, points[len(points)-1].Longitude,
				nil, nil, nil, nil, &severity,
			))
		}
	}

	return events
}

// Helper functions for the driving behavior service
func (s *DrivingBehaviorService) detectRapidLaneChanges(points []models.GPSData, tripId uint, deviceId uint, driverId *uint) []models.DrivingBehaviorEvent {
	var events []models.DrivingBehaviorEvent
	windowSize := 5 // Analyze 5-point windows

	for i := windowSize; i < len(points); i++ {
		window := points[i-windowSize : i]

		// Calculate bearing variance for this window
		var bearings []float64
		var avgSpeed float64
		speedCount := 0

		for j := 1; j < len(window); j++ {
			if window[j-1].GPSTimestamp != nil && window[j].GPSTimestamp != nil {
				bearing := utils.CalculateBearing(
					window[j-1].Latitude, window[j-1].Longitude,
					window[j].Latitude, window[j].Longitude,
				)
				bearings = append(bearings, bearing)

				if window[j].Speed != nil {
					avgSpeed += *window[j].Speed
					speedCount++
				}
			}
		}

		if speedCount > 0 {
			avgSpeed /= float64(speedCount)
		}

		// Only check for rapid lane changes at highway speeds
		if avgSpeed > 50 && len(bearings) >= 3 {
			variance := s.calculateBearingVariance(bearings)
			if variance > s.rapidLaneChangeThreshold {
				severity := s.calculateLaneChangeSeverity(variance)
				curr := points[i]
				if curr.GPSTimestamp != nil {
					events = append(events, s.createBehaviorEvent(
						deviceId, &tripId, driverId, models.EventTypeRapidLaneChange,
						*curr.GPSTimestamp, curr.Latitude, curr.Longitude,
						&avgSpeed, nil, &variance, nil, &severity,
					))
				}
			}
		}
	}

	return events
}

// Severity calculation functions
func (s *DrivingBehaviorService) calculateOverspeedSeverity(speed, limit float64) float64 {
	excess := speed - limit
	if excess <= 10 {
		return models.SeverityLow
	} else if excess <= 20 {
		return models.SeverityMedium
	} else if excess <= 40 {
		return models.SeverityHigh
	}
	return models.SeverityCritical
}

func (s *DrivingBehaviorService) calculateAccelerationSeverity(accel, threshold float64) float64 {
	ratio := accel / threshold
	if ratio <= 1.5 {
		return models.SeverityLow
	} else if ratio <= 2.0 {
		return models.SeverityMedium
	} else if ratio <= 3.0 {
		return models.SeverityHigh
	}
	return models.SeverityCritical
}

func (s *DrivingBehaviorService) calculateBrakingSeverity(accel, threshold float64) float64 {
	ratio := math.Abs(accel / threshold)
	if ratio <= 1.2 {
		return models.SeverityLow
	} else if ratio <= 1.5 {
		return models.SeverityMedium
	} else if ratio <= 2.0 {
		return models.SeverityHigh
	}
	return models.SeverityCritical
}

func (s *DrivingBehaviorService) calculateCorneringSeverity(bearingRate float64) float64 {
	if bearingRate <= 20 {
		return models.SeverityLow
	} else if bearingRate <= 30 {
		return models.SeverityMedium
	} else if bearingRate <= 45 {
		return models.SeverityHigh
	}
	return models.SeverityCritical
}

func (s *DrivingBehaviorService) calculateIdlingSeverity(minutes int) float64 {
	if minutes <= 15 {
		return models.SeverityLow
	} else if minutes <= 30 {
		return models.SeverityMedium
	} else if minutes <= 60 {
		return models.SeverityHigh
	}
	return models.SeverityCritical
}

func (s *DrivingBehaviorService) calculateFatigueSeverity(minutes int) float64 {
	hours := float64(minutes) / 60.0
	if hours <= 5 {
		return models.SeverityLow
	} else if hours <= 7 {
		return models.SeverityMedium
	} else if hours <= 10 {
		return models.SeverityHigh
	}
	return models.SeverityCritical
}

func (s *DrivingBehaviorService) calculateLaneChangeSeverity(variance float64) float64 {
	if variance <= 40 {
		return models.SeverityLow
	} else if variance <= 60 {
		return models.SeverityMedium
	} else if variance <= 80 {
		return models.SeverityHigh
	}
	return models.SeverityCritical
}

func (s *DrivingBehaviorService) calculateBearingVariance(bearings []float64) float64 {
	if len(bearings) < 2 {
		return 0
	}

	var changes []float64
	for i := 1; i < len(bearings); i++ {
		change := utils.NormalizeBearingDifference(bearings[i-1], bearings[i])
		changes = append(changes, math.Abs(change))
	}

	// Calculate variance
	var sum, mean float64
	for _, change := range changes {
		sum += change
	}
	mean = sum / float64(len(changes))

	var variance float64
	for _, change := range changes {
		variance += math.Pow(change-mean, 2)
	}
	variance /= float64(len(changes))

	return math.Sqrt(variance) // Return standard deviation
}

func (s *DrivingBehaviorService) createBehaviorEvent(
	deviceId uint, tripId *uint, driverId *uint, eventType string,
	timestamp time.Time, lat, lng float64,
	speed, acceleration, bearingChange *float64, duration *int, severity *float64,
) models.DrivingBehaviorEvent {
	return models.DrivingBehaviorEvent{
		ClientDeviceId: deviceId,
		TripId:         tripId,
		DriverId:       driverId,
		EventType:      eventType,
		Timestamp:      timestamp,
		Latitude:       lat,
		Longitude:      lng,
		Speed:          speed,
		Acceleration:   acceleration,
		BearingChange:  bearingChange,
		Duration:       duration,
		Severity:       severity,
		ProcessedAt:    time.Now(),
	}
}

func (s *DrivingBehaviorService) saveDrivingBehaviorEvent(event models.DrivingBehaviorEvent) error {
	return config.DB.Create(&event).Error
}
