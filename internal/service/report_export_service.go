package service

import (
	"bytes"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"html/template"
	"strconv"
	"strings"
	"time"
	"yotracker/internal/models"

	"github.com/SebastiaanKlippert/go-wkhtmltopdf"
	"github.com/xuri/excelize/v2"
)

type ReportExportService struct{}

func NewReportExportService() *ReportExportService {
	return &ReportExportService{}
}

// ExportReport exports report data in the specified format
func (s *ReportExportService) ExportReport(reportData *models.ReportData, format string) ([]byte, string, error) {
	switch strings.ToLower(format) {
	case "pdf":
		data, err := s.ExportToPDF(reportData)
		return data, "application/pdf", err
	case "excel", "xlsx":
		data, err := s.ExportToExcel(reportData)
		return data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", err
	case "csv":
		data, err := s.ExportToCSV(reportData)
		return data, "text/csv", err
	default:
		return nil, "", fmt.Errorf("unsupported export format: %s", format)
	}
}

// ExportToPDF generates a PDF report
func (s *ReportExportService) ExportToPDF(reportData *models.ReportData) ([]byte, error) {
	// Create HTML template for the report
	htmlTemplate := s.createReportHTMLTemplate()

	tmpl, err := template.New("report").Parse(htmlTemplate)
	if err != nil {
		return nil, fmt.Errorf("template parse error: %v", err)
	}

	var htmlBuffer bytes.Buffer
	err = tmpl.Execute(&htmlBuffer, reportData)
	if err != nil {
		return nil, fmt.Errorf("template execution error: %v", err)
	}

	// Generate PDF from HTML using wkhtmltopdf
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return nil, fmt.Errorf("PDF generator error: %v", err)
	}

	// Configure PDF options
	pdfg.Dpi.Set(300)
	pdfg.Orientation.Set(wkhtmltopdf.OrientationLandscape)
	pdfg.Grayscale.Set(false)
	pdfg.PageSize.Set(wkhtmltopdf.PageSizeA4)

	page := wkhtmltopdf.NewPageReader(&htmlBuffer)
	pdfg.AddPage(page)

	err = pdfg.Create()
	if err != nil {
		return nil, fmt.Errorf("PDF creation failed: %v", err)
	}

	return pdfg.Bytes(), nil
}

// ExportToExcel generates an Excel report
func (s *ReportExportService) ExportToExcel(reportData *models.ReportData) ([]byte, error) {
	f := excelize.NewFile()
	defer f.Close()

	// Create main sheet
	sheetName := "Report Data"
	f.SetSheetName("Sheet1", sheetName)

	// Add report header
	f.SetCellValue(sheetName, "A1", reportData.ReportInfo.Name)
	f.SetCellValue(sheetName, "A2", reportData.ReportInfo.Description)
	f.SetCellValue(sheetName, "A3", fmt.Sprintf("Generated: %s", reportData.Metadata.GeneratedAt.Format("2006-01-02 15:04:05")))
	f.SetCellValue(sheetName, "A4", fmt.Sprintf("Total Records: %d", reportData.Metadata.TotalRecords))
	f.SetCellValue(sheetName, "A5", fmt.Sprintf("Filtered Records: %d", reportData.Metadata.FilteredRecords))

	// Style the header
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true, Size: 14},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"#E6F3FF"}, Pattern: 1},
	})
	f.SetCellStyle(sheetName, "A1", "A5", headerStyle)

	// Add data starting from row 7
	startRow := 7
	err := s.addDataToExcel(f, sheetName, reportData.Data, startRow)
	if err != nil {
		return nil, fmt.Errorf("failed to add data to Excel: %v", err)
	}

	// Add summary sheet if available
	if reportData.Summary != nil {
		s.addSummarySheet(f, reportData.Summary)
	}

	// Save to buffer
	buf, err := f.WriteToBuffer()
	if err != nil {
		return nil, fmt.Errorf("failed to write Excel file: %v", err)
	}

	return buf.Bytes(), nil
}

// ExportToCSV generates a CSV report
func (s *ReportExportService) ExportToCSV(reportData *models.ReportData) ([]byte, error) {
	var buf bytes.Buffer
	writer := csv.NewWriter(&buf)

	// Write header information
	writer.Write([]string{"Report", reportData.ReportInfo.Name})
	writer.Write([]string{"Description", reportData.ReportInfo.Description})
	writer.Write([]string{"Generated", reportData.Metadata.GeneratedAt.Format("2006-01-02 15:04:05")})
	writer.Write([]string{"Total Records", strconv.Itoa(reportData.Metadata.TotalRecords)})
	writer.Write([]string{"Filtered Records", strconv.Itoa(reportData.Metadata.FilteredRecords)})
	writer.Write([]string{}) // Empty row

	// Write data
	err := s.addDataToCSV(writer, reportData.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to write CSV data: %v", err)
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, fmt.Errorf("CSV writer error: %v", err)
	}

	return buf.Bytes(), nil
}

// addDataToExcel adds report data to Excel sheet
func (s *ReportExportService) addDataToExcel(f *excelize.File, sheetName string, data interface{}, startRow int) error {
	// Convert data to slice of maps for easier processing
	records, headers, err := s.convertDataToRecords(data)
	if err != nil {
		return err
	}

	if len(records) == 0 {
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", startRow), "No data available")
		return nil
	}

	// Write headers
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"#D9E1F2"}, Pattern: 1},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
	})

	for i, header := range headers {
		cell := fmt.Sprintf("%s%d", s.getExcelColumn(i), startRow)
		f.SetCellValue(sheetName, cell, header)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
	}

	// Write data rows
	for i, record := range records {
		row := startRow + 1 + i
		for j, header := range headers {
			cell := fmt.Sprintf("%s%d", s.getExcelColumn(j), row)
			value := record[header]
			f.SetCellValue(sheetName, cell, value)
		}
	}

	// Auto-fit columns
	for i := range headers {
		col := s.getExcelColumn(i)
		f.SetColWidth(sheetName, col, col, 15)
	}

	return nil
}

// addDataToCSV adds report data to CSV writer
func (s *ReportExportService) addDataToCSV(writer *csv.Writer, data interface{}) error {
	records, headers, err := s.convertDataToRecords(data)
	if err != nil {
		return err
	}

	if len(records) == 0 {
		writer.Write([]string{"No data available"})
		return nil
	}

	// Write headers
	writer.Write(headers)

	// Write data rows
	for _, record := range records {
		row := make([]string, len(headers))
		for i, header := range headers {
			if value, exists := record[header]; exists {
				row[i] = fmt.Sprintf("%v", value)
			}
		}
		writer.Write(row)
	}

	return nil
}

// convertDataToRecords converts various data types to slice of maps
func (s *ReportExportService) convertDataToRecords(data interface{}) ([]map[string]interface{}, []string, error) {
	if data == nil {
		return nil, nil, nil
	}

	// Handle different data types
	switch v := data.(type) {
	case []interface{}:
		if len(v) == 0 {
			return nil, nil, nil
		}

		// Convert to JSON and back to get consistent map structure
		jsonData, err := json.Marshal(v)
		if err != nil {
			return nil, nil, err
		}

		var records []map[string]interface{}
		err = json.Unmarshal(jsonData, &records)
		if err != nil {
			return nil, nil, err
		}

		// Extract headers from first record
		if len(records) > 0 {
			headers := make([]string, 0, len(records[0]))
			for key := range records[0] {
				headers = append(headers, key)
			}
			return records, headers, nil
		}

	case []models.Trip:
		return s.convertTripsToRecords(v)

	case []models.DrivingBehaviorEvent:
		return s.convertBehaviorEventsToRecords(v)

	case []models.GPSData:
		return s.convertGPSDataToRecords(v)

	default:
		// For any other type, try to convert via JSON marshaling
		jsonData, err := json.Marshal(data)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to marshal data: %v", err)
		}

		var records []map[string]interface{}
		err = json.Unmarshal(jsonData, &records)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to unmarshal data: %v", err)
		}

		if len(records) == 0 {
			return nil, nil, nil
		}

		// Extract headers from first record
		headers := make([]string, 0, len(records[0]))
		for key := range records[0] {
			headers = append(headers, key)
		}
		return records, headers, nil
	}

	return nil, nil, fmt.Errorf("unsupported data type: %T", data)
}

// Helper functions for specific model conversions
func (s *ReportExportService) convertTripsToRecords(trips []models.Trip) ([]map[string]interface{}, []string, error) {
	headers := []string{"ID", "Client Device ID", "Driver ID", "Start Time", "End Time", "Distance", "Duration", "Avg Speed", "Max Speed", "Idle Time"}
	records := make([]map[string]interface{}, len(trips))

	for i, trip := range trips {
		records[i] = map[string]interface{}{
			"ID":               trip.Id,
			"Client Device ID": trip.ClientDeviceId,
			"Driver ID":        trip.DriverId,
			"Start Time":       trip.StartTime.Format("2006-01-02 15:04:05"),
			"End Time":         s.formatTimePtr(trip.EndTime),
			"Distance":         fmt.Sprintf("%.2f km", trip.Distance),
			"Duration":         s.formatDurationPtr(trip.Duration),
			"Avg Speed":        s.formatFloatPtr(trip.AvgSpeed, "%.1f km/h"),
			"Max Speed":        s.formatFloatPtr(trip.MaxSpeed, "%.1f km/h"),
			"Idle Time":        s.formatDurationPtr(trip.IdleTime),
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertBehaviorEventsToRecords(events []models.DrivingBehaviorEvent) ([]map[string]interface{}, []string, error) {
	headers := []string{"ID", "Client Device ID", "Driver ID", "Event Type", "Timestamp", "Latitude", "Longitude", "Speed", "Severity"}
	records := make([]map[string]interface{}, len(events))

	for i, event := range events {
		records[i] = map[string]interface{}{
			"ID":               event.Id,
			"Client Device ID": event.ClientDeviceId,
			"Driver ID":        event.DriverId,
			"Event Type":       event.EventType,
			"Timestamp":        event.Timestamp.Format("2006-01-02 15:04:05"),
			"Latitude":         fmt.Sprintf("%.6f", event.Latitude),
			"Longitude":        fmt.Sprintf("%.6f", event.Longitude),
			"Speed":            s.formatFloatPtr(event.Speed, "%.1f km/h"),
			"Severity":         s.formatFloatPtr(event.Severity, "%.1f"),
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertGPSDataToRecords(gpsData []models.GPSData) ([]map[string]interface{}, []string, error) {
	headers := []string{"ID", "Client Device ID", "Timestamp", "Latitude", "Longitude", "Speed", "Location"}
	records := make([]map[string]interface{}, len(gpsData))

	for i, gps := range gpsData {
		records[i] = map[string]interface{}{
			"ID":               gps.Id,
			"Client Device ID": s.formatUintPtr(gps.ClientDeviceId),
			"Timestamp":        s.formatTimePtr(gps.GPSTimestamp),
			"Latitude":         fmt.Sprintf("%.6f", gps.Latitude),
			"Longitude":        fmt.Sprintf("%.6f", gps.Longitude),
			"Speed":            s.formatFloatPtr(gps.Speed, "%.1f km/h"),
			"Location":         s.formatStringPtr(gps.LocationName),
		}
	}

	return records, headers, nil
}

// Helper functions
func (s *ReportExportService) getExcelColumn(index int) string {
	column := ""
	for index >= 0 {
		column = string(rune('A'+index%26)) + column
		index = index/26 - 1
	}
	return column
}

func (s *ReportExportService) formatTimePtr(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

func (s *ReportExportService) formatDurationPtr(d *int) string {
	if d == nil {
		return ""
	}
	duration := time.Duration(*d) * time.Second
	return duration.String()
}

func (s *ReportExportService) formatFloatPtr(f *float64, format string) string {
	if f == nil {
		return ""
	}
	return fmt.Sprintf(format, *f)
}

func (s *ReportExportService) formatUintPtr(u *uint) string {
	if u == nil {
		return ""
	}
	return fmt.Sprintf("%d", *u)
}

func (s *ReportExportService) formatStringPtr(str *string) string {
	if str == nil {
		return ""
	}
	return *str
}

func (s *ReportExportService) addSummarySheet(f *excelize.File, summary interface{}) {
	sheetName := "Summary"
	f.NewSheet(sheetName)

	// Convert summary to key-value pairs
	summaryMap, ok := summary.(map[string]interface{})
	if !ok {
		return
	}

	row := 1
	for key, value := range summaryMap {
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), key)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), value)
		row++
	}
}

// createReportHTMLTemplate creates an HTML template for PDF generation
func (s *ReportExportService) createReportHTMLTemplate() string {
	return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{.ReportInfo.Name}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            color: #333;
            font-size: 12px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #007bff;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            color: #007bff;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .metadata {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .metadata table {
            width: 100%;
            border-collapse: collapse;
        }
        .metadata td {
            padding: 5px 10px;
            border: none;
        }
        .metadata .label {
            font-weight: bold;
            width: 150px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .summary {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .summary h3 {
            margin-top: 0;
            color: #155724;
        }
        .no-data {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 40px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{.ReportInfo.Name}}</h1>
        <p>{{.ReportInfo.Description}}</p>
    </div>

    <div class="metadata">
        <table>
            <tr>
                <td class="label">Generated:</td>
                <td>{{.Metadata.GeneratedAt.Format "2006-01-02 15:04:05"}}</td>
                <td class="label">Total Records:</td>
                <td>{{.Metadata.TotalRecords}}</td>
            </tr>
            <tr>
                <td class="label">Filtered Records:</td>
                <td>{{.Metadata.FilteredRecords}}</td>
                <td class="label">Execution Time:</td>
                <td>{{.Metadata.ExecutionTime}}</td>
            </tr>
            <tr>
                <td class="label">Format:</td>
                <td>{{.Metadata.Format}}</td>
                <td class="label">Category:</td>
                <td>{{.ReportInfo.Category}}</td>
            </tr>
        </table>
    </div>

    {{if .Summary}}
    <div class="summary">
        <h3>Summary</h3>
        {{range $key, $value := .Summary}}
        <p><strong>{{$key}}:</strong> {{$value}}</p>
        {{end}}
    </div>
    {{end}}

    <div class="data-section">
        <h3>Report Data</h3>
        {{if .Data}}
            <!-- Data will be rendered based on type -->
            <p>Data records: {{.Metadata.FilteredRecords}}</p>
        {{else}}
            <div class="no-data">No data available for the selected criteria</div>
        {{end}}
    </div>

    <div class="footer">
        <p>Generated by YoTracker Fleet Management System | {{.Metadata.GeneratedAt.Format "2006-01-02 15:04:05"}}</p>
    </div>
</body>
</html>`
}
