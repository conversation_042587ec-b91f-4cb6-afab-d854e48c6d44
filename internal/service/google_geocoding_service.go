package service

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

type GoogleGeocodingResponse struct {
	Results []struct {
		FormattedAddress  string `json:"formatted_address"`
		AddressComponents []struct {
			LongName  string   `json:"long_name"`
			ShortName string   `json:"short_name"`
			Types     []string `json:"types"`
		} `json:"address_components"`
	} `json:"results"`
	Status string `json:"status"`
}

type GoogleGeocodingService struct {
	client *http.Client
}

func NewGoogleGeocodingService() *GoogleGeocodingService {
	return &GoogleGeocodingService{
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// GetLocationName retrieves the location name for given coordinates using Google Geocoding API
func (s *GoogleGeocodingService) GetLocationName(latitude, longitude float64) (string, error) {
	// Get Google Maps API key from settings
	apiKey := models.GetSetting("google_maps_api_key")
	if apiKey == "" {
		return "", fmt.Errorf("google_maps_api_key setting is not configured")
	}

	// Build the Google Geocoding API URL
	baseURL := "https://maps.googleapis.com/maps/api/geocode/json"
	params := url.Values{}
	params.Add("latlng", fmt.Sprintf("%.6f,%.6f", latitude, longitude))
	params.Add("key", apiKey)
	params.Add("language", "en")

	reqURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// Create HTTP request
	req, err := http.NewRequest("GET", reqURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Make the request
	resp, err := s.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("google geocoding API returned status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var geocodingResp GoogleGeocodingResponse
	if err := json.NewDecoder(resp.Body).Decode(&geocodingResp); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	// Check API status
	if geocodingResp.Status != "OK" {
		return "", fmt.Errorf("google geocoding API returned status: %s", geocodingResp.Status)
	}

	if len(geocodingResp.Results) == 0 {
		return "", fmt.Errorf("no results found for coordinates")
	}

	// Build a meaningful location name from address components
	locationName := s.buildLocationName(geocodingResp.Results[0])
	if locationName == "" {
		// Fallback to formatted address if custom building fails
		locationName = geocodingResp.Results[0].FormattedAddress
	}

	return locationName, nil
}

// buildLocationName builds a meaningful location name from Google's address components
func (s *GoogleGeocodingService) buildLocationName(result struct {
	FormattedAddress  string `json:"formatted_address"`
	AddressComponents []struct {
		LongName  string   `json:"long_name"`
		ShortName string   `json:"short_name"`
		Types     []string `json:"types"`
	} `json:"address_components"`
}) string {
	var locationParts []string

	// Extract address components in priority order
	road := s.getAddressComponent(result.AddressComponents, []string{"route"})
	streetNumber := s.getAddressComponent(result.AddressComponents, []string{"street_number"})
	suburb := s.getAddressComponent(result.AddressComponents, []string{"sublocality", "sublocality_level_1"})
	city := s.getAddressComponent(result.AddressComponents, []string{"locality", "administrative_area_level_2"})
	state := s.getAddressComponent(result.AddressComponents, []string{"administrative_area_level_1"})
	country := s.getAddressComponent(result.AddressComponents, []string{"country"})

	// Build location name in priority order
	if streetNumber != "" && road != "" {
		locationParts = append(locationParts, fmt.Sprintf("%s %s", streetNumber, road))
	} else if road != "" {
		locationParts = append(locationParts, road)
	}

	if suburb != "" {
		locationParts = append(locationParts, suburb)
	} else if city != "" {
		locationParts = append(locationParts, city)
	}

	if state != "" {
		locationParts = append(locationParts, state)
	}

	if country != "" {
		locationParts = append(locationParts, country)
	}

	if len(locationParts) > 0 {
		return strings.Join(locationParts, ", ")
	}

	return ""
}

// getAddressComponent extracts a specific address component by type
func (s *GoogleGeocodingService) getAddressComponent(components []struct {
	LongName  string   `json:"long_name"`
	ShortName string   `json:"short_name"`
	Types     []string `json:"types"`
}, types []string) string {
	for _, component := range components {
		for _, componentType := range component.Types {
			for _, targetType := range types {
				if componentType == targetType {
					return component.LongName
				}
			}
		}
	}
	return ""
}

// UpdateGPSDataLocationName updates the location name for a specific GPS data record
func (s *GoogleGeocodingService) UpdateGPSDataLocationName(gpsData *models.GPSData) error {
	// Check if location name is already set
	if gpsData.LocationName != nil && *gpsData.LocationName != "" {
		return nil // Already has location name
	}

	// Get location name from Google Geocoding API
	locationName, err := s.GetLocationName(gpsData.Latitude, gpsData.Longitude)
	if err != nil {
		return fmt.Errorf("failed to get location name: %w", err)
	}

	// Update the GPS data record
	if err := config.DB.Model(&models.GPSData{}).
		Where("id = ?", gpsData.Id).
		Update("location_name", locationName).Error; err != nil {
		return fmt.Errorf("failed to update GPS data location name: %w", err)
	}

	// Update the in-memory object
	gpsData.LocationName = &locationName

	return nil
}
