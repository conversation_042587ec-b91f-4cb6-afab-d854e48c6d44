package service

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"
	"yotracker/internal/models"
)

// SlackService handles Slack notifications
type SlackService struct {
	botToken   string
	webhookURL string
	httpClient *http.Client
}

// SlackMessage represents a Slack message with blocks
type SlackMessage struct {
	Channel     string            `json:"channel,omitempty"`
	Text        string            `json:"text"`
	Blocks      []SlackBlock      `json:"blocks,omitempty"`
	Attachments []SlackAttachment `json:"attachments,omitempty"`
}

// SlackBlock represents a Slack block element
type SlackBlock struct {
	Type      string         `json:"type"`
	Text      *SlackText     `json:"text,omitempty"`
	Elements  []SlackElement `json:"elements,omitempty"`
	Fields    []SlackText    `json:"fields,omitempty"`
	Accessory *SlackElement  `json:"accessory,omitempty"`
}

// SlackText represents text in Slack blocks
type SlackText struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

// SlackElement represents an element in Slack blocks
type SlackElement struct {
	Type     string     `json:"type"`
	Text     *SlackText `json:"text,omitempty"`
	URL      string     `json:"url,omitempty"`
	ActionID string     `json:"action_id,omitempty"`
	Style    string     `json:"style,omitempty"`
}

// SlackAttachment represents a Slack attachment (for color coding)
type SlackAttachment struct {
	Color  string       `json:"color,omitempty"`
	Blocks []SlackBlock `json:"blocks,omitempty"`
}

// AlertSeverity represents alert severity levels
type AlertSeverity string

const (
	SeverityInfo     AlertSeverity = "info"
	SeverityWarning  AlertSeverity = "warning"
	SeverityCritical AlertSeverity = "critical"
)

// NewSlackService creates a new Slack service instance
func NewSlackService() (*SlackService, error) {
	// Try to get bot token from environment first, then from settings
	botToken := os.Getenv("SLACK_BOT_TOKEN")
	if botToken == "" && os.Getenv("TESTING_DB_NAME") == "" && os.Getenv("GO_ENV") != "test" {
		botToken = models.GetSetting("slack_bot_token")
	}

	// Try to get webhook URL from environment first, then from settings
	webhookURL := os.Getenv("SLACK_WEBHOOK_URL")
	if webhookURL == "" && os.Getenv("TESTING_DB_NAME") == "" && os.Getenv("GO_ENV") != "test" {
		webhookURL = models.GetSetting("slack_webhook_url")
	}

	// Validate bot token format if provided
	if botToken != "" && !strings.HasPrefix(botToken, "xoxb-") && os.Getenv("GO_ENV") != "test" {
		return nil, errors.New("invalid bot token format: must start with 'xoxb-'")
	}

	// At least one method must be configured (except in test mode)
	if botToken == "" && webhookURL == "" && os.Getenv("GO_ENV") != "test" {
		return nil, errors.New("either SLACK_BOT_TOKEN or SLACK_WEBHOOK_URL must be configured")
	}

	return &SlackService{
		botToken:   botToken,
		webhookURL: webhookURL,
		httpClient: &http.Client{Timeout: 30 * time.Second},
	}, nil
}

// IsEnabled checks if Slack notifications are enabled
func (s *SlackService) IsEnabled() bool {
	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		return false
	}

	enabled := models.GetSetting("slack_alerts_enabled")
	return enabled == "true" || enabled == "1"
}

// GetDefaultChannel returns the default Slack channel
func (s *SlackService) GetDefaultChannel() string {
	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		return "#yotracker-alerts"
	}

	channel := models.GetSetting("slack_default_channel")
	if channel == "" {
		channel = "#yotracker-alerts"
	}
	return channel
}

// ShouldSendAlert checks if an alert should be sent based on severity filter
func (s *SlackService) ShouldSendAlert(alertLevel string) bool {
	if !s.IsEnabled() {
		return false
	}
	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		return true
	}

	severityFilter := models.GetSetting("slack_alert_severity_filter")
	if severityFilter == "" {
		return true // Send all alerts if no filter is set
	}

	allowedSeverities := strings.Split(severityFilter, ",")
	for _, severity := range allowedSeverities {
		if strings.TrimSpace(severity) == strings.ToLower(alertLevel) {
			return true
		}
	}

	return false
}

// SendAlert sends an alert to Slack with rich formatting
func (s *SlackService) SendAlert(alert *models.Alert, clientDevice *models.ClientDevice) error {
	if !s.IsEnabled() {
		return nil // Silently skip if disabled
	}
	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		return nil
	}

	// Determine alert severity and color
	severity := s.determineSeverity(alert.AlertType)
	color := s.getSeverityColor(severity)

	// Check if we should send this alert based on severity filter
	if !s.ShouldSendAlert(string(severity)) {
		return nil
	}

	// Build the Slack message
	message := s.buildAlertMessage(alert, clientDevice, color)

	// Send via bot token first, then webhook
	if s.botToken != "" {
		return s.sendViaAPI(message)
	} else if s.webhookURL != "" {
		return s.sendViaWebhook(message)
	}

	return errors.New("no Slack configuration available")
}

// SendCustomMessage sends a custom message to Slack
func (s *SlackService) SendCustomMessage(channel, text string, blocks []SlackBlock) error {
	if !s.IsEnabled() {
		return nil
	}
	message := &SlackMessage{
		Channel: channel,
		Text:    text,
		Blocks:  blocks,
	}

	if s.botToken != "" {
		return s.sendViaAPI(message)
	} else if s.webhookURL != "" {
		return s.sendViaWebhook(message)
	}

	return errors.New("no Slack configuration available")
}

// SendToWebhook sends a message to a specific webhook URL
func (s *SlackService) SendToWebhook(webhookURL string, message *SlackMessage) error {
	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		return nil
	}

	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	resp, err := s.httpClient.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to send webhook: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("webhook returned status %d", resp.StatusCode)
	}

	return nil
}

// SendRawDataLog sends a formatted message for device raw data logging
func (s *SlackService) SendRawDataLog(deviceName, deviceId, rawHex string) error {
	if !s.IsEnabled() {
		return nil // Silently skip if disabled
	}

	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		return nil
	}

	// Check if raw data logging is enabled (separate from alerts)
	if !s.ShouldSendRawDataLog() {
		return nil
	}

	// Build the raw data message
	message := s.buildRawDataMessage(deviceName, deviceId, rawHex)

	// Send via bot token first, then webhook
	if s.botToken != "" {
		return s.sendViaAPI(message)
	} else if s.webhookURL != "" {
		return s.sendViaWebhook(message)
	}

	return errors.New("no Slack configuration available")
}

// ShouldSendRawDataLog checks if raw data logging is enabled
func (s *SlackService) ShouldSendRawDataLog() bool {
	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		return true
	}

	enabled := models.GetSetting("slack_raw_data_enabled")
	return enabled == "true" || enabled == "1"
}

// GetRawDataChannel returns the channel for raw data logs
func (s *SlackService) GetRawDataChannel() string {
	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		return "#raw-data"
	}

	channel := models.GetSetting("slack_raw_data_channel")
	if channel == "" {
		channel = "#yotracker-device-logs" // Default channel for raw data
	}
	return channel
}

// buildRawDataMessage builds a formatted Slack message for raw device data
func (s *SlackService) buildRawDataMessage(deviceName, deviceId, rawHex string) *SlackMessage {
	// Format the hex data for better readability
	//formattedHex := s.formatHexData(rawHex)
	formattedHex := rawHex

	// Get current timestamp
	timestamp := time.Now().Format("2006-01-02 15:04:05 MST")

	// Build blocks for the raw data message - using simpler structure to avoid validation errors
	blocks := []SlackBlock{
		{
			Type: "section",
			Text: &SlackText{
				Type: "mrkdwn",
				Text: fmt.Sprintf("📡 *Device Raw Data*\n\n*Device:* %s\n*ID:* %s", deviceName, deviceId),
			},
		},
		{
			Type: "section",
			Text: &SlackText{
				Type: "mrkdwn",
				Text: fmt.Sprintf("*Raw Data (%d bytes):*\n```\n%s\n```", len(rawHex)/2, formattedHex),
			},
		},
		{
			Type: "section",
			Text: &SlackText{
				Type: "mrkdwn",
				Text: fmt.Sprintf("*Timestamp:* %s\n_Raw device communication data for debugging and analysis_", timestamp),
			},
		},
	}

	return &SlackMessage{
		Channel: s.GetRawDataChannel(),
		Text:    fmt.Sprintf("Raw data from device %s (%s)", deviceName, deviceId),
		Blocks:  blocks,
	}
}

// formatHexData formats hex string for better readability
func (s *SlackService) formatHexData(rawHex string) string {
	// Remove any existing spaces or formatting
	cleanHex := strings.ReplaceAll(strings.ToUpper(rawHex), " ", "")

	// Add spaces every 2 characters (bytes) and line breaks every 16 bytes
	var formatted strings.Builder
	for i := 0; i < len(cleanHex); i += 2 {
		// Add line break every 16 bytes (32 hex characters)
		if i > 0 && i%32 == 0 {
			formatted.WriteString("\n")
		}
		// Add space before each byte (except first on line)
		if i > 0 && i%32 != 0 {
			formatted.WriteString(" ")
		}
		// Add the hex byte (2 characters)
		if i+1 < len(cleanHex) {
			formatted.WriteString(cleanHex[i : i+2])
		} else {
			formatted.WriteString(cleanHex[i:]) // Handle odd length
		}
	}

	return formatted.String()
}

// determineSeverity determines alert severity based on alert type
func (s *SlackService) determineSeverity(alertType string) AlertSeverity {
	alertType = strings.ToLower(alertType)

	switch {
	case strings.Contains(alertType, "critical") || strings.Contains(alertType, "emergency"):
		return SeverityCritical
	case strings.Contains(alertType, "warning") || strings.Contains(alertType, "alert"):
		return SeverityWarning
	default:
		return SeverityInfo
	}
}

// getSeverityColor returns the color for alert severity
func (s *SlackService) getSeverityColor(severity AlertSeverity) string {
	switch severity {
	case SeverityCritical:
		return "#FF0000" // Red
	case SeverityWarning:
		return "#FFA500" // Orange
	default:
		return "#36A64F" // Green
	}
}

// buildAlertMessage builds a rich Slack message for alerts
func (s *SlackService) buildAlertMessage(alert *models.Alert, clientDevice *models.ClientDevice, color string) *SlackMessage {
	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "https://yourdomain.com"
	}

	// Build the main text with severity indicator
	severity := s.determineSeverity(alert.AlertType)
	var emoji string
	switch severity {
	case SeverityCritical:
		emoji = "🔴" // Red circle for critical
	case SeverityWarning:
		emoji = "🟠" // Orange circle for warning
	default:
		emoji = "🟢" // Green circle for info
	}

	mainText := fmt.Sprintf("%s Device Alert: %s", emoji, alert.AlertType)
	if alert.AlertName != nil && *alert.AlertName != "" {
		mainText = fmt.Sprintf("%s %s", emoji, *alert.AlertName)
	}
	fmt.Println("Main Alert text: ", mainText)

	// Build blocks for rich formatting
	blocks := []SlackBlock{
		{
			Type: "header",
			Text: &SlackText{
				Type: "plain_text",
				Text: mainText,
			},
		},
		{
			Type: "section",
			Fields: []SlackText{
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Device ID:*\n%s", *alert.DeviceId),
				},
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Alert Type:*\n%s", alert.AlertType),
				},
			},
		},
	}

	// Add device name if available
	if clientDevice != nil && clientDevice.Name != nil {
		plateNumber := ""
		if clientDevice.PlateNumber != nil {
			plateNumber = *clientDevice.PlateNumber
		}
		blocks = append(blocks, SlackBlock{
			Type: "section",
			Fields: []SlackText{
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Device Name:*\n%s", *clientDevice.Name),
				},
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Plate Number:*\n%s", plateNumber),
				},
			},
		})
	}

	// Add message if available
	if alert.Message != nil && *alert.Message != "" {
		blocks = append(blocks, SlackBlock{
			Type: "section",
			Text: &SlackText{
				Type: "mrkdwn",
				Text: fmt.Sprintf("*Message:*\n%s", *alert.Message),
			},
		})
	}

	// Add location info if available
	if alert.Speed != nil && *alert.Speed > 0 {
		fields := []SlackText{}
		if *alert.Speed > 0 {
			fields = append(fields, SlackText{
				Type: "mrkdwn",
				Text: fmt.Sprintf("*Speed:*\n%.2f km/h", *alert.Speed),
			})
		}
		if alert.Direction != nil && *alert.Direction != "" {
			fields = append(fields, SlackText{
				Type: "mrkdwn",
				Text: fmt.Sprintf("*Direction:*\n%s", *alert.Direction),
			})
		}
		if len(fields) > 0 {
			blocks = append(blocks, SlackBlock{
				Type:   "section",
				Fields: fields,
			})
		}
	}

	// Add timestamp - simplified to avoid attachment validation issues
	blocks = append(blocks, SlackBlock{
		Type: "section",
		Text: &SlackText{
			Type: "mrkdwn",
			Text: fmt.Sprintf("⏰ %s", alert.AlertTimestamp.Format("2006-01-02 15:04:05 MST")),
		},
	})

	// Add action button
	blocks = append(blocks, SlackBlock{
		Type: "actions",
		Elements: []SlackElement{
			{
				Type: "button",
				Text: &SlackText{
					Type: "plain_text",
					Text: "View Dashboard",
				},
				URL:      appURL + "/dashboard",
				ActionID: "view_dashboard",
				Style:    "primary",
			},
		},
	})
	fmt.Println("Blocks: ", blocks)
	return &SlackMessage{
		Channel: s.GetDefaultChannel(),
		Text:    mainText, // Fallback text for notifications
		Blocks:  blocks,   // Use blocks directly instead of attachments to avoid validation issues
	}
}

// sendViaAPI sends message using Slack Web API
func (s *SlackService) sendViaAPI(message *SlackMessage) error {
	url := "https://slack.com/api/chat.postMessage"

	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Authorization", "Bearer "+s.botToken)
	req.Header.Set("User-Agent", "YoTracker-Slack-Service/1.0")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send API request: %w", err)
	}
	defer resp.Body.Close()
	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(bodyBytes))
	} else {
	}
	fmt.Printf("Slack API response: %s", string(bodyBytes))

	// Parse response to check for Slack API errors
	var slackResp struct {
		OK      bool                   `json:"ok"`
		Error   string                 `json:"error,omitempty"`
		Warning string                 `json:"warning,omitempty"`
		Channel string                 `json:"channel,omitempty"`
		TS      string                 `json:"ts,omitempty"`
		Message map[string]interface{} `json:"message,omitempty"`
	}

	if err := json.Unmarshal(bodyBytes, &slackResp); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	if !slackResp.OK {
		switch slackResp.Error {
		case "invalid_auth":
			return fmt.Errorf("Slack API error: %s - Check that your bot token is valid and starts with 'xoxb-'. Verify the token in your Slack app settings", slackResp.Error)
		case "channel_not_found":
			return fmt.Errorf("Slack API error: %s - Channel '%s' not found. Try using channel ID instead of name, or ensure the channel exists", slackResp.Error, message.Channel)
		case "not_in_channel":
			return fmt.Errorf("Slack API error: %s - Bot is not in channel '%s'. Add the bot to the channel first", slackResp.Error, message.Channel)
		case "missing_scope":
			return fmt.Errorf("Slack API error: %s - Bot token missing required 'chat:write' scope. Update bot permissions in Slack app settings", slackResp.Error)
		default:
			return fmt.Errorf("Slack API error: %s", slackResp.Error)
		}
	}

	if slackResp.Warning != "" {
		log.Printf("Slack API warning: %s", slackResp.Warning)
	}
	return nil
}

// sendViaWebhook sends message using webhook URL
func (s *SlackService) sendViaWebhook(message *SlackMessage) error {
	return s.SendToWebhook(s.webhookURL, message)
}

// TestBotToken tests if the bot token is valid by calling auth.test
func (s *SlackService) TestBotToken() error {
	if s.botToken == "" {
		return errors.New("no bot token configured")
	}

	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		return nil
	}

	url := "https://slack.com/api/auth.test"

	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create auth test request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("Authorization", "Bearer "+s.botToken)
	req.Header.Set("User-Agent", "YoTracker-Slack-Service/1.0")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send auth test request: %w", err)
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read auth test response: %w", err)
	}

	var authResp struct {
		OK    bool   `json:"ok"`
		Error string `json:"error,omitempty"`
		User  string `json:"user,omitempty"`
		Team  string `json:"team,omitempty"`
		URL   string `json:"url,omitempty"`
	}

	if err := json.Unmarshal(bodyBytes, &authResp); err != nil {
		return fmt.Errorf("failed to decode auth test response: %w", err)
	}

	if !authResp.OK {
		return fmt.Errorf("bot token validation failed: %s", authResp.Error)
	}

	return nil
}
