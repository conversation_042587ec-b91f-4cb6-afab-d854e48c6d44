package service

import (
	"encoding/json"
	"fmt"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
	// "gorm.io/gorm" // Commented out since AddDistanceToDailyStat is disabled
	// "gorm.io/gorm/clause" // Commented out since AddDistanceToDailyStat is disabled
)

type GPSData struct {
	Timestamp      time.Time
	DeviceId       string
	Latitude       float64
	Longitude      float64
	Altitude       float64
	Speed          float64
	Temperature    float64
	BatteryLevel   float64
	Direction      string
	VehicleStatus  string
	IgnitionStatus bool
	CellID         string
	Mnc            string
	Mcc            string
	Lac            string
	Cid            string
	RawData        string
	AdditionalData json.RawMessage
}

func (gpsData *GPSData) SaveGPSData() bool {
	// Validate GPS coordinates before processing
	if !isValidGPSData(gpsData) {
		fmt.Printf("Invalid GPS data received for device %s: lat=%.6f, lng=%.6f - skipping save\n",
			gpsData.DeviceId, gpsData.Latitude, gpsData.Longitude)
		return false
	}

	//check for client device id
	var clientDevice models.ClientDevice
	config.DB.Where("device_id", gpsData.DeviceId).First(&clientDevice)
	if clientDevice.Id != 0 {
		gps := &models.GPSData{
			ClientDeviceId: &clientDevice.Id,
			GPSTimestamp:   &gpsData.Timestamp,
			DeviceId:       gpsData.DeviceId,
			Latitude:       gpsData.Latitude,
			Longitude:      gpsData.Longitude,
			Altitude:       &gpsData.Altitude,
			Speed:          &gpsData.Speed,
			Temperature:    &gpsData.Temperature,
			BatteryLevel:   &gpsData.BatteryLevel,
			Direction:      &gpsData.Direction,
			VehicleStatus:  &gpsData.VehicleStatus,
			IgnitionStatus: &gpsData.IgnitionStatus,
			CellId:         &gpsData.CellID,
			Mcc:            &gpsData.Mcc,
			Mnc:            &gpsData.Mnc,
			Lac:            &gpsData.Lac,
			RawData:        &gpsData.RawData,
			AdditionalData: &gpsData.AdditionalData,
		}
		//get last gps data for this device
		var totalDistance float64
		var lastGPSData models.GPSData
		config.DB.Where("client_device_id = ?", clientDevice.Id).Last(&lastGPSData)
		if lastGPSData.Id != 0 {
			//check if we have a trip
			totalDistance = utils.HaversineDistance(lastGPSData.Latitude, lastGPSData.Longitude, gpsData.Latitude, gpsData.Longitude)

		}
		deviceStatus := utils.DetermineState(gpsData.Speed, totalDistance, gpsData.IgnitionStatus)
		clientDevice.DeviceStatus = &deviceStatus
		clientDevice.DistanceCovered = func() *float64 {
			if clientDevice.DistanceCovered == nil {
				return &totalDistance
			}
			currentTotalDistance := *clientDevice.DistanceCovered + totalDistance
			return &currentTotalDistance
		}()
		clientDevice.Mileage = func() *float64 {
			if clientDevice.Mileage == nil {
				return &totalDistance
			}
			currentTotalDistance := *clientDevice.Mileage + totalDistance
			return &currentTotalDistance
		}()
		timeNow := time.Now()
		clientDevice.LastStatusUpdate = &timeNow
		clientDevice.Latitude = &gpsData.Latitude
		clientDevice.Longitude = &gpsData.Longitude
		config.DB.Updates(clientDevice)
		result := config.DB.Create(gps)
		if result.Error != nil {
			return false
		}
		// NOTE: Daily stats are now updated in trip_detection_service.go
		// Commenting out to avoid duplicate updates and conflicts
		// go func() {
		// 	err := AddDistanceToDailyStat(clientDevice.Id, gpsData.Timestamp, totalDistance)
		// 	if err != nil {
		// 		fmt.Printf("failed to update daily stat for device %d: %v", clientDevice.Id, err)
		// 	}
		// }()

		// Check geofences for entry/exit events
		go func() {
			CheckGeofences(clientDevice.Id, gpsData.DeviceId, gpsData.Latitude, gpsData.Longitude, gpsData.Timestamp)
		}()

		//UpdateDeviceLastLocation(&clientDevice, gpsData.Latitude, gpsData.Longitude, gpsData.Speed)
	} else {
		fmt.Println("Could not find device")
	}
	return false
}

// isValidGPSData validates GPS coordinates for realistic values
func isValidGPSData(gpsData *GPSData) bool {
	// Check for null island (0,0) coordinates
	if gpsData.Latitude == 0 && gpsData.Longitude == 0 {
		return false
	}

	// Check for valid latitude range (-90 to 90)
	if gpsData.Latitude < -90 || gpsData.Latitude > 90 {
		return false
	}

	// Check for valid longitude range (-180 to 180)
	if gpsData.Longitude < -180 || gpsData.Longitude > 180 {
		return false
	}

	// Check for unrealistic speed values (e.g., > 200 km/h)
	if gpsData.Speed > 200 {
		return false
	}

	// Check for unrealistic altitude values (e.g., > 9000m or < -500m)
	if gpsData.Altitude > 9000 || gpsData.Altitude < -500 {
		return false
	}

	return true
}
func UpdateDeviceLastLocation(device *models.ClientDevice, latitude float64, longitude float64, speed float64) {
	if device.Latitude != nil && device.Longitude != nil {
		distance := utils.HaversineDistance(*device.Latitude, *device.Longitude, latitude, longitude)
		if distance > 0.01 && speed > 0 {
			zero := 0.0
			if device.DistanceCovered == nil {

				device.DistanceCovered = &zero
				*device.DistanceCovered += distance
			}
			if device.Mileage == nil {
				zero = 0.0
				device.Mileage = &zero
				*device.Mileage += distance
			}

		}
	}
	device.Latitude = &latitude
	device.Longitude = &longitude
	// Update status (moving, idling, stopped)
	var status string
	if speed > 0 {
		status = "moving"
	} else if speed == 0 && device.LastStatusUpdate != nil && time.Since(*device.LastStatusUpdate) < 10*time.Minute {
		status = "idling"
	} else {
		status = "stopped"
	}
	device.DeviceStatus = &status
	timeNow := time.Now()
	device.LastStatusUpdate = &timeNow

	config.DB.Updates(device)
}

// NOTE: Daily stats are now updated in trip_detection_service.g
// NOTE: Daily stats are now updated in trip_detection_service.go
// Commenting out to avoid duplicate updates and conflicts
// func AddDistanceToDailyStat(deviceId uint, timestamp time.Time, distance float64) error {
// 	// Truncate time to get the daily date key
// 	date := timestamp.Truncate(24 * time.Hour)

// 	// Use upsert logic to insert or update
// 	return config.DB.Clauses(clause.OnConflict{
// 		Columns: []clause.Column{
// 			{Name: "client_device_id"},
// 			{Name: "date"},
// 		},
// 		DoUpdates: clause.Assignments(map[string]interface{}{
// 			"distance":   gorm.Expr("distance + ?", distance),
// 			"updated_at": time.Now(),
// 		}),
// 	}).Create(&models.ClientDeviceDailyStat{
// 		ClientDeviceId: deviceId,
// 		Date:           date,
// 		Distance:       distance, // initial value if new row
// 	}).Error
// }
