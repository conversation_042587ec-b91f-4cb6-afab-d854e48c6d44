package service

import (
	"testing"
	"yotracker/internal/models"
)

// Test validation functions without database dependency
func TestGeofenceValidation(t *testing.T) {
	// Test 1: Validate coordinates
	t.Run("Validate coordinates", func(t *testing.T) {
		// Valid coordinates
		err := ValidateGeofenceCoordinates(-17.8216, 31.0492)
		if err != nil {
			t.<PERSON>rf("Expected valid coordinates to pass validation, but got error: %v", err)
		}

		// Valid edge cases
		err = ValidateGeofenceCoordinates(-90.0, -180.0)
		if err != nil {
			t.<PERSON><PERSON>rf("Expected valid edge coordinates to pass validation, but got error: %v", err)
		}

		err = ValidateGeofenceCoordinates(90.0, 180.0)
		if err != nil {
			t.<PERSON><PERSON>rf("Expected valid edge coordinates to pass validation, but got error: %v", err)
		}

		// Invalid latitude - too high
		err = ValidateGeofenceCoordinates(91.0, 31.0492)
		if err == nil {
			t.<PERSON><PERSON><PERSON>("Expected invalid latitude (91.0) to fail validation")
		}

		// Invalid latitude - too low
		err = ValidateGeofenceCoordinates(-91.0, 31.0492)
		if err == nil {
			t.Error("Expected invalid latitude (-91.0) to fail validation")
		}

		// Invalid longitude - too high
		err = ValidateGeofenceCoordinates(-17.8216, 181.0)
		if err == nil {
			t.Error("Expected invalid longitude (181.0) to fail validation")
		}

		// Invalid longitude - too low
		err = ValidateGeofenceCoordinates(-17.8216, -181.0)
		if err == nil {
			t.Error("Expected invalid longitude (-181.0) to fail validation")
		}
	})

	// Test 2: Validate radius
	t.Run("Validate radius", func(t *testing.T) {
		// Valid radius
		err := ValidateGeofenceRadius(100)
		if err != nil {
			t.Errorf("Expected valid radius to pass validation, but got error: %v", err)
		}

		// Valid minimum radius
		err = ValidateGeofenceRadius(10)
		if err != nil {
			t.Errorf("Expected minimum valid radius (10) to pass validation, but got error: %v", err)
		}

		// Valid maximum radius
		err = ValidateGeofenceRadius(50000)
		if err != nil {
			t.Errorf("Expected maximum valid radius (50000) to pass validation, but got error: %v", err)
		}

		// Too small radius
		err = ValidateGeofenceRadius(5)
		if err == nil {
			t.Error("Expected small radius (5) to fail validation")
		}

		// Too large radius
		err = ValidateGeofenceRadius(60000)
		if err == nil {
			t.Error("Expected large radius (60000) to fail validation")
		}

		// Zero radius
		err = ValidateGeofenceRadius(0)
		if err == nil {
			t.Error("Expected zero radius to fail validation")
		}

		// Negative radius
		err = ValidateGeofenceRadius(-100)
		if err == nil {
			t.Error("Expected negative radius to fail validation")
		}
	})

	// Test 3: Validate geofence by type
	t.Run("Validate geofence by type", func(t *testing.T) {
		// Valid circle geofence
		lat := -17.8216
		lng := 31.0492
		radius := 100.0
		deviceId := uint(123)
		circleGeofence := models.CreateGeofenceRequest{
			Name:           "Test Circle",
			GeofenceType:   "circle",
			AppliesTo:      "device",
			ClientDeviceId: &deviceId,
			Latitude:       &lat,
			Longitude:      &lng,
			Radius:         &radius,
		}

		err := ValidateGeofenceByType(circleGeofence)
		if err != nil {
			t.Errorf("Expected valid circle geofence to pass validation, but got error: %v", err)
		}

		// Circle geofence missing radius
		circleGeofenceNoRadius := models.CreateGeofenceRequest{
			Name:           "Test Circle",
			GeofenceType:   "circle",
			AppliesTo:      "device",
			ClientDeviceId: &deviceId,
			Latitude:       &lat,
			Longitude:      &lng,
		}

		err = ValidateGeofenceByType(circleGeofenceNoRadius)
		if err == nil {
			t.Error("Expected circle geofence without radius to fail validation")
		}

		// Valid polygon geofence
		coordinates := `[{"lat":-17.8216,"lng":31.0492},{"lat":-17.8220,"lng":31.0500},{"lat":-17.8210,"lng":31.0510}]`
		fleetId := uint(456)
		polygonGeofence := models.CreateGeofenceRequest{
			Name:         "Test Polygon",
			GeofenceType: "polygon",
			AppliesTo:    "fleet",
			FleetId:      &fleetId,
			Coordinates:  &coordinates,
		}

		err = ValidateGeofenceByType(polygonGeofence)
		if err != nil {
			t.Errorf("Expected valid polygon geofence to pass validation, but got error: %v", err)
		}

		// Polygon geofence missing coordinates
		polygonGeofenceNoCoords := models.CreateGeofenceRequest{
			Name:         "Test Polygon",
			GeofenceType: "polygon",
			AppliesTo:    "fleet",
			FleetId:      &fleetId,
		}

		err = ValidateGeofenceByType(polygonGeofenceNoCoords)
		if err == nil {
			t.Error("Expected polygon geofence without coordinates to fail validation")
		}

		// Valid rectangle geofence
		rectCoordinates := `[{"lat":-17.8216,"lng":31.0492},{"lat":-17.8220,"lng":31.0492},{"lat":-17.8220,"lng":31.0500},{"lat":-17.8216,"lng":31.0500}]`
		rectangleGeofence := models.CreateGeofenceRequest{
			Name:         "Test Rectangle",
			GeofenceType: "rectangle",
			AppliesTo:    "client",
			Coordinates:  &rectCoordinates,
		}

		err = ValidateGeofenceByType(rectangleGeofence)
		if err != nil {
			t.Errorf("Expected valid rectangle geofence to pass validation, but got error: %v", err)
		}

		// Rectangle geofence with wrong number of points
		wrongRectCoordinates := `[{"lat":-17.8216,"lng":31.0492},{"lat":-17.8220,"lng":31.0492}]`
		wrongRectangleGeofence := models.CreateGeofenceRequest{
			Name:         "Test Wrong Rectangle",
			GeofenceType: "rectangle",
			Coordinates:  &wrongRectCoordinates,
		}

		err = ValidateGeofenceByType(wrongRectangleGeofence)
		if err == nil {
			t.Error("Expected rectangle geofence with wrong number of points to fail validation")
		}

		// Unsupported geofence type
		unsupportedGeofence := models.CreateGeofenceRequest{
			Name:         "Test Unsupported",
			GeofenceType: "hexagon",
		}

		err = ValidateGeofenceByType(unsupportedGeofence)
		if err == nil {
			t.Error("Expected unsupported geofence type to fail validation")
		}

		// Default to circle when no type specified
		defaultGeofence := models.CreateGeofenceRequest{
			Name:           "Test Default",
			AppliesTo:      "client",
			Latitude:       &lat,
			Longitude:      &lng,
			Radius:         &radius,
		}

		err = ValidateGeofenceByType(defaultGeofence)
		if err != nil {
			t.Errorf("Expected default geofence (circle) to pass validation, but got error: %v", err)
		}
	})

	// Test 4: Validate scope
	t.Run("Validate scope", func(t *testing.T) {
		// Re-declare variables for this test scope
		lat := -17.8216
		lng := 31.0492
		radius := 100.0
		deviceId := uint(123)
		fleetId := uint(456)

		// Valid device scope
		deviceScopeGeofence := models.CreateGeofenceRequest{
			Name:           "Device Scope",
			AppliesTo:      "device",
			ClientDeviceId: &deviceId,
			Latitude:       &lat,
			Longitude:      &lng,
			Radius:         &radius,
		}

		err := ValidateGeofenceByType(deviceScopeGeofence)
		if err != nil {
			t.Errorf("Expected valid device scope geofence to pass validation, but got error: %v", err)
		}

		// Invalid device scope - missing client_device_id
		invalidDeviceScopeGeofence := models.CreateGeofenceRequest{
			Name:      "Invalid Device Scope",
			AppliesTo: "device",
			Latitude:  &lat,
			Longitude: &lng,
			Radius:    &radius,
		}

		err = ValidateGeofenceByType(invalidDeviceScopeGeofence)
		if err == nil {
			t.Error("Expected device scope geofence without client_device_id to fail validation")
		}

		// Valid fleet scope
		fleetScopeGeofence := models.CreateGeofenceRequest{
			Name:      "Fleet Scope",
			AppliesTo: "fleet",
			FleetId:   &fleetId,
			Latitude:  &lat,
			Longitude: &lng,
			Radius:    &radius,
		}

		err = ValidateGeofenceByType(fleetScopeGeofence)
		if err != nil {
			t.Errorf("Expected valid fleet scope geofence to pass validation, but got error: %v", err)
		}

		// Invalid fleet scope - missing fleet_id
		invalidFleetScopeGeofence := models.CreateGeofenceRequest{
			Name:      "Invalid Fleet Scope",
			AppliesTo: "fleet",
			Latitude:  &lat,
			Longitude: &lng,
			Radius:    &radius,
		}

		err = ValidateGeofenceByType(invalidFleetScopeGeofence)
		if err == nil {
			t.Error("Expected fleet scope geofence without fleet_id to fail validation")
		}

		// Valid client scope
		clientScopeGeofence := models.CreateGeofenceRequest{
			Name:      "Client Scope",
			AppliesTo: "client",
			Latitude:  &lat,
			Longitude: &lng,
			Radius:    &radius,
		}

		err = ValidateGeofenceByType(clientScopeGeofence)
		if err != nil {
			t.Errorf("Expected valid client scope geofence to pass validation, but got error: %v", err)
		}
	})

	// Test 5: Validate trigger events
	t.Run("Validate trigger events", func(t *testing.T) {
		lat := -17.8216
		lng := 31.0492
		radius := 100.0

		// Valid trigger events
		validTriggerEvents := []string{"entry", "exit", "both"}
		for _, triggerEvent := range validTriggerEvents {
			geofence := models.CreateGeofenceRequest{
				Name:          "Test Trigger Events",
				AppliesTo:     "client",
				TriggerEvents: triggerEvent,
				Latitude:      &lat,
				Longitude:     &lng,
				Radius:        &radius,
			}

			err := ValidateGeofenceByType(geofence)
			if err != nil {
				t.Errorf("Expected trigger_events '%s' to pass validation, but got error: %v", triggerEvent, err)
			}
		}

		// Invalid trigger events
		invalidGeofence := models.CreateGeofenceRequest{
			Name:          "Invalid Trigger Events",
			AppliesTo:     "client",
			TriggerEvents: "invalid",
			Latitude:      &lat,
			Longitude:     &lng,
			Radius:        &radius,
		}

		err := ValidateGeofenceByType(invalidGeofence)
		if err == nil {
			t.Error("Expected invalid trigger_events to fail validation")
		}

		// Default trigger events (empty should default to "both")
		defaultGeofence := models.CreateGeofenceRequest{
			Name:      "Default Trigger Events",
			AppliesTo: "client",
			Latitude:  &lat,
			Longitude: &lng,
			Radius:    &radius,
		}

		err = ValidateGeofenceByType(defaultGeofence)
		if err != nil {
			t.Errorf("Expected default trigger_events to pass validation, but got error: %v", err)
		}
	})
}

// Test point-in-geofence checking
func TestPointInGeofence(t *testing.T) {
	t.Run("Point in circle geofence", func(t *testing.T) {
		lat := -17.8216
		lng := 31.0492
		radius := 100.0

		circleGeofence := models.Geofence{
			GeofenceType: "circle",
			Latitude:     &lat,
			Longitude:    &lng,
			Radius:       &radius,
		}

		// Point at center should be inside
		inside := isPointInsideGeofence(-17.8216, 31.0492, circleGeofence)
		if !inside {
			t.Error("Expected point at center to be inside circle geofence")
		}

		// Point far away should be outside
		outside := isPointInsideGeofence(-17.8300, 31.0600, circleGeofence)
		if outside {
			t.Error("Expected distant point to be outside circle geofence")
		}
	})

	t.Run("Point in polygon geofence", func(t *testing.T) {
		// Square polygon
		coordinates := `[{"lat":-17.8216,"lng":31.0492},{"lat":-17.8220,"lng":31.0492},{"lat":-17.8220,"lng":31.0500},{"lat":-17.8216,"lng":31.0500}]`

		polygonGeofence := models.Geofence{
			GeofenceType: "polygon",
			Coordinates:  &coordinates,
		}

		// Point inside the square
		inside := isPointInsideGeofence(-17.8218, 31.0496, polygonGeofence)
		if !inside {
			t.Error("Expected point inside polygon to be detected as inside")
		}

		// Point outside the square
		outside := isPointInsideGeofence(-17.8230, 31.0510, polygonGeofence)
		if outside {
			t.Error("Expected point outside polygon to be detected as outside")
		}
	})

	t.Run("Point in rectangle geofence", func(t *testing.T) {
		// Rectangle coordinates
		coordinates := `[{"lat":-17.8216,"lng":31.0492},{"lat":-17.8220,"lng":31.0492},{"lat":-17.8220,"lng":31.0500},{"lat":-17.8216,"lng":31.0500}]`

		rectangleGeofence := models.Geofence{
			GeofenceType: "rectangle",
			Coordinates:  &coordinates,
		}

		// Point inside the rectangle
		inside := isPointInsideGeofence(-17.8218, 31.0496, rectangleGeofence)
		if !inside {
			t.Error("Expected point inside rectangle to be detected as inside")
		}

		// Point outside the rectangle
		outside := isPointInsideGeofence(-17.8230, 31.0510, rectangleGeofence)
		if outside {
			t.Error("Expected point outside rectangle to be detected as outside")
		}
	})
}

// Test trigger event logic
func TestTriggerEvents(t *testing.T) {
	t.Run("Should trigger event", func(t *testing.T) {
		// Test "entry" trigger
		if !shouldTriggerEvent("entry", "entry") {
			t.Error("Expected entry trigger to allow entry events")
		}
		if shouldTriggerEvent("entry", "exit") {
			t.Error("Expected entry trigger to block exit events")
		}

		// Test "exit" trigger
		if shouldTriggerEvent("exit", "entry") {
			t.Error("Expected exit trigger to block entry events")
		}
		if !shouldTriggerEvent("exit", "exit") {
			t.Error("Expected exit trigger to allow exit events")
		}

		// Test "both" trigger
		if !shouldTriggerEvent("both", "entry") {
			t.Error("Expected both trigger to allow entry events")
		}
		if !shouldTriggerEvent("both", "exit") {
			t.Error("Expected both trigger to allow exit events")
		}

		// Test default (empty) trigger - should default to "both"
		if !shouldTriggerEvent("", "entry") {
			t.Error("Expected default trigger to allow entry events")
		}
		if !shouldTriggerEvent("", "exit") {
			t.Error("Expected default trigger to allow exit events")
		}

		// Test unknown trigger - should default to "both"
		if !shouldTriggerEvent("unknown", "entry") {
			t.Error("Expected unknown trigger to default to allowing entry events")
		}
		if !shouldTriggerEvent("unknown", "exit") {
			t.Error("Expected unknown trigger to default to allowing exit events")
		}
	})
}

// Test cache functionality
func TestGeofenceStateCache(t *testing.T) {
	t.Run("Clear geofence state cache", func(t *testing.T) {
		// Test that clearing cache doesn't panic
		ClearGeofenceStateCache(123)

		// Test clearing non-existent cache entry
		ClearGeofenceStateCache(999)
	})
}
