package service

import (
	"fmt"
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/models"

	"gorm.io/gorm"
)

// ReportService handles report generation and management
type ReportService struct{}

// NewReportService creates a new report service
func NewReportService() *ReportService {
	return &ReportService{}
}

// GenerateReport generates a report based on the report type and filters
func (s *ReportService) GenerateReport(reportId uint, filters models.ReportFilters, format string) (*models.ReportData, error) {
	startTime := time.Now()

	// Get report definition
	var report models.Report
	err := config.DB.First(&report, reportId).Error
	if err != nil {
		return nil, fmt.Errorf("report not found: %v", err)
	}

	log.Printf("Generating report: %s (%s)", report.Name, report.ReportType)

	// Generate report data based on type
	var data interface{}
	var summary interface{}
	var totalRecords, filteredRecords int

	switch report.ReportType {
	// Core reports
	case "position_log":
		data, totalRecords, filteredRecords, err = s.generatePositionLogReport(filters)
	case "trip_detail":
		data, totalRecords, filteredRecords, err = s.generateTripDetailReport(filters)
	case "trip_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateTripSummaryReport(filters)
	case "speeding_detail":
		data, totalRecords, filteredRecords, err = s.generateSpeedingDetailReport(filters)
	case "driver_performance_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateDriverPerformanceReport(filters)

	// Powerful new reports
	case "driver_safety_scorecard":
		data, summary, totalRecords, filteredRecords, err = s.generateDriverSafetyScorecardReport(filters)
	case "speeding_violations":
		data, totalRecords, filteredRecords, err = s.generateSpeedingViolationsReport(filters)
	case "geofence_activity":
		data, totalRecords, filteredRecords, err = s.generateGeofenceActivityReport(filters)
	case "fleet_roi_dashboard":
		data, summary, totalRecords, filteredRecords, err = s.generateFleetROIDashboard(filters)
	case "executive_fleet_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateExecutiveFleetSummary(filters)
	case "vehicle_utilization":
		data, summary, totalRecords, filteredRecords, err = s.generateVehicleUtilizationReport(filters)
	case "fuel_consumption_analysis":
		data, summary, totalRecords, filteredRecords, err = s.generateFuelConsumptionAnalysis(filters)
	case "emergency_response":
		data, totalRecords, filteredRecords, err = s.generateEmergencyResponseReport(filters)

	default:
		return nil, fmt.Errorf("report type %s not implemented yet", report.ReportType)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to generate report data: %v", err)
	}

	// Create report response
	reportData := &models.ReportData{
		ReportInfo: report,
		Filters:    filters,
		Data:       data,
		Summary:    summary,
		Metadata: models.ReportMetadata{
			GeneratedAt:     time.Now(),
			TotalRecords:    totalRecords,
			FilteredRecords: filteredRecords,
			ExecutionTime:   time.Since(startTime).String(),
			Format:          format,
		},
	}

	log.Printf("Report generated successfully: %d records in %s", filteredRecords, time.Since(startTime))
	return reportData, nil
}

// generatePositionLogReport generates comprehensive GPS position data
func (s *ReportService) generatePositionLogReport(filters models.ReportFilters) (interface{}, int, int, error) {
	// Enhanced position log with device and driver information
	var data []struct {
		models.GPSData
		DeviceName  string  `json:"device_name"`
		DeviceType  string  `json:"device_type"`
		PlateNumber string  `json:"plate_number"`
		DriverName  *string `json:"driver_name"`
		TripId      *uint   `json:"trip_id"`
	}

	query := config.DB.Table("gps_data g").
		Select(`
			g.*,
			cd.name as device_name,
			dt.name as device_type,
			cd.plate_number,
			d.name as driver_name,
			g.trip_id
		`).
		Joins("LEFT JOIN client_devices cd ON g.client_device_id = cd.id").
		Joins("LEFT JOIN device_types dt ON cd.device_type_id = dt.id").
		Joins("LEFT JOIN trips t ON g.trip_id = t.id").
		Joins("LEFT JOIN drivers d ON t.driver_id = d.id").
		Order("g.gps_timestamp DESC")

	// Apply filters
	query = s.applyGPSFilters(query, filters)

	// Get total count
	var totalCount int64
	config.DB.Model(&models.GPSData{}).Count(&totalCount)

	// Get filtered count
	var filteredCount int64
	countQuery := config.DB.Table("gps_data g").
		Joins("LEFT JOIN client_devices cd ON g.client_device_id = cd.id").
		Joins("LEFT JOIN device_types dt ON cd.device_type_id = dt.id").
		Joins("LEFT JOIN trips t ON g.trip_id = t.id").
		Joins("LEFT JOIN drivers d ON t.driver_id = d.id")
	countQuery = s.applyGPSFilters(countQuery, filters)
	countQuery.Count(&filteredCount)

	// Apply pagination (default to 1000 records if not specified)
	perPage := filters.PerPage
	if perPage == 0 {
		perPage = 1000 // Default for position logs
	}

	offset := 0
	if filters.Page > 0 {
		offset = (filters.Page - 1) * perPage
	}
	query = query.Offset(offset).Limit(perPage)

	err := query.Scan(&data).Error

	return data, int(totalCount), int(filteredCount), err
}

// generateTripDetailReport generates detailed trip information
func (s *ReportService) generateTripDetailReport(filters models.ReportFilters) (interface{}, int, int, error) {
	query := config.DB.Model(&models.Trip{}).
		Preload("ClientDevice").
		Order("start_time DESC")

	// Apply filters
	query = s.applyTripFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []models.Trip
	err := query.Find(&data).Error

	return data, int(totalCount), int(filteredCount), err
}

// generateTripSummaryReport generates trip summary statistics
func (s *ReportService) generateTripSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Detailed data
	data, totalCount, filteredCount, err := s.generateTripDetailReport(filters)
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Summary statistics
	var summary struct {
		TotalTrips    int     `json:"total_trips"`
		TotalDistance float64 `json:"total_distance"`
		TotalDuration int     `json:"total_duration"`
		AvgDistance   float64 `json:"avg_distance"`
		AvgDuration   float64 `json:"avg_duration"`
		AvgSpeed      float64 `json:"avg_speed"`
	}

	query := config.DB.Model(&models.Trip{})
	query = s.applyTripFilters(query, filters)

	err = query.Select(`
		COUNT(*) as total_trips,
		COALESCE(SUM(distance), 0) as total_distance,
		COALESCE(SUM(duration), 0) as total_duration,
		COALESCE(AVG(distance), 0) as avg_distance,
		COALESCE(AVG(duration), 0) as avg_duration,
		COALESCE(AVG(avg_speed), 0) as avg_speed
	`).Scan(&summary).Error

	return data, summary, totalCount, filteredCount, err
}

// generateSpeedingDetailReport generates speeding events
func (s *ReportService) generateSpeedingDetailReport(filters models.ReportFilters) (interface{}, int, int, error) {
	query := config.DB.Model(&models.DrivingBehaviorEvent{}).
		Where("event_type = ?", models.EventTypeOverspeed).
		Preload("ClientDevice").
		Order("timestamp DESC")

	// Apply filters
	query = s.applyBehaviorFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.DrivingBehaviorEvent{}).Where("event_type = ?", models.EventTypeOverspeed).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []models.DrivingBehaviorEvent
	err := query.Find(&data).Error

	return data, int(totalCount), int(filteredCount), err
}

// generateDriverPerformanceReport generates driver performance summary
func (s *ReportService) generateDriverPerformanceReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Get driver performance data
	var data []struct {
		DriverId      *uint   `json:"driver_id"`
		DriverName    string  `json:"driver_name"`
		TotalTrips    int     `json:"total_trips"`
		TotalDistance float64 `json:"total_distance"`
		TotalEvents   int     `json:"total_events"`
		SafetyScore   float64 `json:"safety_score"`
		AvgSpeed      float64 `json:"avg_speed"`
	}

	query := `
		SELECT 
			t.driver_id,
			COALESCE(d.name, 'Unknown Driver') as driver_name,
			COUNT(DISTINCT t.id) as total_trips,
			COALESCE(SUM(t.distance), 0) as total_distance,
			COUNT(DISTINCT dbe.id) as total_events,
			GREATEST(0, 100 - (COUNT(DISTINCT dbe.id) * 2)) as safety_score,
			COALESCE(AVG(t.avg_speed), 0) as avg_speed
		FROM trips t
		LEFT JOIN drivers d ON t.driver_id = d.id
		LEFT JOIN driving_behavior_events dbe ON t.id = dbe.trip_id
		WHERE 1=1
	`

	// Add date filters
	args := []interface{}{}
	if filters.StartDate != nil {
		query += " AND t.start_time >= ?"
		args = append(args, *filters.StartDate)
	}
	if filters.EndDate != nil {
		query += " AND t.start_time <= ?"
		args = append(args, *filters.EndDate)
	}

	query += " GROUP BY t.driver_id, d.name ORDER BY safety_score DESC"

	err := config.DB.Raw(query, args...).Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Summary
	summary := struct {
		TotalDrivers   int     `json:"total_drivers"`
		AvgSafetyScore float64 `json:"avg_safety_score"`
	}{
		TotalDrivers: len(data),
	}

	if len(data) > 0 {
		var totalScore float64
		for _, d := range data {
			totalScore += d.SafetyScore
		}
		summary.AvgSafetyScore = totalScore / float64(len(data))
	}

	return data, summary, len(data), len(data), nil
}

// Helper functions for applying filters
func (s *ReportService) applyGPSFilters(query *gorm.DB, filters models.ReportFilters) *gorm.DB {
	if filters.StartDate != nil {
		query = query.Where("g.gps_timestamp >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("g.gps_timestamp <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("g.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if filters.MinSpeed != nil {
		query = query.Where("g.speed >= ?", *filters.MinSpeed)
	}
	if filters.MaxSpeed != nil {
		query = query.Where("g.speed <= ?", *filters.MaxSpeed)
	}
	return query
}

func (s *ReportService) applyTripFilters(query *gorm.DB, filters models.ReportFilters) *gorm.DB {
	if filters.StartDate != nil {
		query = query.Where("start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("start_time <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("driver_id IN ?", filters.DriverIds)
	}
	if filters.MinDistance != nil {
		query = query.Where("distance >= ?", *filters.MinDistance)
	}
	if filters.MaxDistance != nil {
		query = query.Where("distance <= ?", *filters.MaxDistance)
	}
	return query
}

func (s *ReportService) applyBehaviorFilters(query *gorm.DB, filters models.ReportFilters) *gorm.DB {
	if filters.StartDate != nil {
		query = query.Where("timestamp >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("timestamp <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.EventTypes) > 0 {
		query = query.Where("event_type IN ?", filters.EventTypes)
	}
	if filters.MinSeverity != nil {
		query = query.Where("severity >= ?", *filters.MinSeverity)
	}
	if filters.MaxSeverity != nil {
		query = query.Where("severity <= ?", *filters.MaxSeverity)
	}
	return query
}

// === POWERFUL REPORT GENERATORS ===

// generateDriverSafetyScorecardReport generates comprehensive driver safety scoring
func (s *ReportService) generateDriverSafetyScorecardReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	var data []struct {
		DriverId       *uint   `json:"driver_id"`
		DriverName     string  `json:"driver_name"`
		TotalTrips     int     `json:"total_trips"`
		TotalDistance  float64 `json:"total_distance"`
		SpeedingEvents int     `json:"speeding_events"`
		HarshEvents    int     `json:"harsh_events"`
		SafetyScore    float64 `json:"safety_score"`
		RiskLevel      string  `json:"risk_level"`
	}

	query := `
		SELECT
			t.driver_id,
			COALESCE(d.name, 'Unknown Driver') as driver_name,
			COUNT(DISTINCT t.id) as total_trips,
			COALESCE(SUM(t.distance), 0) as total_distance,
			COUNT(CASE WHEN dbe.event_type = 'overspeed' THEN 1 END) as speeding_events,
			COUNT(CASE WHEN dbe.event_type IN ('harsh_braking', 'harsh_acceleration', 'harsh_cornering') THEN 1 END) as harsh_events,
			GREATEST(0, 100 - (COUNT(dbe.id) * 2)) as safety_score,
			CASE
				WHEN COUNT(dbe.id) = 0 THEN 'Excellent'
				WHEN COUNT(dbe.id) <= 5 THEN 'Good'
				WHEN COUNT(dbe.id) <= 15 THEN 'Fair'
				ELSE 'Critical'
			END as risk_level
		FROM trips t
		LEFT JOIN drivers d ON t.driver_id = d.id
		LEFT JOIN driving_behavior_events dbe ON t.id = dbe.trip_id
		WHERE 1=1
	`

	args := []interface{}{}
	if filters.StartDate != nil {
		query += " AND t.start_time >= ?"
		args = append(args, *filters.StartDate)
	}
	if filters.EndDate != nil {
		query += " AND t.start_time <= ?"
		args = append(args, *filters.EndDate)
	}

	query += " GROUP BY t.driver_id, d.name ORDER BY safety_score DESC"

	err := config.DB.Raw(query, args...).Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Summary
	summary := struct {
		TotalDrivers     int     `json:"total_drivers"`
		AvgSafetyScore   float64 `json:"avg_safety_score"`
		ExcellentDrivers int     `json:"excellent_drivers"`
		CriticalDrivers  int     `json:"critical_drivers"`
	}{
		TotalDrivers: len(data),
	}

	var totalScore float64
	for _, d := range data {
		totalScore += d.SafetyScore
		if d.RiskLevel == "Excellent" {
			summary.ExcellentDrivers++
		} else if d.RiskLevel == "Critical" {
			summary.CriticalDrivers++
		}
	}

	if len(data) > 0 {
		summary.AvgSafetyScore = totalScore / float64(len(data))
	}

	return data, summary, len(data), len(data), nil
}

// generateSpeedingViolationsReport generates detailed speeding violations
func (s *ReportService) generateSpeedingViolationsReport(filters models.ReportFilters) (interface{}, int, int, error) {
	query := config.DB.Model(&models.DrivingBehaviorEvent{}).
		Where("event_type = ?", "overspeed").
		Order("timestamp DESC")

	query = s.applyBehaviorFilters(query, filters)

	var totalCount, filteredCount int64
	config.DB.Model(&models.DrivingBehaviorEvent{}).Where("event_type = ?", "overspeed").Count(&totalCount)

	// Create a separate query for counting to avoid the column issue
	countQuery := config.DB.Model(&models.DrivingBehaviorEvent{}).Where("event_type = ?", "overspeed")
	countQuery = s.applyBehaviorFilters(countQuery, filters)
	countQuery.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []models.DrivingBehaviorEvent
	err := query.Find(&data).Error
	return data, int(totalCount), int(filteredCount), err
}

// Placeholder functions for other powerful reports
func (s *ReportService) generateGeofenceActivityReport(filters models.ReportFilters) (interface{}, int, int, error) {
	// TODO: Implement when geofence system is ready
	return []string{"Geofence activity report - coming soon"}, 0, 0, nil
}

func (s *ReportService) generateFleetROIDashboard(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// TODO: Implement ROI calculations
	summary := map[string]interface{}{
		"message":        "Fleet ROI dashboard - coming soon",
		"roi_percentage": 15.5,
		"cost_savings":   25000,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateExecutiveFleetSummary(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// TODO: Implement executive summary
	summary := map[string]interface{}{
		"message":          "Executive fleet summary - coming soon",
		"total_vehicles":   10,
		"total_distance":   50000,
		"avg_safety_score": 85.5,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateVehicleUtilizationReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// TODO: Implement utilization calculations
	summary := map[string]interface{}{
		"message":         "Vehicle utilization report - coming soon",
		"avg_utilization": 75.5,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateFuelConsumptionAnalysis(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// TODO: Implement fuel analysis
	summary := map[string]interface{}{
		"message":           "Fuel consumption analysis - coming soon",
		"total_fuel_cost":   15000,
		"efficiency_rating": "Good",
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateEmergencyResponseReport(filters models.ReportFilters) (interface{}, int, int, error) {
	// TODO: Implement emergency response tracking
	return []string{"Emergency response report - coming soon"}, 0, 0, nil
}
