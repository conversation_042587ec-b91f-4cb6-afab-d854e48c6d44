package service

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"
	"yotracker/internal/models"
)

func TestMain(m *testing.M) {
	// Set test mode
	os.Setenv("GO_ENV", "test")
	os.Setenv("TESTING_DB_NAME", "testing")

	// Run tests
	code := m.Run()

	os.Exit(code)
}

func TestNewSlackService(t *testing.T) {
	tests := []struct {
		name        string
		botToken    string
		webhookURL  string
		expectError bool
	}{
		{
			name:        "Valid bot token",
			botToken:    "xoxb-test-token",
			webhookURL:  "",
			expectError: false,
		},
		{
			name:        "Valid webhook URL",
			botToken:    "",
			webhookURL:  "https://hooks.slack.com/test",
			expectError: false,
		},
		{
			name:        "Both bot token and webhook",
			botToken:    "xoxb-test-token",
			webhookURL:  "https://hooks.slack.com/test",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variables
			os.Setenv("SLACK_BOT_TOKEN", tt.botToken)
			os.Setenv("SLACK_WEBHOOK_URL", tt.webhookURL)

			service, err := NewSlackService()

			if tt.expectError {
				if err == nil {
					t.Error("Expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if service == nil {
					t.Error("Expected service but got nil")
				}
			}

			// Clean up
			os.Unsetenv("SLACK_BOT_TOKEN")
			os.Unsetenv("SLACK_WEBHOOK_URL")
		})
	}
}

// TestNewSlackService_NoConfiguration tests the error case when no configuration is provided
// This test is separate because it requires disabling test mode to properly test the error condition
func TestNewSlackService_NoConfiguration(t *testing.T) {
	// Skip this test since it requires database access which isn't available in test mode
	t.Skip("Skipping no configuration test - requires database access")
}

// Skip database-dependent tests since we don't have DB setup in this test environment

func TestSlackService_DetermineSeverity(t *testing.T) {
	service := &SlackService{}

	tests := []struct {
		alertType string
		expected  AlertSeverity
	}{
		{"critical_alert", SeverityCritical},
		{"EMERGENCY", SeverityCritical},
		{"warning_message", SeverityWarning},
		{"ALERT_TYPE", SeverityWarning},
		{"info_message", SeverityInfo},
		{"normal", SeverityInfo},
	}

	for _, tt := range tests {
		t.Run(tt.alertType, func(t *testing.T) {
			result := service.determineSeverity(tt.alertType)
			if result != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestSlackService_GetSeverityColor(t *testing.T) {
	service := &SlackService{}

	tests := []struct {
		severity AlertSeverity
		expected string
	}{
		{SeverityCritical, "#FF0000"},
		{SeverityWarning, "#FFA500"},
		{SeverityInfo, "#36A64F"},
	}

	for _, tt := range tests {
		t.Run(string(tt.severity), func(t *testing.T) {
			result := service.getSeverityColor(tt.severity)
			if result != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestSlackService_SendAlert_TestMode(t *testing.T) {
	// Ensure test mode is set
	os.Setenv("GO_ENV", "test")
	os.Setenv("TESTING_DB_NAME", "testing")

	service := &SlackService{
		botToken: "test-token",
	}

	alert := &models.Alert{
		DeviceId:       func() *string { s := "TEST123"; return &s }(),
		AlertType:      "warning",
		Message:        func() *string { s := "Test message"; return &s }(),
		AlertTimestamp: time.Now(),
	}

	// In test mode, this should return nil without error (skips actual sending)
	err := service.SendAlert(alert, nil)
	if err != nil {
		t.Errorf("Unexpected error in test mode: %v", err)
	}
}

func TestSlackService_SendToWebhook(t *testing.T) {
	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			t.Errorf("Expected POST request, got %s", r.Method)
		}

		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("Expected Content-Type application/json, got %s", r.Header.Get("Content-Type"))
		}

		var message SlackMessage
		if err := json.NewDecoder(r.Body).Decode(&message); err != nil {
			t.Errorf("Failed to decode request body: %v", err)
		}

		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	service := &SlackService{}

	message := &SlackMessage{
		Text: "Test message",
		Blocks: []SlackBlock{
			{
				Type: "section",
				Text: &SlackText{
					Type: "mrkdwn",
					Text: "Test block",
				},
			},
		},
	}

	// Test in non-test mode temporarily
	originalEnv := os.Getenv("GO_ENV")
	os.Setenv("GO_ENV", "production")

	err := service.SendToWebhook(server.URL, message)

	// Restore test mode
	os.Setenv("GO_ENV", originalEnv)

	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}

func TestSlackService_SendRawDataLog_TestMode(t *testing.T) {
	// Ensure test mode is set
	os.Setenv("GO_ENV", "test")
	os.Setenv("TESTING_DB_NAME", "testing")

	service := &SlackService{
		botToken: "test-token",
	}

	// Should not return error in test mode
	err := service.SendRawDataLog("Test Device", "DEV123", "48656C6C6F20576F726C64")
	if err != nil {
		t.Errorf("Unexpected error in test mode: %v", err)
	}
}

func TestSlackService_FormatHexData(t *testing.T) {
	service := &SlackService{}

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Simple hex string",
			input:    "48656C6C6F",
			expected: "48 65 6C 6C 6F",
		},
		{
			name:     "Long hex string with line breaks",
			input:    "48656C6C6F20576F726C6448656C6C6F20576F726C6448656C6C6F20576F726C64",
			expected: "48 65 6C 6C 6F 20 57 6F 72 6C 64 48 65 6C 6C 6F\n20 57 6F 72 6C 64 48 65 6C 6C 6F 20 57 6F 72 6C\n64",
		},
		{
			name:     "Hex with existing spaces",
			input:    "48 65 6C 6C 6F",
			expected: "48 65 6C 6C 6F",
		},
		{
			name:     "Lowercase hex",
			input:    "48656c6c6f",
			expected: "48 65 6C 6C 6F",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.formatHexData(tt.input)
			if result != tt.expected {
				t.Errorf("Expected %q, got %q", tt.expected, result)
			}
		})
	}
}

func TestSlackService_GetRawDataChannel(t *testing.T) {
	service := &SlackService{}

	// In test mode, should return default
	os.Setenv("GO_ENV", "test")
	channel := service.GetRawDataChannel()
	expected := "#raw-data"

	if channel != expected {
		t.Errorf("Expected %s, got %s", expected, channel)
	}
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}
