package service

import (
	"encoding/json"
	"log"
	"yotracker/config"
	"yotracker/internal/models"
)

// CommunicationCampaignLogService handles communication campaign logging
type CommunicationCampaignLogService struct{}

// NewCommunicationCampaignLogService creates a new service instance
func NewCommunicationCampaignLogService() *CommunicationCampaignLogService {
	return &CommunicationCampaignLogService{}
}

// LogCommunication logs a communication attempt
func (s *CommunicationCampaignLogService) LogCommunication(clientId *uint, to, campaignType, message, status string, errorMessage *string, metadata map[string]interface{}) error {
	var metadataStr *string
	if metadata != nil {
		if metadataJSON, err := json.Marshal(metadata); err == nil {
			jsonStr := string(metadataJSON)
			metadataStr = &jsonStr
		}
	}

	logEntry := models.CommunicationCampaignLog{
		ClientId:     clientId,
		To:           to,
		CampaignType: campaignType,
		Message:      message,
		Status:       status,
		ErrorMessage: errorMessage,
		Metadata:     metadataStr,
	}

	if err := config.DB.Create(&logEntry).Error; err != nil {
		log.Printf("Failed to log communication: %v", err)
		return err
	}

	return nil
}

// LogSuccessfulCommunication logs a successful communication
func (s *CommunicationCampaignLogService) LogSuccessfulCommunication(clientId *uint, to, campaignType, message string, metadata map[string]interface{}) error {
	return s.LogCommunication(clientId, to, campaignType, message, models.StatusSent, nil, metadata)
}

// LogFailedCommunication logs a failed communication
func (s *CommunicationCampaignLogService) LogFailedCommunication(clientId *uint, to, campaignType, message, errorMessage string, metadata map[string]interface{}) error {
	return s.LogCommunication(clientId, to, campaignType, message, models.StatusFailed, &errorMessage, metadata)
}

// LogSMS logs an SMS communication
func (s *CommunicationCampaignLogService) LogSMS(clientId *uint, to, message string, success bool, errorMessage string, metadata map[string]interface{}) error {
	if success {
		return s.LogSuccessfulCommunication(clientId, to, models.CampaignTypeSMS, message, metadata)
	}
	return s.LogFailedCommunication(clientId, to, models.CampaignTypeSMS, message, errorMessage, metadata)
}

// LogWhatsApp logs a WhatsApp communication
func (s *CommunicationCampaignLogService) LogWhatsApp(clientId *uint, to, message string, success bool, errorMessage string, metadata map[string]interface{}) error {
	if success {
		return s.LogSuccessfulCommunication(clientId, to, models.CampaignTypeWhatsApp, message, metadata)
	}
	return s.LogFailedCommunication(clientId, to, models.CampaignTypeWhatsApp, message, errorMessage, metadata)
}

// LogEmail logs an email communication
func (s *CommunicationCampaignLogService) LogEmail(clientId *uint, to, message string, success bool, errorMessage string, metadata map[string]interface{}) error {
	if success {
		return s.LogSuccessfulCommunication(clientId, to, models.CampaignTypeEmail, message, metadata)
	}
	return s.LogFailedCommunication(clientId, to, models.CampaignTypeEmail, message, errorMessage, metadata)
}

// GetCommunicationStatistics retrieves communication statistics
func (s *CommunicationCampaignLogService) GetCommunicationStatistics(clientId *uint, startDate, endDate *string) (*models.CommunicationCampaignLogStatistics, error) {
	query := config.DB.Model(&models.CommunicationCampaignLog{})

	if clientId != nil {
		query = query.Where("client_id = ?", *clientId)
	}

	// Apply date filters if provided
	// Note: This is a simplified version - you might want to add proper date parsing

	var total int64
	query.Count(&total)

	var smsCount, whatsappCount, emailCount int64
	query.Where("campaign_type = ?", models.CampaignTypeSMS).Count(&smsCount)
	query.Where("campaign_type = ?", models.CampaignTypeWhatsApp).Count(&whatsappCount)
	query.Where("campaign_type = ?", models.CampaignTypeEmail).Count(&emailCount)

	var successfulCount, failedCount int64
	query.Where("status = ?", models.StatusSent).Count(&successfulCount)
	query.Where("status = ?", models.StatusFailed).Count(&failedCount)

	statistics := &models.CommunicationCampaignLogStatistics{
		TotalMessages:   total,
		SmsCount:        smsCount,
		WhatsappCount:   whatsappCount,
		EmailCount:      emailCount,
		SuccessfulCount: successfulCount,
		FailedCount:     failedCount,
	}

	return statistics, nil
}
