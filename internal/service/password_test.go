package service

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestHashPassword(t *testing.T) {
	password := "password"
	hash := HashPassword(password)
	assert.NotEmpty(t, hash, "Hashed password should not be empty")
}
func TestCheckPassword(t *testing.T) {
	password := "password"
	hash := HashPassword(password)
	assert.True(t, CheckPassword(hash, password), "Password should match")
	assert.False(t, CheckPassword(hash, "wrongpassword"), "Password should not match")
}
