package service

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

type NominatimResponse struct {
	DisplayName string `json:"display_name"`
	Address     struct {
		Road        string `json:"road"`
		Suburb      string `json:"suburb"`
		City        string `json:"city"`
		Town        string `json:"town"`
		Village     string `json:"village"`
		County      string `json:"county"`
		State       string `json:"state"`
		Country     string `json:"country"`
		Postcode    string `json:"postcode"`
		HouseNumber string `json:"house_number"`
	} `json:"address"`
}

// CacheEntry represents a cached location name for a coordinate range
type CacheEntry struct {
	LocationName string    `json:"location_name"`
	Latitude     float64   `json:"latitude"`
	Longitude    float64   `json:"longitude"`
	Radius       float64   `json:"radius"` // in kilometers
	CreatedAt    time.Time `json:"created_at"`
}

// ReverseGeocodingService handles reverse geocoding with caching
type ReverseGeocodingService struct {
	client    *http.Client
	cache     map[string]*CacheEntry
	mutex     sync.RWMutex
	batchSize int // Configurable batch size for processing
}

func NewReverseGeocodingService() *ReverseGeocodingService {
	return &ReverseGeocodingService{
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
		cache:     make(map[string]*CacheEntry),
		batchSize: 40, // Smaller batch size for minute-by-minute processing
	}
}

// NewReverseGeocodingServiceWithBatchSize creates a service with custom batch size
func NewReverseGeocodingServiceWithBatchSize(batchSize int) *ReverseGeocodingService {
	return &ReverseGeocodingService{
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
		cache:     make(map[string]*CacheEntry),
		batchSize: batchSize,
	}
}

// generateCacheKey creates a cache key for coordinates
func (s *ReverseGeocodingService) generateCacheKey(lat, lon float64) string {
	// Round coordinates to 4 decimal places (~11 meters precision)
	// This groups nearby coordinates together
	latRounded := float64(int(lat*10000)) / 10000
	lonRounded := float64(int(lon*10000)) / 10000
	return fmt.Sprintf("%.4f,%.4f", latRounded, lonRounded)
}

// isWithinCacheRadius checks if coordinates are within the cached radius
func (s *ReverseGeocodingService) isWithinCacheRadius(lat1, lon1, lat2, lon2 float64, radiusKm float64) bool {
	// Simple distance calculation (approximate)
	latDiff := lat1 - lat2
	lonDiff := lon1 - lon2
	distance := (latDiff*latDiff + lonDiff*lonDiff) * 111 * 111 // rough km conversion
	return distance <= radiusKm*radiusKm
}

// getFromCache retrieves location name from cache if available
func (s *ReverseGeocodingService) getFromCache(latitude, longitude float64) (string, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// Check each cache entry to see if coordinates fall within cached radius
	for _, entry := range s.cache {
		if s.isWithinCacheRadius(latitude, longitude, entry.Latitude, entry.Longitude, entry.Radius) {
			// Check if cache entry is not too old (7 days)
			if time.Since(entry.CreatedAt) < 7*24*time.Hour {
				return entry.LocationName, true
			}
		}
	}
	return "", false
}

// addToCache adds a location name to the cache
func (s *ReverseGeocodingService) addToCache(latitude, longitude float64, locationName string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Determine cache radius based on location type
	radius := 0.1 // Default 100 meters
	if strings.Contains(locationName, "City") || strings.Contains(locationName, "Town") {
		radius = 1.0 // 1 km for cities/towns
	} else if strings.Contains(locationName, "Province") || strings.Contains(locationName, "State") {
		radius = 5.0 // 5 km for provinces/states
	}

	cacheKey := s.generateCacheKey(latitude, longitude)
	s.cache[cacheKey] = &CacheEntry{
		LocationName: locationName,
		Latitude:     latitude,
		Longitude:    longitude,
		Radius:       radius,
		CreatedAt:    time.Now(),
	}

	// Clean up old cache entries (older than 30 days)
	s.cleanupCache()
}

// cleanupCache removes old cache entries
func (s *ReverseGeocodingService) cleanupCache() {
	cutoff := time.Now().Add(-30 * 24 * time.Hour)
	for key, entry := range s.cache {
		if entry.CreatedAt.Before(cutoff) {
			delete(s.cache, key)
		}
	}
}

// GetLocationName retrieves the location name for given coordinates using cache and Nominatim API
func (s *ReverseGeocodingService) GetLocationName(latitude, longitude float64) (string, error) {
	// First, try to get from cache
	if locationName, found := s.getFromCache(latitude, longitude); found {
		return locationName, nil
	}

	// If not in cache, make API request
	locationName, err := s.fetchFromAPI(latitude, longitude)
	if err != nil {
		return "", err
	}

	// Add to cache for future use
	s.addToCache(latitude, longitude, locationName)

	return locationName, nil
}

// fetchFromAPI makes the actual API request to Nominatim
func (s *ReverseGeocodingService) fetchFromAPI(latitude, longitude float64) (string, error) {
	// Build the Nominatim API URL
	baseURL := "https://nominatim.openstreetmap.org/reverse"
	params := url.Values{}
	params.Add("lat", fmt.Sprintf("%.6f", latitude))
	params.Add("lon", fmt.Sprintf("%.6f", longitude))
	params.Add("format", "json")
	params.Add("addressdetails", "1")
	params.Add("accept-language", "en")

	reqURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// Create HTTP request
	req, err := http.NewRequest("GET", reqURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Set required headers for Nominatim API
	req.Header.Set("User-Agent", "YoTracker/1.0")
	req.Header.Set("Accept", "application/json")

	// Make the request
	resp, err := s.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("nominatim API returned status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var nominatimResp NominatimResponse
	if err := json.NewDecoder(resp.Body).Decode(&nominatimResp); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	// Build a meaningful location name
	locationParts := []string{}

	// Add road if available
	if nominatimResp.Address.Road != "" {
		locationParts = append(locationParts, nominatimResp.Address.Road)
	}

	// Add suburb/town/city/village
	if nominatimResp.Address.Suburb != "" {
		locationParts = append(locationParts, nominatimResp.Address.Suburb)
	} else if nominatimResp.Address.Town != "" {
		locationParts = append(locationParts, nominatimResp.Address.Town)
	} else if nominatimResp.Address.City != "" {
		locationParts = append(locationParts, nominatimResp.Address.City)
	} else if nominatimResp.Address.Village != "" {
		locationParts = append(locationParts, nominatimResp.Address.Village)
	}

	// Add county/state
	if nominatimResp.Address.County != "" {
		locationParts = append(locationParts, nominatimResp.Address.County)
	} else if nominatimResp.Address.State != "" {
		locationParts = append(locationParts, nominatimResp.Address.State)
	}

	// Add country
	if nominatimResp.Address.Country != "" {
		locationParts = append(locationParts, nominatimResp.Address.Country)
	}

	// If we have parts, join them, otherwise use the full display name
	if len(locationParts) > 0 {
		return strings.Join(locationParts, ", "), nil
	}

	return nominatimResp.DisplayName, nil
}

// UpdateGPSDataLocationNames updates location names for GPS data records that don't have them
func (s *ReverseGeocodingService) UpdateGPSDataLocationNames() error {
	// Get GPS data records with empty location_name, ordered by created_at desc
	var gpsDataRecords []models.GPSData
	if err := config.DB.Where("location_name IS NULL OR location_name = ''").
		Order("created_at DESC").
		Limit(s.batchSize). // Use configurable batch size
		Find(&gpsDataRecords).Error; err != nil {
		return fmt.Errorf("failed to fetch GPS data: %w", err)
	}

	if len(gpsDataRecords) == 0 {
		fmt.Println("No GPS data records found without location names")
		return nil
	}

	fmt.Printf("Found %d GPS data records without location names\n", len(gpsDataRecords))

	// Track cache hits vs API calls
	cacheHits := 0
	apiCalls := 0

	// Process each record
	for i, record := range gpsDataRecords {
		fmt.Printf("Processing record %d/%d (ID: %d, Lat: %.6f, Lon: %.6f)\n",
			i+1, len(gpsDataRecords), record.Id, record.Latitude, record.Longitude)

		// Check cache first
		if locationName, found := s.getFromCache(record.Latitude, record.Longitude); found {
			// Update the record in the database
			if err := config.DB.Model(&models.GPSData{}).
				Where("id = ?", record.Id).
				Update("location_name", locationName).Error; err != nil {
				fmt.Printf("Failed to update location name for record %d: %v\n", record.Id, err)
				continue
			}
			fmt.Printf("Updated record %d with cached location: %s (CACHE HIT)\n", record.Id, locationName)
			cacheHits++
			continue
		}

		// Get location name from Nominatim API
		locationName, err := s.GetLocationName(record.Latitude, record.Longitude)
		if err != nil {
			fmt.Printf("Failed to get location name for record %d: %v\n", record.Id, err)
			continue
		}

		// Update the record in the database
		if err := config.DB.Model(&models.GPSData{}).
			Where("id = ?", record.Id).
			Update("location_name", locationName).Error; err != nil {
			fmt.Printf("Failed to update location name for record %d: %v\n", record.Id, err)
			continue
		}

		fmt.Printf("Updated record %d with location: %s (API CALL)\n", record.Id, locationName)
		apiCalls++

		// Sleep for 1 second to respect the API rate limit (only for API calls)
		if i < len(gpsDataRecords)-1 { // Don't sleep after the last record
			time.Sleep(1 * time.Second)
		}
	}

	fmt.Printf("Completed processing %d GPS data records\n", len(gpsDataRecords))
	fmt.Printf("Cache hits: %d, API calls: %d\n", cacheHits, apiCalls)
	return nil
}

// GetCacheStats returns cache statistics
func (s *ReverseGeocodingService) GetCacheStats() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_entries": len(s.cache),
		"cache_size":    len(s.cache),
	}

	// Calculate cache age statistics
	var totalAge time.Duration
	oldestEntry := time.Now()
	newestEntry := time.Time{}

	for _, entry := range s.cache {
		age := time.Since(entry.CreatedAt)
		totalAge += age

		if entry.CreatedAt.Before(oldestEntry) {
			oldestEntry = entry.CreatedAt
		}
		if entry.CreatedAt.After(newestEntry) {
			newestEntry = entry.CreatedAt
		}
	}

	if len(s.cache) > 0 {
		stats["average_age_hours"] = totalAge.Hours() / float64(len(s.cache))
		stats["oldest_entry_hours"] = time.Since(oldestEntry).Hours()
		stats["newest_entry_hours"] = time.Since(newestEntry).Hours()
	}

	return stats
}

// ClearCache clears all cached entries
func (s *ReverseGeocodingService) ClearCache() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.cache = make(map[string]*CacheEntry)
}
