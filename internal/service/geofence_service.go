package service

import (
	"encoding/json"
	"fmt"
	"math"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

// GeofenceState tracks the current state of a device relative to geofences
type GeofenceState struct {
	DeviceId        string
	ClientDeviceId  uint
	InsideGeofences map[uint]bool // geofence_id -> inside status
	LastChecked     time.Time
}

// Global cache for geofence states to prevent duplicate alerts
var geofenceStateCache = make(map[uint]*GeofenceState) // client_device_id -> state

// CheckGeofences checks if a GPS location triggers any geofence events
func CheckGeofences(clientDeviceId uint, deviceId string, latitude, longitude float64, timestamp time.Time) {
	// Get client device info
	var clientDevice models.ClientDevice
	if err := config.DB.Preload("Fleet").First(&clientDevice, clientDeviceId).Error; err != nil {
		return
	}

	// Get all applicable geofences for this device
	geofences := getApplicableGeofences(clientDevice)
	if len(geofences) == 0 {
		return
	}

	// Get or create geofence state for this device
	state, exists := geofenceStateCache[clientDeviceId]
	if !exists {
		state = &GeofenceState{
			DeviceId:        deviceId,
			ClientDeviceId:  clientDeviceId,
			InsideGeofences: make(map[uint]bool),
			LastChecked:     timestamp,
		}
		geofenceStateCache[clientDeviceId] = state
	}

	// Check each geofence
	for _, geofence := range geofences {
		isInside := isPointInsideGeofence(latitude, longitude, geofence)
		wasInside, existed := state.InsideGeofences[geofence.Id]

		// Detect entry event
		if isInside && (!existed || !wasInside) {
			if shouldTriggerEvent(geofence.TriggerEvents, "entry") {
				createGeofenceAlert(clientDeviceId, deviceId, geofence, "entry", latitude, longitude, timestamp)
				createGeofenceEvent(clientDeviceId, geofence.Id, "entry", latitude, longitude, timestamp)
			}
		}

		// Detect exit event
		if !isInside && existed && wasInside {
			if shouldTriggerEvent(geofence.TriggerEvents, "exit") {
				createGeofenceAlert(clientDeviceId, deviceId, geofence, "exit", latitude, longitude, timestamp)
				createGeofenceEvent(clientDeviceId, geofence.Id, "exit", latitude, longitude, timestamp)
			}
		}
		// Update state
		state.InsideGeofences[geofence.Id] = isInside
	}

	state.LastChecked = timestamp
}

// createGeofenceAlert creates an alert for geofence entry/exit
func createGeofenceAlert(clientDeviceId uint, deviceId string, geofence models.Geofence, eventType string, latitude, longitude float64, timestamp time.Time) {
	// Check if the device has geofence events enabled
	var clientDevice models.ClientDevice
	if err := config.DB.First(&clientDevice, clientDeviceId).Error; err != nil {
		return
	}

	// Check event type preferences
	if eventType == "entry" && !clientDevice.GeofenceEntryEvents {
		return
	}
	if eventType == "exit" && !clientDevice.GeofenceExitEvents {
		return
	}

	// Create device display name
	deviceDisplayName := deviceId
	if clientDevice.Name != nil {
		deviceDisplayName = *clientDevice.Name
		if clientDevice.PlateNumber != nil && *clientDevice.PlateNumber != "" {
			deviceDisplayName = fmt.Sprintf("%s (%s)", *clientDevice.Name, *clientDevice.PlateNumber)
		}
	}

	// Create alert message
	var message string
	if eventType == "entry" {
		message = fmt.Sprintf("Device %s entered geofence '%s'", deviceDisplayName, geofence.Name)
	} else {
		message = fmt.Sprintf("Device %s exited geofence '%s'", deviceDisplayName, geofence.Name)
	}

	// Create additional data with geofence info
	additionalData := map[string]interface{}{
		"geofence_id":   geofence.Id,
		"geofence_name": geofence.Name,
		"geofence_type": geofence.GeofenceType,
		"event_type":    eventType,
		"latitude":      latitude,
		"longitude":     longitude,
	}

	// Add type-specific data
	if geofence.GeofenceType == "circle" {
		additionalData["radius"] = geofence.Radius
		additionalData["geofence_lat"] = geofence.Latitude
		additionalData["geofence_lng"] = geofence.Longitude
	} else if geofence.Coordinates != nil {
		additionalData["coordinates"] = *geofence.Coordinates
	}

	additionalDataJSON, _ := json.Marshal(additionalData)

	// Use the alert data service to save and send alerts
	alertData := Alert{
		DeviceId:       deviceId,
		AlertType:      "geofence",
		AlertName:      geofence.Name,
		AlertLevel:     "info",
		Message:        message,
		AdditionalData: additionalDataJSON,
		Timestamp:      timestamp,
	}

	// Save alert and send notifications through all channels
	alertData.SaveAlert()
}

// createGeofenceEvent creates a geofence event record
func createGeofenceEvent(clientDeviceId, geofenceId uint, eventType string, latitude, longitude float64, timestamp time.Time) {
	event := models.GeofenceEvent{
		ClientDeviceId: clientDeviceId,
		GeofenceId:     geofenceId,
		EventType:      eventType,
		EventTimestamp: timestamp,
		Latitude:       latitude,
		Longitude:      longitude,
	}

	config.DB.Create(&event)
}

// ClearGeofenceStateCache clears the cache for a specific device (useful when device is deleted)
func ClearGeofenceStateCache(clientDeviceId uint) {
	delete(geofenceStateCache, clientDeviceId)
}

// GetGeofencesByDevice returns all geofences for a specific device
func GetGeofencesByDevice(clientDeviceId uint) ([]models.Geofence, error) {
	var clientDevice models.ClientDevice
	if err := config.DB.Preload("Fleet").First(&clientDevice, clientDeviceId).Error; err != nil {
		return nil, err
	}

	geofences := getApplicableGeofences(clientDevice)
	return geofences, nil
}

// ValidateGeofenceRadius ensures the radius is within acceptable limits
func ValidateGeofenceRadius(radius float64) error {
	if radius < 10 {
		return fmt.Errorf("geofence radius must be at least 10 meters")
	}
	if radius > 50000 {
		return fmt.Errorf("geofence radius cannot exceed 50 kilometers")
	}
	return nil
}

// ValidateGeofenceCoordinates ensures coordinates are valid
func ValidateGeofenceCoordinates(latitude, longitude float64) error {
	if latitude < -90 || latitude > 90 {
		return fmt.Errorf("latitude must be between -90 and 90 degrees")
	}
	if longitude < -180 || longitude > 180 {
		return fmt.Errorf("longitude must be between -180 and 180 degrees")
	}
	return nil
}

// isPointInsideGeofence checks if a point is inside a geofence based on its type
func isPointInsideGeofence(latitude, longitude float64, geofence models.Geofence) bool {
	switch geofence.GeofenceType {
	case "circle":
		return isPointInsideCircle(latitude, longitude, geofence)
	case "polygon":
		return isPointInsidePolygon(latitude, longitude, geofence)
	case "rectangle":
		return isPointInsideRectangle(latitude, longitude, geofence)
	default:
		// Default to circle for backward compatibility
		return isPointInsideCircle(latitude, longitude, geofence)
	}
}

// isPointInsideCircle checks if a point is inside a circular geofence
func isPointInsideCircle(latitude, longitude float64, geofence models.Geofence) bool {
	if geofence.Latitude == nil || geofence.Longitude == nil || geofence.Radius == nil {
		return false
	}

	distance := utils.HaversineDistance(latitude, longitude, *geofence.Latitude, *geofence.Longitude)
	distanceInMeters := distance * 1000 // Convert km to meters

	return distanceInMeters <= *geofence.Radius
}

// getApplicableGeofences returns all geofences that apply to a specific device
func getApplicableGeofences(clientDevice models.ClientDevice) []models.Geofence {
	var geofences []models.Geofence

	// Get device-specific geofences
	config.DB.Where("client_id = ? AND applies_to = 'device' AND client_device_id = ? AND status = 'active'",
		clientDevice.ClientId, clientDevice.Id).Find(&geofences)

	// Get fleet-specific geofences if device belongs to a fleet
	if clientDevice.FleetId != nil {
		var fleetGeofences []models.Geofence
		config.DB.Where("client_id = ? AND applies_to = 'fleet' AND fleet_id = ? AND status = 'active'",
			clientDevice.ClientId, *clientDevice.FleetId).Find(&fleetGeofences)
		geofences = append(geofences, fleetGeofences...)
	}

	// Get client-level geofences (apply to all devices)
	var clientGeofences []models.Geofence
	config.DB.Where("client_id = ? AND applies_to = 'client' AND status = 'active'",
		clientDevice.ClientId).Find(&clientGeofences)
	geofences = append(geofences, clientGeofences...)

	return geofences
}

// shouldTriggerEvent checks if a geofence should trigger for a specific event type
func shouldTriggerEvent(triggerEvents, eventType string) bool {
	// Default to "both" if not specified
	if triggerEvents == "" {
		triggerEvents = "both"
	}

	switch triggerEvents {
	case "entry":
		return eventType == "entry"
	case "exit":
		return eventType == "exit"
	case "both":
		return true
	default:
		return true // Default to both for unknown values
	}
}

// Point represents a coordinate point
type Point struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

// isPointInsidePolygon checks if a point is inside a polygon using ray casting algorithm
func isPointInsidePolygon(latitude, longitude float64, geofence models.Geofence) bool {
	if geofence.Coordinates == nil {
		return false
	}

	var points []Point
	if err := json.Unmarshal([]byte(*geofence.Coordinates), &points); err != nil {
		return false
	}
	if len(points) < 3 {
		return false // A polygon needs at least 3 points
	}

	// Ray casting algorithm
	inside := false
	j := len(points) - 1

	for i := 0; i < len(points); i++ {
		if ((points[i].Lat > latitude) != (points[j].Lat > latitude)) &&
			(longitude < (points[j].Lng-points[i].Lng)*(latitude-points[i].Lat)/(points[j].Lat-points[i].Lat)+points[i].Lng) {
			inside = !inside
		}
		j = i
	}

	return inside
}

// isPointInsideRectangle checks if a point is inside a rectangle
func isPointInsideRectangle(latitude, longitude float64, geofence models.Geofence) bool {
	if geofence.Coordinates == nil {
		return false
	}

	var points []Point
	if err := json.Unmarshal([]byte(*geofence.Coordinates), &points); err != nil {
		return false
	}

	if len(points) != 4 {
		return false // Rectangle needs exactly 4 points
	}

	// Find min/max bounds
	minLat := math.Inf(1)
	maxLat := math.Inf(-1)
	minLng := math.Inf(1)
	maxLng := math.Inf(-1)
	for _, point := range points {
		if point.Lat < minLat {
			minLat = point.Lat
		}
		if point.Lat > maxLat {
			maxLat = point.Lat
		}
		if point.Lng < minLng {
			minLng = point.Lng
		}
		if point.Lng > maxLng {
			maxLng = point.Lng
		}
	}

	// Check if point is within bounds
	return latitude >= minLat && latitude <= maxLat && longitude >= minLng && longitude <= maxLng
}

// ValidateGeofenceByType validates a geofence based on its type and scope
func ValidateGeofenceByType(geofence models.CreateGeofenceRequest) error {
	// Validate scope first
	if err := validateGeofenceScope(geofence); err != nil {
		return err
	}

	// Default to circle if no type specified
	geofenceType := geofence.GeofenceType
	if geofenceType == "" {
		geofenceType = "circle"
	}

	switch geofenceType {
	case "circle":
		return validateCircleGeofence(geofence)
	case "polygon":
		return validatePolygonGeofence(geofence)
	case "rectangle":
		return validateRectangleGeofence(geofence)
	default:
		return fmt.Errorf("unsupported geofence type: %s", geofenceType)
	}
}

// validateGeofenceScope validates the scope (applies_to) and related fields
func validateGeofenceScope(geofence models.CreateGeofenceRequest) error {
	appliesTo := geofence.AppliesTo
	if appliesTo == "" {
		appliesTo = "client" // default
	}

	switch appliesTo {
	case "client":
		// Client-level geofences don't need additional fields
		break
	case "fleet":
		if geofence.FleetId == nil {
			return fmt.Errorf("fleet_id is required when applies_to is 'fleet'")
		}
	case "device":
		if geofence.ClientDeviceId == nil {
			return fmt.Errorf("client_device_id is required when applies_to is 'device'")
		}
	default:
		return fmt.Errorf("invalid applies_to value: %s. Must be 'client', 'fleet', or 'device'", appliesTo)
	}

	// Validate trigger events
	triggerEvents := geofence.TriggerEvents
	if triggerEvents == "" {
		triggerEvents = "both" // default
	}

	switch triggerEvents {
	case "entry", "exit", "both":
		// Valid values
		return nil
	default:
		return fmt.Errorf("invalid trigger_events value: %s. Must be 'entry', 'exit', or 'both'", triggerEvents)
	}
}

// validateCircleGeofence validates circle-specific fields
func validateCircleGeofence(geofence models.CreateGeofenceRequest) error {
	if geofence.Latitude == nil {
		return fmt.Errorf("latitude is required for circle geofences")
	}
	if geofence.Longitude == nil {
		return fmt.Errorf("longitude is required for circle geofences")
	}
	if geofence.Radius == nil {
		return fmt.Errorf("radius is required for circle geofences")
	}

	// Validate coordinates
	if err := ValidateGeofenceCoordinates(*geofence.Latitude, *geofence.Longitude); err != nil {
		return err
	}

	// Validate radius
	if err := ValidateGeofenceRadius(*geofence.Radius); err != nil {
		return err
	}

	return nil
}

// validatePolygonGeofence validates polygon-specific fields
func validatePolygonGeofence(geofence models.CreateGeofenceRequest) error {
	if geofence.Coordinates == nil {
		return fmt.Errorf("coordinates are required for polygon geofences")
	}

	var points []Point
	if err := json.Unmarshal([]byte(*geofence.Coordinates), &points); err != nil {
		return fmt.Errorf("invalid coordinates format for polygon geofence: %v", err)
	}

	if len(points) < 3 {
		return fmt.Errorf("polygon geofences require at least 3 coordinate points")
	}

	// Validate each coordinate point
	for i, point := range points {
		if err := ValidateGeofenceCoordinates(point.Lat, point.Lng); err != nil {
			return fmt.Errorf("invalid coordinate at index %d: %v", i, err)
		}
	}

	return nil
}

// validateRectangleGeofence validates rectangle-specific fields
func validateRectangleGeofence(geofence models.CreateGeofenceRequest) error {
	if geofence.Coordinates == nil {
		return fmt.Errorf("coordinates are required for rectangle geofences")
	}

	var points []Point
	if err := json.Unmarshal([]byte(*geofence.Coordinates), &points); err != nil {
		return fmt.Errorf("invalid coordinates format for rectangle geofence: %v", err)
	}

	if len(points) != 4 {
		return fmt.Errorf("rectangle geofences require exactly 4 coordinate points")
	}

	// Validate each coordinate point
	for i, point := range points {
		if err := ValidateGeofenceCoordinates(point.Lat, point.Lng); err != nil {
			return fmt.Errorf("invalid coordinate at index %d: %v", i, err)
		}
	}

	return nil
}

// ValidateGeofenceUpdateByType validates a geofence update based on its type
func ValidateGeofenceUpdateByType(existing models.Geofence, update models.UpdateGeofenceRequest) error {
	geofenceType := existing.GeofenceType
	if update.GeofenceType != "" {
		geofenceType = update.GeofenceType
	}

	switch geofenceType {
	case "circle":
		return validateCircleGeofenceUpdate(existing, update)
	case "polygon":
		return validatePolygonGeofenceUpdate(existing, update)
	case "rectangle":
		return validateRectangleGeofenceUpdate(existing, update)
	default:
		return fmt.Errorf("unsupported geofence type: %s", geofenceType)
	}
}

// validateCircleGeofenceUpdate validates circle-specific update fields
func validateCircleGeofenceUpdate(existing models.Geofence, update models.UpdateGeofenceRequest) error {
	// If coordinates are being updated, validate them
	if update.Latitude != nil && update.Longitude != nil {
		if err := ValidateGeofenceCoordinates(*update.Latitude, *update.Longitude); err != nil {
			return err
		}
	}

	// If radius is being updated, validate it
	if update.Radius != nil {
		if err := ValidateGeofenceRadius(*update.Radius); err != nil {
			return err
		}
	}

	return nil
}

// validatePolygonGeofenceUpdate validates polygon-specific update fields
func validatePolygonGeofenceUpdate(existing models.Geofence, update models.UpdateGeofenceRequest) error {
	// If coordinates are being updated, validate them
	if update.Coordinates != nil {
		var points []Point
		if err := json.Unmarshal([]byte(*update.Coordinates), &points); err != nil {
			return fmt.Errorf("invalid coordinates format for polygon geofence: %v", err)
		}

		if len(points) < 3 {
			return fmt.Errorf("polygon geofences require at least 3 coordinate points")
		}

		// Validate each coordinate point
		for i, point := range points {
			if err := ValidateGeofenceCoordinates(point.Lat, point.Lng); err != nil {
				return fmt.Errorf("invalid coordinate at index %d: %v", i, err)
			}
		}
	}

	return nil
}

// validateRectangleGeofenceUpdate validates rectangle-specific update fields
func validateRectangleGeofenceUpdate(existing models.Geofence, update models.UpdateGeofenceRequest) error {
	// If coordinates are being updated, validate them
	if update.Coordinates != nil {
		var points []Point
		if err := json.Unmarshal([]byte(*update.Coordinates), &points); err != nil {
			return fmt.Errorf("invalid coordinates format for rectangle geofence: %v", err)
		}

		if len(points) != 4 {
			return fmt.Errorf("rectangle geofences require exactly 4 coordinate points")
		}

		// Validate each coordinate point
		for i, point := range points {
			if err := ValidateGeofenceCoordinates(point.Lat, point.Lng); err != nil {
				return fmt.Errorf("invalid coordinate at index %d: %v", i, err)
			}
		}
	}

	return nil
}
