package templates

import (
	"bytes"
	"testing"
)

func TestGetInvoiceTemplate(t *testing.T) {
	// Test getting the invoice template
	tmpl, err := GetInvoiceTemplate()
	if err != nil {
		t.Fatalf("Failed to get invoice template: %v", err)
	}

	if tmpl == nil {
		t.Fatal("Template is nil")
	}

	// Test template execution with sample data
	data := struct {
		Reference string
		BillFrom  struct {
			Name    string
			Address string
			Email   string
		}
		BillTo struct {
			Name    string
			Address string
			City    string
			State   string
			Country string
			Email   string
		}
		Date         string
		DueDate      string
		InvoiceItems []struct {
			Description string
			Qty         int
			UnitPrice   float64
			TaxAmount   float64
			Total       float64
		}
		Subtotal  float64
		TaxAmount float64
		Total     float64
		Terms     string
		Logo      string
	}{
		Reference: "INV-001",
		Date:      "2025-01-01",
		DueDate:   "2025-01-31",
		Subtotal:  100.00,
		TaxAmount: 15.00,
		Total:     115.00,
		Terms:     "Payment due within 30 days",
	}

	data.BillFrom.Name = "Test Company"
	data.BillFrom.Address = "123 Test St"
	data.BillFrom.Email = "<EMAIL>"

	data.BillTo.Name = "Test Client"
	data.BillTo.Address = "456 Client Ave"
	data.BillTo.City = "Test City"
	data.BillTo.State = "Test State"
	data.BillTo.Country = "Test Country"
	data.BillTo.Email = "<EMAIL>"

	data.InvoiceItems = []struct {
		Description string
		Qty         int
		UnitPrice   float64
		TaxAmount   float64
		Total       float64
	}{
		{
			Description: "Test Service",
			Qty:         1,
			UnitPrice:   100.00,
			TaxAmount:   15.00,
			Total:       115.00,
		},
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil {
		t.Fatalf("Failed to execute template: %v", err)
	}

	output := buf.String()
	if len(output) == 0 {
		t.Fatal("Template output is empty")
	}

	// Check that the template contains expected content
	expectedStrings := []string{
		"INV-001",
		"Test Company",
		"Test Client",
		"Test Service",
		"Payment due within 30 days",
	}

	for _, expected := range expectedStrings {
		if !bytes.Contains(buf.Bytes(), []byte(expected)) {
			t.Errorf("Template output does not contain expected string: %s", expected)
		}
	}
}

func TestTemplateManager(t *testing.T) {
	// Test creating a new template manager
	tm, err := NewTemplateManager()
	if err != nil {
		t.Fatalf("Failed to create template manager: %v", err)
	}

	// Test getting a template that exists
	tmpl, err := tm.GetTemplate("invoice.html")
	if err != nil {
		t.Fatalf("Failed to get existing template: %v", err)
	}

	if tmpl == nil {
		t.Fatal("Template is nil")
	}

	// Test getting a template that doesn't exist
	_, err = tm.GetTemplate("nonexistent.html")
	if err == nil {
		t.Fatal("Expected error for nonexistent template, but got nil")
	}
}
