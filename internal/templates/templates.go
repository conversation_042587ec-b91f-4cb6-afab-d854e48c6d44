package templates

import (
	"embed"
	"errors"
	"html/template"
	"io/fs"
)

//go:embed *.html
var templateFS embed.FS

// TemplateManager manages embedded templates
type TemplateManager struct {
	templates map[string]*template.Template
}

// NewTemplateManager creates a new template manager with embedded templates
func NewTemplateManager() (*TemplateManager, error) {
	tm := &TemplateManager{
		templates: make(map[string]*template.Template),
	}

	// Load all embedded templates
	err := tm.loadTemplates()
	if err != nil {
		return nil, err
	}

	return tm, nil
}

// loadTemplates loads all embedded template files
func (tm *TemplateManager) loadTemplates() error {
	// Walk through embedded files
	return fs.WalkDir(templateFS, ".", func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// Skip directories and non-HTML files
		if d.IsDir() || !isHTMLFile(path) {
			return nil
		}

		// Read template content
		content, err := templateFS.ReadFile(path)
		if err != nil {
			return err
		}

		// Parse template
		tmpl, err := template.New(path).Parse(string(content))
		if err != nil {
			return err
		}

		// Store template
		tm.templates[path] = tmpl
		return nil
	})
}

// GetTemplate returns a template by name
func (tm *TemplateManager) GetTemplate(name string) (*template.Template, error) {
	tmpl, exists := tm.templates[name]
	if !exists {
		return nil, errors.New("template not found: " + name)
	}
	return tmpl, nil
}

// isHTMLFile checks if the file has .html extension
func isHTMLFile(filename string) bool {
	return len(filename) > 5 && filename[len(filename)-5:] == ".html"
}

// Global template manager instance
var globalTemplateManager *TemplateManager

// InitTemplates initializes the global template manager
func InitTemplates() error {
	var err error
	globalTemplateManager, err = NewTemplateManager()
	return err
}

// GetInvoiceTemplate returns the invoice template
func GetInvoiceTemplate() (*template.Template, error) {
	if globalTemplateManager == nil {
		if err := InitTemplates(); err != nil {
			return nil, err
		}
	}
	return globalTemplateManager.GetTemplate("invoice.html")
}
