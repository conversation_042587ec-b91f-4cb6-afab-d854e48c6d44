package response

type PaymentTypeResponse struct {
	Id          uint    `json:"id"`
	Name        string  `json:"name"`
	SystemName  *string `json:"system_name"`
	Description *string `json:"description" gorm:"type:text"`
	IsCash      *bool   `json:"is_cash" gorm:"default:false"`
	IsOnline    *bool   `json:"is_online" gorm:"default:false"`
	IsSystem    *bool   `json:"is_system" gorm:"default:false"`
	IsEft       *bool   `json:"is_eft" gorm:"default:false"`
	Active      bool    `json:"active" gorm:"default:true"`
	Position    *uint   `json:"position"`
	UniqueId    *string `json:"unique_id"`
	Logo        *string `json:"logo"`
	ReportColor *string `json:"report_color"`
}
