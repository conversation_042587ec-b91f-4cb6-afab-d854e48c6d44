package services

import (
	"fmt"
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/seed"
	"yotracker/internal/utils"
)

// SetupTestEnvironment sets up the complete test environment with seeded data
func SetupTestEnvironment() {
	// Force project root for proper file paths
	utils.ForceProjectRoot()

	// Clean up and seed all test data using the existing seeder
	CleanupTestData()

	// Try to seed data, but don't fail if seeder has issues
	// The getter functions will create fallback data if needed
	defer func() {
		if r := recover(); r != nil {
			// Seeder failed, but we'll continue with fallback data
			log.Printf("See<PERSON> failed with panic: %v", r)
		}
	}()

	// Disable foreign key checks during seeding to avoid constraint issues
	config.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	defer config.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

	seed.Seed() // This seeds all settings, currencies, payment types, tax rates, etc.
}

// CleanupTestData cleans up test data in proper order to avoid foreign key constraint errors
func CleanupTestData() {
	// Disable foreign key checks during cleanup to avoid constraint issues
	config.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	defer config.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

	// Delete in proper order (child tables first, then parent tables)
	// Start with the most dependent tables
	config.DB.Exec("DELETE FROM alerts")
	config.DB.Exec("DELETE FROM invoice_payments")
	config.DB.Exec("DELETE FROM invoice_items")
	config.DB.Exec("DELETE FROM invoices")
	config.DB.Exec("DELETE FROM driver_device_assignments")
	config.DB.Exec("DELETE FROM drivers")
	config.DB.Exec("DELETE FROM client_devices")
	config.DB.Exec("DELETE FROM client_roles")
	config.DB.Exec("DELETE FROM users WHERE user_type != 'backend'") // Keep backend users for auth tests
	config.DB.Exec("DELETE FROM clients")

	// Note: Don't delete seeded data (currencies, payment_types, tax_rates, settings, etc.)
	// as they are needed by the services and will be recreated by seed.Seed()
}

// GetSeededCurrency returns the first seeded currency (USD)
func GetSeededCurrency() models.Currency {
	var currency models.Currency
	err := config.DB.Where("code = ?", "USD").First(&currency).Error
	if err != nil {
		// Fallback: create a test currency if none exists
		currency = models.Currency{
			Name:   "US Dollar",
			Symbol: "$",
			Code:   "USD",
		}
		config.DB.Create(&currency)
	}
	return currency
}

// GetSeededPaymentType returns the first active seeded payment type
func GetSeededPaymentType() models.PaymentType {
	var paymentType models.PaymentType
	err := config.DB.Where("active = ?", true).First(&paymentType).Error
	if err != nil {
		// Fallback: create a test payment type if none exists
		paymentType = models.PaymentType{
			Name:   "Test Payment",
			Active: true,
		}
		config.DB.Create(&paymentType)
	}
	return paymentType
}

// GetSeededTaxRate returns the first active seeded tax rate
func GetSeededTaxRate() models.TaxRate {
	var taxRate models.TaxRate
	err := config.DB.Where("active = ?", true).First(&taxRate).Error
	if err != nil {
		// Fallback: create a test tax rate if none exists
		amountType := "percentage"
		taxRate = models.TaxRate{
			Name:       "Test VAT",
			AmountType: &amountType,
			Amount:     15.0,
			Active:     true,
		}
		config.DB.Create(&taxRate)
	}
	return taxRate
}

// CreateTestClient creates a test client and returns it
func CreateTestClient() models.Client {
	// Use timestamp to ensure unique email
	timestamp := time.Now().UnixNano()
	client := models.Client{
		Name:         "Test Client",
		Email:        fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber:  "1234567890",
		ClientType:   "individual",
		Status:       "active",
		BillingCycle: func() *string { s := "monthly"; return &s }(),
		BillingDay:   func() *uint { d := uint(15); return &d }(),
	}
	config.DB.Create(&client)
	return client
}

// CreateTestUser creates a test user and returns it
func CreateTestUser(userType string) models.User {
	// Use timestamp to ensure unique email
	timestamp := time.Now().UnixNano()
	hashedPassword := "hashed_password_for_testing"
	user := models.User{
		Name:     "Test User",
		Email:    fmt.Sprintf("<EMAIL>", timestamp),
		Password: hashedPassword,
		UserType: userType,
	}
	config.DB.Create(&user)
	return user
}
