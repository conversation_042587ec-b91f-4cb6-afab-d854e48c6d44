package services

import (
	"os"
	"testing"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func TestClientSuspensionService(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.<PERSON>env("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}

	// Initialize test database
	config.InitTestDB()
	migrations.Migrate()

	// Setup test environment with seeded data
	SetupTestEnvironment()

	// Create test client
	client := CreateTestClient()

	// Get seeded currency and payment type
	currency := GetSeededCurrency()
	paymentType := GetSeededPaymentType()

	// Create overdue subscription invoice (due 15 days ago, beyond the 10-day threshold)
	overdueDate := time.Now().AddDate(0, 0, -15)
	invoice := models.Invoice{
		ClientId:       client.Id,
		CurrencyId:     &currency.Id,
		PaymentTypeId:  &paymentType.Id,
		Date:           &overdueDate,
		DueDate:        &overdueDate,
		Amount:         func() *float64 { a := 100.0; return &a }(),
		Balance:        func() *float64 { b := 100.0; return &b }(),
		Status:         "sent",
		IsSubscription: func() *bool { s := true; return &s }(),
	}
	config.DB.Create(&invoice)

	// Test the suspension service
	service := NewClientSuspensionService()
	err := service.ProcessOverdueSubscriptions()
	if err != nil {
		t.Errorf("ProcessOverdueSubscriptions failed: %v", err)
	}

	// Verify the client was suspended
	var updatedClient models.Client
	config.DB.First(&updatedClient, client.Id)
	if updatedClient.SuspendedAt == nil {
		t.Error("Client should have been suspended")
	}
	if updatedClient.Status != "suspended" {
		t.Errorf("Client status should be 'suspended', got '%s'", updatedClient.Status)
	}
}

func TestClientSuspensionServiceAlreadySuspended(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}

	// Initialize test database
	config.InitTestDB()
	migrations.Migrate()

	// Clean up any existing test data
	config.DB.Exec("DELETE FROM invoice_items")
	config.DB.Exec("DELETE FROM invoices")
	config.DB.Exec("DELETE FROM clients")
	config.DB.Exec("DELETE FROM currencies")
	config.DB.Exec("DELETE FROM payment_types")

	// Create test client that's already suspended
	suspendedAt := time.Now().AddDate(0, 0, -5)
	client := models.Client{
		Name:        "Already Suspended Client",
		Email:       "<EMAIL>",
		PhoneNumber: "**********",
		ClientType:  "individual",
		Status:      "suspended",
		SuspendedAt: &suspendedAt,
	}
	config.DB.Create(&client)

	// Create test currency and payment type
	currency := models.Currency{Name: "USD", Symbol: "$", Code: "USD"}
	config.DB.Create(&currency)

	paymentType := models.PaymentType{Name: "Bank Transfer"}
	config.DB.Create(&paymentType)

	// Create overdue subscription invoice
	overdueDate := time.Now().AddDate(0, 0, -15)
	invoice := models.Invoice{
		ClientId:       client.Id,
		CurrencyId:     &currency.Id,
		PaymentTypeId:  &paymentType.Id,
		Date:           &overdueDate,
		DueDate:        &overdueDate,
		Amount:         func() *float64 { a := 100.0; return &a }(),
		Balance:        func() *float64 { b := 100.0; return &b }(),
		Status:         "sent",
		IsSubscription: func() *bool { s := true; return &s }(),
	}
	config.DB.Create(&invoice)

	// Test the suspension service
	service := NewClientSuspensionService()
	err := service.ProcessOverdueSubscriptions()
	if err != nil {
		t.Errorf("ProcessOverdueSubscriptions failed: %v", err)
	}

	// Verify the client suspension date didn't change
	var updatedClient models.Client
	config.DB.First(&updatedClient, client.Id)
	if updatedClient.SuspendedAt == nil {
		t.Error("Client should still be suspended")
	}
	// Check if the suspension dates are within 1 second (to account for database precision)
	timeDiff := updatedClient.SuspendedAt.Sub(suspendedAt)
	if timeDiff < -time.Second || timeDiff > time.Second {
		t.Errorf("Client suspension date should not have changed significantly. Original: %v, Updated: %v, Diff: %v",
			suspendedAt, updatedClient.SuspendedAt, timeDiff)
	}
}
