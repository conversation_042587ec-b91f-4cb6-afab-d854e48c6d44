package services

import (
	"os"
	"testing"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func TestRecurringInvoiceService(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.<PERSON>env("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}

	// Initialize test database
	config.InitTestDB()
	migrations.Migrate()

	// Setup test environment with seeded data
	SetupTestEnvironment()

	// Create test client and get seeded data
	client := CreateTestClient()
	currency := GetSeededCurrency()
	paymentType := GetSeededPaymentType()

	// Create test recurring invoice
	recurNextDate := time.Now().AddDate(0, 0, -1) // Yesterday (should be processed)
	invoice := models.Invoice{
		ClientId:       client.Id,
		CurrencyId:     &currency.Id,
		PaymentTypeId:  &paymentType.Id,
		Date:           &recurNextDate,
		Amount:         func() *float64 { a := 100.0; return &a }(),
		Status:         "paid", // Must be paid for recurring processing
		Recurring:      func() *bool { r := true; return &r }(),
		RecurFrequency: func() *uint { f := uint(30); return &f }(), // Add frequency for new logic
		RecurNextDate:  &recurNextDate,
		IsSubscription: func() *bool { s := false; return &s }(),
	}
	result := config.DB.Create(&invoice)
	if result.Error != nil {
		t.Fatalf("Failed to create test invoice: %v", result.Error)
	}

	// Create test invoice item
	invoiceItem := models.InvoiceItem{
		InvoiceId: invoice.Id,
		Name:      func() *string { n := "Test Service"; return &n }(),
		Quantity:  func() *uint { q := uint(1); return &q }(),
		UnitCost:  func() *float64 { c := 100.0; return &c }(),
		Total:     func() *float64 { t := 100.0; return &t }(),
	}
	result = config.DB.Create(&invoiceItem)
	if result.Error != nil {
		t.Fatalf("Failed to create test invoice item: %v", result.Error)
	}

	// Test the service
	service := NewRecurringInvoiceService()
	err := service.ProcessDailyRecurringInvoices()
	if err != nil {
		t.Errorf("ProcessDailyRecurringInvoices failed: %v", err)
	}

	// Verify a new invoice was created
	var invoiceCount int64
	config.DB.Model(&models.Invoice{}).Where("client_id = ?", client.Id).Count(&invoiceCount)
	if invoiceCount != 2 {
		t.Errorf("Expected 2 invoices, got %d", invoiceCount)
	}

	// Verify the original invoice's RecurNextDate was updated
	var updatedInvoice models.Invoice
	result = config.DB.First(&updatedInvoice, invoice.Id)
	if result.Error != nil {
		t.Fatalf("Failed to find original invoice: %v", result.Error)
	}
	if updatedInvoice.RecurNextDate != nil && updatedInvoice.RecurNextDate.Equal(recurNextDate) {
		t.Error("RecurNextDate was not updated")
	}
}

func TestRecurringInvoiceFrequencyCalculation(t *testing.T) {
	baseDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)

	// Test 30-day recurring (monthly equivalent)
	nextDate := baseDate.AddDate(0, 0, 30)
	expected := time.Date(2024, 2, 14, 0, 0, 0, 0, time.UTC)
	if !nextDate.Equal(expected) {
		t.Errorf("30-day recurring: expected %v, got %v", expected, nextDate)
	}

	// Test 90-day recurring (quarterly equivalent)
	nextDate = baseDate.AddDate(0, 0, 90)
	expected = time.Date(2024, 4, 14, 0, 0, 0, 0, time.UTC)
	if !nextDate.Equal(expected) {
		t.Errorf("90-day recurring: expected %v, got %v", expected, nextDate)
	}

	// Test 365-day recurring (yearly equivalent) - 2024 is a leap year, so 365 days from Jan 15 is Jan 14 next year
	nextDate = baseDate.AddDate(0, 0, 365)
	expected = time.Date(2025, 1, 14, 0, 0, 0, 0, time.UTC)
	if !nextDate.Equal(expected) {
		t.Errorf("365-day recurring: expected %v, got %v", expected, nextDate)
	}

	t.Log("✅ Recurring invoice frequency calculation now uses simple day addition")
	t.Log("✅ RecurFrequency field contains the number of days between recurring invoices")
	t.Log("✅ No more complex billing cycle logic needed for recurring invoices")
}
