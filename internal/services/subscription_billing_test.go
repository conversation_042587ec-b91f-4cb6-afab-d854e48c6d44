package services

import (
	"fmt"
	"os"
	"testing"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func TestSubscriptionBillingService_ProcessSubscriptionBilling(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}

	// Initialize test database
	config.InitTestDB()
	migrations.Migrate()

	// Setup test environment with seeded data
	SetupTestEnvironment()

	// Get seeded data
	currency := GetSeededCurrency()

	// Get seeded protocol and device type
	var protocol models.Protocol
	config.DB.Where("name = ?", "H02").First(&protocol)

	var deviceType models.DeviceType
	config.DB.Where("protocol_id = ?", protocol.Id).First(&deviceType)

	// Ensure device type has an amount for testing
	if deviceType.Amount == nil || *deviceType.Amount == 0 {
		amount := 10.0
		deviceType.Amount = &amount
		config.DB.Save(&deviceType)
	}

	// Create test client with NextBillingDate
	nextBillingDate := time.Now().AddDate(0, 0, 5) // 5 days from now
	timestamp := time.Now().UnixNano()
	client := models.Client{
		Name:            "Test Subscription Client",
		Email:           fmt.Sprintf("<EMAIL>", timestamp),
		Status:          "active",
		NextBillingDate: &nextBillingDate,
		BillingCycle:    func() *string { s := "monthly"; return &s }(),
		BillingDay:      func() *uint { d := uint(15); return &d }(),
		CurrencyId:      &currency.Id,
	}

	err := config.DB.Create(&client).Error
	if err != nil {
		t.Fatalf("Failed to create test client: %v", err)
	}

	// Device type is already retrieved from seeded data above

	// Create test client device
	clientDevice := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		Name:         func() *string { s := "Vehicle 1"; return &s }(),
		PlateNumber:  func() *string { s := "ABC123"; return &s }(),
		Status:       "active",
	}

	err = config.DB.Create(&clientDevice).Error
	if err != nil {
		t.Fatalf("Failed to create test client device: %v", err)
	}

	// Debug: Check if client device was created
	var clientDevices []models.ClientDevice
	config.DB.Where("client_id = ?", client.Id).Find(&clientDevices)
	t.Logf("Created %d client devices for client ID %d", len(clientDevices), client.Id)

	// Debug: Check device type amount
	t.Logf("Device type ID %d has amount: %v", deviceType.Id, deviceType.Amount)

	// Debug: Check settings
	currencySetting := models.GetSetting("currency")
	dueDaysSetting := models.GetSetting("invoice_due_after_days")
	t.Logf("Currency setting: '%s', Due days setting: '%s'", currencySetting, dueDaysSetting)

	// Debug: Check payment types
	var paymentTypes []models.PaymentType
	config.DB.Where("active = ?", true).Find(&paymentTypes)
	t.Logf("Found %d active payment types", len(paymentTypes))

	// Debug: Check tax rates
	var taxRates []models.TaxRate
	config.DB.Find(&taxRates)
	t.Logf("Found %d total tax rates", len(taxRates))

	var activeTaxRates []models.TaxRate
	config.DB.Where("active = ?", true).Find(&activeTaxRates)
	t.Logf("Found %d active tax rates", len(activeTaxRates))

	// Test the subscription billing service
	service := NewSubscriptionBillingService()
	err = service.ProcessSubscriptionBilling()
	if err != nil {
		t.Fatalf("ProcessSubscriptionBilling failed: %v", err)
	}

	// Verify invoice was created
	var invoices []models.Invoice
	err = config.DB.Where("client_id = ?", client.Id).Find(&invoices).Error
	if err != nil {
		t.Fatalf("Failed to fetch invoices: %v", err)
	}

	if len(invoices) != 1 {
		t.Errorf("Expected 1 invoice, got %d", len(invoices))
		// Debug: Show what invoices exist
		var allInvoices []models.Invoice
		config.DB.Find(&allInvoices)
		t.Logf("Total invoices in database: %d", len(allInvoices))
		return
	}

	invoice := invoices[0]
	if invoice.IsSubscription == nil || !*invoice.IsSubscription {
		t.Error("Invoice should be marked as subscription")
	}

	// Expected amount: device amount (10.00) + 15% tax = 11.50
	expectedAmount := 11.50
	if invoice.Amount == nil || *invoice.Amount != expectedAmount {
		t.Errorf("Expected invoice amount %.2f, got %.2f", expectedAmount, *invoice.Amount)
	}

	// Verify invoice items were created
	var invoiceItems []models.InvoiceItem
	err = config.DB.Where("invoice_id = ?", invoice.Id).Find(&invoiceItems).Error
	if err != nil {
		t.Fatalf("Failed to fetch invoice items: %v", err)
	}

	if len(invoiceItems) != 1 {
		t.Fatalf("Expected 1 invoice item, got %d", len(invoiceItems))
	}

	item := invoiceItems[0]
	expectedName := "Vehicle 1 (ABC123)"
	if item.Name == nil || *item.Name != expectedName {
		t.Errorf("Expected item name '%s', got '%v'", expectedName, item.Name)
	}

	// Verify client's next billing date was updated
	var updatedClient models.Client
	err = config.DB.First(&updatedClient, client.Id).Error
	if err != nil {
		t.Fatalf("Failed to fetch updated client: %v", err)
	}

	if updatedClient.NextBillingDate.Before(nextBillingDate) {
		t.Error("Client's NextBillingDate should have been updated to a future date")
	}
}

func TestSubscriptionBillingService_SkipClientWithUnpaidInvoices(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}

	// Initialize test database
	config.InitTestDB()
	migrations.Migrate()

	// Setup test environment with seeded data
	SetupTestEnvironment()

	// Get seeded data
	currency := GetSeededCurrency()

	// Get seeded protocol and device type
	var protocol models.Protocol
	config.DB.Where("name = ?", "H02").First(&protocol)

	var deviceType models.DeviceType
	config.DB.Where("protocol_id = ?", protocol.Id).First(&deviceType)

	// Create test client with next billing date in the future (should be processed)
	nextBillingDate := time.Now().AddDate(0, 0, 5)
	timestamp := time.Now().UnixNano()
	client := models.Client{
		Name:            "Test Client With Unpaid 2",
		Email:           fmt.Sprintf("<EMAIL>", timestamp),
		Status:          "active",
		NextBillingDate: &nextBillingDate,
		BillingCycle:    func() *string { s := "monthly"; return &s }(),
		CurrencyId:      &currency.Id,
	}

	err := config.DB.Create(&client).Error
	if err != nil {
		t.Fatalf("Failed to create test client: %v", err)
	}

	// Create unpaid subscription invoice for the client
	balance := 100.0
	unpaidInvoice := models.Invoice{
		ClientId:       client.Id,
		CurrencyId:     client.CurrencyId,
		Date:           &nextBillingDate,
		Balance:        &balance,
		Status:         "draft",
		IsSubscription: func() *bool { b := true; return &b }(), // Mark as subscription invoice
	}

	err = config.DB.Create(&unpaidInvoice).Error
	if err != nil {
		t.Fatalf("Failed to create unpaid invoice: %v", err)
	}

	// Create device for the client using seeded device type
	clientDevice := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		Name:         func() *string { s := "Test Vehicle"; return &s }(),
		PlateNumber:  func() *string { s := "TEST123"; return &s }(),
		Status:       "active",
	}

	err = config.DB.Create(&clientDevice).Error
	if err != nil {
		t.Fatalf("Failed to create test client device: %v", err)
	}

	// Test the subscription billing service
	service := NewSubscriptionBillingService()
	err = service.ProcessSubscriptionBilling()
	if err != nil {
		t.Fatalf("ProcessSubscriptionBilling failed: %v", err)
	}

	// Verify no new invoice was created (should still have only the unpaid one)
	var invoices []models.Invoice
	err = config.DB.Where("client_id = ?", client.Id).Find(&invoices).Error
	if err != nil {
		t.Fatalf("Failed to fetch invoices: %v", err)
	}

	if len(invoices) != 1 {
		t.Fatalf("Expected 1 invoice (the unpaid one), got %d", len(invoices))
	}

	// Verify it's the original unpaid invoice
	if invoices[0].Id != unpaidInvoice.Id {
		t.Error("Should not have created a new invoice when client has unpaid invoices")
	}
}

func TestSubscriptionBillingService_CalculateNextBillingDate(t *testing.T) {
	service := NewSubscriptionBillingService()

	// Test monthly billing
	currentDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	nextDate := service.calculateNextBillingDate(currentDate, "monthly", 15)
	expected := time.Date(2024, 2, 15, 0, 0, 0, 0, time.UTC)

	if !nextDate.Equal(expected) {
		t.Errorf("Monthly billing: expected %v, got %v", expected, nextDate)
	}

	// Test quarterly billing
	nextDate = service.calculateNextBillingDate(currentDate, "quarterly", 15)
	expected = time.Date(2024, 4, 15, 0, 0, 0, 0, time.UTC)

	if !nextDate.Equal(expected) {
		t.Errorf("Quarterly billing: expected %v, got %v", expected, nextDate)
	}

	// Test yearly billing
	nextDate = service.calculateNextBillingDate(currentDate, "yearly", 15)
	expected = time.Date(2025, 1, 15, 0, 0, 0, 0, time.UTC)

	if !nextDate.Equal(expected) {
		t.Errorf("Yearly billing: expected %v, got %v", expected, nextDate)
	}

	// Test edge case: February 29 -> February 28 in non-leap year
	currentDate = time.Date(2024, 2, 29, 0, 0, 0, 0, time.UTC) // 2024 is leap year
	nextDate = service.calculateNextBillingDate(currentDate, "yearly", 29)
	expected = time.Date(2025, 2, 28, 0, 0, 0, 0, time.UTC) // 2025 is not leap year

	if !nextDate.Equal(expected) {
		t.Errorf("Leap year edge case: expected %v, got %v", expected, nextDate)
	}
}
