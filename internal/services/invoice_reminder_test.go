package services

import (
	"os"
	"testing"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func TestInvoiceReminderService(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.<PERSON>env("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}

	// Initialize test database
	config.InitTestDB()
	migrations.Migrate()

	// Clean up any existing test data
	config.DB.Exec("DELETE FROM invoice_items")
	config.DB.Exec("DELETE FROM invoices")
	config.DB.Exec("DELETE FROM clients")
	config.DB.Exec("DELETE FROM currencies")
	config.DB.Exec("DELETE FROM payment_types")

	// Create test client
	client := models.Client{
		Name:         "Test Client",
		Email:        "<EMAIL>",
		PhoneNumber:  "**********",
		ClientType:   "individual",
		Status:       "active",
		BillingCycle: func() *string { s := "monthly"; return &s }(),
		BillingDay:   func() *uint { d := uint(15); return &d }(),
	}
	config.DB.Create(&client)

	// Create test currency and payment type
	currency := models.Currency{Name: "USD", Symbol: "$", Code: "USD"}
	config.DB.Create(&currency)

	paymentType := models.PaymentType{Name: "Bank Transfer"}
	config.DB.Create(&paymentType)

	// Test first overdue reminder (2 days overdue)
	firstOverdueDate := time.Now().AddDate(0, 0, -2)
	firstInvoice := models.Invoice{
		ClientId:      client.Id,
		CurrencyId:    &currency.Id,
		PaymentTypeId: &paymentType.Id,
		Date:          &firstOverdueDate,
		DueDate:       &firstOverdueDate,
		Amount:        func() *float64 { a := 100.0; return &a }(),
		Balance:       func() *float64 { b := 100.0; return &b }(),
		Status:        "sent",
		Reference:     func() *string { r := "INV-001"; return &r }(),
	}
	config.DB.Create(&firstInvoice)

	// Test second overdue reminder (5 days overdue)
	secondOverdueDate := time.Now().AddDate(0, 0, -5)
	secondInvoice := models.Invoice{
		ClientId:      client.Id,
		CurrencyId:    &currency.Id,
		PaymentTypeId: &paymentType.Id,
		Date:          &secondOverdueDate,
		DueDate:       &secondOverdueDate,
		Amount:        func() *float64 { a := 200.0; return &a }(),
		Balance:       func() *float64 { b := 200.0; return &b }(),
		Status:        "sent",
		Reference:     func() *string { r := "INV-002"; return &r }(),
	}
	config.DB.Create(&secondInvoice)

	// Test third overdue reminder (10 days overdue)
	thirdOverdueDate := time.Now().AddDate(0, 0, -10)
	thirdInvoice := models.Invoice{
		ClientId:      client.Id,
		CurrencyId:    &currency.Id,
		PaymentTypeId: &paymentType.Id,
		Date:          &thirdOverdueDate,
		DueDate:       &thirdOverdueDate,
		Amount:        func() *float64 { a := 300.0; return &a }(),
		Balance:       func() *float64 { b := 300.0; return &b }(),
		Status:        "sent",
		Reference:     func() *string { r := "INV-003"; return &r }(),
	}
	config.DB.Create(&thirdInvoice)

	// Test the reminder service
	service := NewInvoiceReminderService()

	// Test first overdue reminders
	err := service.ProcessFirstOverdueReminders()
	if err != nil {
		t.Errorf("ProcessFirstOverdueReminders failed: %v", err)
	}

	// Test second overdue reminders
	err = service.ProcessSecondOverdueReminders()
	if err != nil {
		t.Errorf("ProcessSecondOverdueReminders failed: %v", err)
	}

	// Test third overdue reminders
	err = service.ProcessThirdOverdueReminders()
	if err != nil {
		t.Errorf("ProcessThirdOverdueReminders failed: %v", err)
	}
}

func TestInvoiceReminderServiceNoPaidInvoices(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}

	// Initialize test database
	config.InitTestDB()
	migrations.Migrate()

	// Clean up any existing test data
	config.DB.Exec("DELETE FROM invoice_items")
	config.DB.Exec("DELETE FROM invoices")
	config.DB.Exec("DELETE FROM clients")
	config.DB.Exec("DELETE FROM currencies")
	config.DB.Exec("DELETE FROM payment_types")
	config.DB.Exec("DELETE FROM settings")

	// Create required settings for invoice reminders
	settings := []models.Setting{
		{Name: "First Invoice Overdue Reminder Days", SettingKey: "first_invoice_overdue_reminder_days", SettingValue: "2", Category: stringPtr("general")},
		{Name: "Second Invoice Overdue Reminder Days", SettingKey: "second_invoice_overdue_reminder_days", SettingValue: "5", Category: stringPtr("general")},
		{Name: "Third Invoice Overdue Reminder Days", SettingKey: "third_invoice_overdue_reminder_days", SettingValue: "10", Category: stringPtr("general")},
	}
	for _, setting := range settings {
		config.DB.Create(&setting)
	}

	// Create test client
	client := models.Client{
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "**********",
		ClientType:  "individual",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test currency and payment type
	currency := models.Currency{Name: "USD", Symbol: "$", Code: "USD"}
	config.DB.Create(&currency)

	paymentType := models.PaymentType{Name: "Bank Transfer"}
	config.DB.Create(&paymentType)

	// Create a paid invoice (should not trigger reminders)
	paidDate := time.Now().AddDate(0, 0, -2)
	paidInvoice := models.Invoice{
		ClientId:      client.Id,
		CurrencyId:    &currency.Id,
		PaymentTypeId: &paymentType.Id,
		Date:          &paidDate,
		DueDate:       &paidDate,
		Amount:        func() *float64 { a := 100.0; return &a }(),
		Balance:       func() *float64 { b := 0.0; return &b }(), // Paid invoice has 0 balance
		Status:        "paid",
		Reference:     func() *string { r := "INV-PAID"; return &r }(),
	}
	config.DB.Create(&paidInvoice)

	// Test the reminder service - should not send any reminders for paid invoices
	service := NewInvoiceReminderService()

	err := service.ProcessFirstOverdueReminders()
	if err != nil {
		t.Errorf("ProcessFirstOverdueReminders failed: %v", err)
	}
}

// Helper function for string pointers
func stringPtr(s string) *string {
	return &s
}
