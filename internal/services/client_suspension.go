package services

import (
	"fmt"
	"log"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/mail"
	"yotracker/internal/models"
)

type ClientSuspensionService struct{}

func NewClientSuspensionService() *ClientSuspensionService {
	return &ClientSuspensionService{}
}

// ProcessOverdueInvoices finds and suspends clients with overdue invoices based on due_date + third_invoice_overdue_reminder_days
func (s *ClientSuspensionService) ProcessOverdueInvoices() error {
	// Get the third_invoice_overdue_reminder_days setting
	overdueReminderDays := models.GetSetting("third_invoice_overdue_reminder_days")
	if overdueReminderDays == "" {
		overdueReminderDays = "10" // default value
	}

	reminderDaysInt, err := strconv.Atoi(overdueReminderDays)
	if err != nil {
		return fmt.Errorf("invalid third_invoice_overdue_reminder_days setting: %v", err)
	}

	log.Printf("Processing client suspensions for invoices overdue beyond %d days after due date", reminderDaysInt)

	// Find invoices where due_date + third_invoice_overdue_reminder_days <= today and balance > 0
	var overdueInvoices []models.Invoice
	err = config.DB.Where("DATE_ADD(due_date, INTERVAL ? DAY) <= CURDATE() AND balance > 0 AND status in ? and is_subscription = ?",
		reminderDaysInt, []string{"sent", "partial", "pending", "overdue", "failed"}, true).
		Preload("Client").
		Preload("Currency").
		Find(&overdueInvoices).Error

	if err != nil {
		return fmt.Errorf("failed to fetch overdue invoices for suspension: %v", err)
	}

	log.Printf("Found %d overdue invoices for suspension processing", len(overdueInvoices))

	// Group invoices by client to avoid duplicate suspensions
	clientInvoiceMap := make(map[uint][]models.Invoice)
	for _, invoice := range overdueInvoices {
		clientInvoiceMap[invoice.ClientId] = append(clientInvoiceMap[invoice.ClientId], invoice)
	}

	for clientId, invoices := range clientInvoiceMap {
		suspended, err := s.suspendClient(clientId, invoices)
		if err != nil {
			log.Printf("Failed to suspend client ID %d: %v", clientId, err)
			continue
		}
		if suspended {
			log.Printf("Successfully suspended client ID %d for %d overdue invoices", clientId, len(invoices))
		} else {
			log.Printf("Client ID %d was already suspended, skipped", clientId)
		}
	}

	return nil
}

// suspendClient suspends a client and sends suspension notification email
// Returns (wasSuspended, error)
func (s *ClientSuspensionService) suspendClient(clientId uint, overdueInvoices []models.Invoice) (bool, error) {
	// Check if client is already suspended
	var client models.Client
	err := config.DB.First(&client, clientId).Error
	if err != nil {
		return false, fmt.Errorf("failed to fetch client: %v", err)
	}

	// Skip if already suspended
	if client.SuspendedAt != nil {
		log.Printf("Client ID %d is already suspended, skipping", clientId)
		return false, nil
	}

	// Suspend the client
	now := time.Now()
	client.SuspendedAt = &now
	client.Status = "suspended"

	err = config.DB.Save(&client).Error
	if err != nil {
		return false, fmt.Errorf("failed to suspend client: %v", err)
	}

	// Send suspension notification email
	err = s.sendSuspensionEmail(client, overdueInvoices)
	if err != nil {
		log.Printf("Failed to send suspension email to client ID %d: %v", clientId, err)
		// Don't return error here as suspension was successful
	}

	return true, nil
}

// sendSuspensionEmail sends a service suspension notification email
func (s *ClientSuspensionService) sendSuspensionEmail(client models.Client, overdueInvoices []models.Invoice) error {
	return mail.SendServiceSuspensionEmail(client, overdueInvoices)
}

// ProcessOverdueSubscriptions is kept for backward compatibility - now calls ProcessOverdueInvoices
func (s *ClientSuspensionService) ProcessOverdueSubscriptions() error {
	return s.ProcessOverdueInvoices()
}
