FROM golang:1.23-alpine

WORKDIR /app/yotracker

# Install Air for hot reloading
RUN go install github.com/air-verse/air@latest

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY cmd ./cmd
COPY internal ./internal
COPY config ./config
COPY docker/simulator/gt06/.air.toml ./
COPY .env ./

# Run Air for hot reloading
CMD ["air", "-c", ".air.toml"] 