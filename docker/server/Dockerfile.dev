FROM golang:1.23-alpine

WORKDIR /app/yotracker

# Install Air for hot reloading
RUN go install github.com/air-verse/air@latest

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY cmd ./cmd
COPY internal ./internal
COPY config ./config
COPY .env ./
COPY docker/server/.air.toml ./


# Expose the TCP ports for GT06 and H02 protocols
EXPOSE 5022 5010

# Run Air for hot reloading
CMD ["air", "-c", ".air.toml"] 