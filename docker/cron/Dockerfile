# Build stage
FROM golang:1.21-alpine AS builder

WORKDIR /app/yotracker

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY cmd ./cmd
COPY internal ./internal
COPY config ./config

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o backend ./cmd/cron

# Final stage
FROM alpine:latest

WORKDIR /app

# Copy the binary from builder
COPY --from=builder /app/yotracker/cron .


# Run the backend
CMD ["./cron"]
