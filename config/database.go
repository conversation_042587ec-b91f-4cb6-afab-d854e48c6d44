package config

import (
	"fmt"
	"github.com/joho/godotenv"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"log"
	"os"
	"path/filepath"
	"regexp"
)

var DB *gorm.DB

const projectDirName = "yotracker"

func InitDB() {
	// Get the absolute path to the .env file
	rootDir, _ := filepath.Abs(filepath.Join(filepath.Dir("."), ".."))
	envPath := filepath.Join(rootDir+"/"+projectDirName, ".env")
	err := godotenv.Load(envPath)

	if err != nil {
		log.Fatalf("Error loading .env file")
	}
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local", os.Getenv("DB_USERNAME"), os.<PERSON>env("DB_PASSWORD"), os.<PERSON>env("DB_HOST"), os.<PERSON>env("DB_PORT"), os.<PERSON>env("DB_NAME"))
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		fmt.Println("Failed to connect to database")
		log.Fatal("Database connection error:", err)
	}
}
func InitTestDB() {
	rootDir, _ := filepath.Abs(filepath.Join(filepath.Dir("."), ".."))
	envPath := filepath.Join(rootDir+"/"+projectDirName, ".env.testing")
	err := godotenv.Load(envPath)
	if err != nil {
		fmt.Println("Error loading .env file, using exported variables")
	}

	// Use TESTING_DB_NAME for test database, fallback to "testing" if not set
	testDBName := os.Getenv("TESTING_DB_NAME")
	if testDBName == "" {
		testDBName = "testing"
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local", os.Getenv("DB_USERNAME"), os.Getenv("DB_PASSWORD"), os.Getenv("DB_HOST"), os.Getenv("DB_PORT"), testDBName)
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		fmt.Println("Failed to connect to test database")
		log.Fatal("Test database connection error:", err)
	}

	// Disable foreign key constraints for testing
	DB.Exec("SET FOREIGN_KEY_CHECKS = 0")

}
func loadEnv() {
	projectName := regexp.MustCompile(`^(.*` + projectDirName + `)`)
	currentWorkDirectory, _ := os.Getwd()
	rootPath := projectName.Find([]byte(currentWorkDirectory))

	err := godotenv.Load(string(rootPath) + `/.env`)

	if err != nil {
		log.Fatalf("Error loading .env file")
	}
}
func GetDB() *gorm.DB {
	return DB
}
