package config

import (
	"os"
	"path/filepath"
	"testing"
)

func TestDatabaseConnection(t *testing.T) {
	// Set working directory to project root
	if wd, err := os.Getwd(); err == nil {
		if filepath.Base(wd) == "config" {
			os.Ch<PERSON>("..")
		}
	}

	// Set minimal required environment variables if not already set
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.<PERSON>env("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}

	// Test database connection
	InitTestDB()

	if DB == nil {
		t.Fatal("Database connection failed - DB is nil")
	}

	// Test basic query
	sqlDB, err := DB.DB()
	if err != nil {
		t.Fatalf("Failed to get underlying sql.DB: %v", err)
	}

	err = sqlDB.Ping()
	if err != nil {
		t.Fatalf("Database ping failed: %v", err)
	}

	t.Log("Database connection test passed!")
}
