package examples

import (
	"fmt"
	"log"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
)

func GoogleGeocodingExample() {
	// Initialize database
	config.InitDB()

	// Create Google Geocoding service
	geocodingService := service.NewGoogleGeocodingService()

	// Example 1: Get location name for specific coordinates
	fmt.Println("=== Example 1: Get location name for specific coordinates ===")
	latitude := -17.8216
	longitude := 31.0492

	locationName, err := geocodingService.GetLocationName(latitude, longitude)
	if err != nil {
		log.Printf("Error getting location name: %v", err)
	} else {
		fmt.Printf("Coordinates (%.6f, %.6f): %s\n", latitude, longitude, locationName)
	}

	// Example 2: Simulate last_location route behavior
	fmt.Println("\n=== Example 2: Simulate last_location route behavior ===")

	// Get a sample GPS data record (you would normally get this from the route)
	var gpsData models.GPSData
	if err := config.DB.First(&gpsData).Error; err != nil {
		log.Printf("No GPS data found in database: %v", err)
		fmt.Println("Skipping GPS data update example")
	} else {
		fmt.Printf("Found GPS data record ID: %d\n", gpsData.Id)
		fmt.Printf("Current location name: %s\n", func() string {
			if gpsData.LocationName == nil {
				return "nil"
			}
			return *gpsData.LocationName
		}())
		fmt.Printf("Coordinates: (%.6f, %.6f)\n", gpsData.Latitude, gpsData.Longitude)

		// Check if location name is empty and fetch it
		if gpsData.LocationName == nil || *gpsData.LocationName == "" {
			fmt.Println("Location name is empty, fetching from Google Geocoding API...")
			err := geocodingService.UpdateGPSDataLocationName(&gpsData)
			if err != nil {
				log.Printf("Error updating GPS data location name: %v", err)
			} else {
				fmt.Printf("Updated location name: %s\n", func() string {
					if gpsData.LocationName == nil {
						return "nil"
					}
					return *gpsData.LocationName
				}())
			}
		} else {
			fmt.Println("Location name already exists, no need to fetch")
		}
	}

	// Example 3: Test with different coordinates
	fmt.Println("\n=== Example 3: Test with different coordinates ===")
	testCoords := []struct {
		lat, lon float64
		desc     string
	}{
		{-17.8250, 31.0500, "Near Harare"},
		{-17.8300, 31.0550, "Harare area"},
		{-17.8350, 31.0600, "Further from Harare"},
	}

	for _, coord := range testCoords {
		locationName, err := geocodingService.GetLocationName(coord.lat, coord.lon)
		if err != nil {
			log.Printf("Error getting location name for %s: %v", coord.desc, err)
			continue
		}
		fmt.Printf("%s (%.6f, %.6f): %s\n", coord.desc, coord.lat, coord.lon, locationName)
	}

	// Example 4: Check Google Maps API key configuration
	fmt.Println("\n=== Example 4: Check Google Maps API key configuration ===")
	apiKey := models.GetSetting("google_maps_api_key")
	if apiKey == "" {
		fmt.Println("Warning: Google Maps API key is not configured in settings")
		fmt.Println("Please add 'google_maps_api_key' setting with your API key")
	} else {
		fmt.Printf("Google Maps API key is configured (length: %d characters)\n", len(apiKey))
		fmt.Printf("API key starts with: %s...\n", apiKey[:10])
	}
}
