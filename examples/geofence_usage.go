package examples

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

// Example demonstrating how to use the geofence API endpoints

type GeofenceRequest struct {
	Name           string   `json:"name"`
	GeofenceType   string   `json:"geofence_type,omitempty"`
	AppliesTo      string   `json:"applies_to"`
	FleetId        *uint    `json:"fleet_id,omitempty"`
	ClientDeviceId *uint    `json:"client_device_id,omitempty"`
	DeviceId       string   `json:"device_id,omitempty"`
	TriggerEvents  string   `json:"trigger_events,omitempty"`
	Radius         *float64 `json:"radius,omitempty"`
	Latitude       *float64 `json:"latitude,omitempty"`
	Longitude      *float64 `json:"longitude,omitempty"`
	Coordinates    *string  `json:"coordinates,omitempty"`
	Status         string   `json:"status,omitempty"`
}

type GeofenceResponse struct {
	Id             uint     `json:"id"`
	ClientId       uint     `json:"client_id"`
	Name           string   `json:"name"`
	GeofenceType   string   `json:"geofence_type"`
	AppliesTo      string   `json:"applies_to"`
	FleetId        *uint    `json:"fleet_id,omitempty"`
	ClientDeviceId *uint    `json:"client_device_id,omitempty"`
	DeviceId       string   `json:"device_id,omitempty"`
	TriggerEvents  string   `json:"trigger_events,omitempty"`
	Radius         *float64 `json:"radius,omitempty"`
	Latitude       *float64 `json:"latitude,omitempty"`
	Longitude      *float64 `json:"longitude,omitempty"`
	Coordinates    *string  `json:"coordinates,omitempty"`
	Status         string   `json:"status"`
	CreatedAt      string   `json:"created_at"`
	UpdatedAt      string   `json:"updated_at"`
}

type APIResponse struct {
	Message string           `json:"message,omitempty"`
	Data    GeofenceResponse `json:"data,omitempty"`
}

func main() {
	// Example usage of geofence API
	baseURL := "http://localhost:9001/api/v1/frontend"
	authToken := "your-auth-token-here"

	// Example 1: Create a device-level circle geofence (entry only)
	fmt.Println("=== Creating Device-Level Circle Geofence (Entry Only) ===")
	geofence := GeofenceRequest{
		Name:           "Home Arrival Alert",
		GeofenceType:   "circle",
		AppliesTo:      "device",
		ClientDeviceId: func() *uint { id := uint(123); return &id }(),
		TriggerEvents:  "entry",
		Radius:         func() *float64 { r := 100.0; return &r }(), // 100 meters
		Latitude:       func() *float64 { lat := -17.8216; return &lat }(),
		Longitude:      func() *float64 { lng := 31.0492; return &lng }(),
		Status:         "active",
	}

	geofenceID, err := createGeofence(baseURL, authToken, geofence)
	if err != nil {
		fmt.Printf("Error creating geofence: %v\n", err)
		return
	}
	fmt.Printf("Created geofence with ID: %d\n", geofenceID)

	// Example 2: Get all geofences
	fmt.Println("\n=== Getting All Geofences ===")
	err = getAllGeofences(baseURL, authToken)
	if err != nil {
		fmt.Printf("Error getting geofences: %v\n", err)
	}

	// Example 3: Get geofence by ID
	fmt.Println("\n=== Getting Geofence by ID ===")
	err = getGeofenceById(baseURL, authToken, geofenceID)
	if err != nil {
		fmt.Printf("Error getting geofence: %v\n", err)
	}

	// Example 4: Update geofence
	fmt.Println("\n=== Updating Geofence ===")
	updateData := map[string]interface{}{
		"name":   "Updated Home Geofence",
		"radius": 150,
	}
	err = updateGeofence(baseURL, authToken, geofenceID, updateData)
	if err != nil {
		fmt.Printf("Error updating geofence: %v\n", err)
	}

	// Example 5: Get geofence events
	fmt.Println("\n=== Getting Geofence Events ===")
	err = getGeofenceEvents(baseURL, authToken)
	if err != nil {
		fmt.Printf("Error getting geofence events: %v\n", err)
	}

	// Example 6: Get geofences for specific device
	fmt.Println("\n=== Getting Geofences for Device ===")
	err = getGeofencesByDevice(baseURL, authToken, 123)
	if err != nil {
		fmt.Printf("Error getting geofences for device: %v\n", err)
	}

	// Example 7: Create a fleet-level polygon geofence (exit only)
	fmt.Println("\n=== Creating Fleet-Level Polygon Geofence (Exit Only) ===")
	polygonCoords := `[{"lat":-17.8216,"lng":31.0492},{"lat":-17.8220,"lng":31.0500},{"lat":-17.8210,"lng":31.0510},{"lat":-17.8200,"lng":31.0495}]`
	polygonGeofence := GeofenceRequest{
		Name:          "Fleet Operating Area - Exit Alert",
		GeofenceType:  "polygon",
		AppliesTo:     "fleet",
		FleetId:       func() *uint { id := uint(456); return &id }(),
		TriggerEvents: "exit",
		Coordinates:   &polygonCoords,
		Status:        "active",
	}

	polygonGeofenceID, err := createGeofence(baseURL, authToken, polygonGeofence)
	if err != nil {
		fmt.Printf("Error creating polygon geofence: %v\n", err)
	} else {
		fmt.Printf("Created polygon geofence with ID: %d\n", polygonGeofenceID)
	}

	// Example 8: Create a client-level rectangle geofence (both events)
	fmt.Println("\n=== Creating Client-Level Rectangle Geofence (Both Events) ===")
	rectCoords := `[{"lat":-17.8216,"lng":31.0492},{"lat":-17.8220,"lng":31.0492},{"lat":-17.8220,"lng":31.0500},{"lat":-17.8216,"lng":31.0500}]`
	rectangleGeofence := GeofenceRequest{
		Name:          "Company Campus - Full Tracking",
		GeofenceType:  "rectangle",
		AppliesTo:     "client",
		TriggerEvents: "both",
		Coordinates:   &rectCoords,
		Status:        "active",
	}

	rectangleGeofenceID, err := createGeofence(baseURL, authToken, rectangleGeofence)
	if err != nil {
		fmt.Printf("Error creating rectangle geofence: %v\n", err)
	} else {
		fmt.Printf("Created rectangle geofence with ID: %d\n", rectangleGeofenceID)
	}

	// Example 9: Search geofences
	fmt.Println("\n=== Searching Geofences ===")
	err = searchGeofences(baseURL, authToken, "Home")
	if err != nil {
		fmt.Printf("Error searching geofences: %v\n", err)
	}

	// Example 10: Delete geofences
	fmt.Println("\n=== Deleting Geofences ===")
	err = deleteGeofence(baseURL, authToken, geofenceID)
	if err != nil {
		fmt.Printf("Error deleting circle geofence: %v\n", err)
	}

	if polygonGeofenceID > 0 {
		err = deleteGeofence(baseURL, authToken, polygonGeofenceID)
		if err != nil {
			fmt.Printf("Error deleting polygon geofence: %v\n", err)
		}
	}

	if rectangleGeofenceID > 0 {
		err = deleteGeofence(baseURL, authToken, rectangleGeofenceID)
		if err != nil {
			fmt.Printf("Error deleting rectangle geofence: %v\n", err)
		}
	}
}

func createGeofence(baseURL, authToken string, geofence GeofenceRequest) (uint, error) {
	jsonData, err := json.Marshal(geofence)
	if err != nil {
		return 0, err
	}

	req, err := http.NewRequest("POST", baseURL+"/geofences", bytes.NewBuffer(jsonData))
	if err != nil {
		return 0, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+authToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, err
	}

	var response APIResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		return 0, err
	}

	fmt.Printf("Response: %s\n", response.Message)
	return response.Data.Id, nil
}

func getAllGeofences(baseURL, authToken string) error {
	req, err := http.NewRequest("GET", baseURL+"/geofences", nil)
	if err != nil {
		return err
	}

	req.Header.Set("Authorization", "Bearer "+authToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	fmt.Printf("Geofences: %s\n", string(body))
	return nil
}

func getGeofenceById(baseURL, authToken string, id uint) error {
	url := fmt.Sprintf("%s/geofences/%d", baseURL, id)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return err
	}

	req.Header.Set("Authorization", "Bearer "+authToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	fmt.Printf("Geofence: %s\n", string(body))
	return nil
}

func updateGeofence(baseURL, authToken string, id uint, updateData map[string]interface{}) error {
	jsonData, err := json.Marshal(updateData)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("%s/geofences/%d", baseURL, id)
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+authToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	fmt.Printf("Update response: %s\n", string(body))
	return nil
}

func getGeofenceEvents(baseURL, authToken string) error {
	req, err := http.NewRequest("GET", baseURL+"/geofences/events", nil)
	if err != nil {
		return err
	}

	req.Header.Set("Authorization", "Bearer "+authToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	fmt.Printf("Geofence events: %s\n", string(body))
	return nil
}

func getGeofencesByDevice(baseURL, authToken string, deviceId uint) error {
	url := fmt.Sprintf("%s/geofences/device/%d", baseURL, deviceId)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return err
	}

	req.Header.Set("Authorization", "Bearer "+authToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	fmt.Printf("Device geofences: %s\n", string(body))
	return nil
}

func searchGeofences(baseURL, authToken, name string) error {
	url := fmt.Sprintf("%s/geofences/search?name=%s", baseURL, name)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return err
	}

	req.Header.Set("Authorization", "Bearer "+authToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	fmt.Printf("Search results: %s\n", string(body))
	return nil
}

func deleteGeofence(baseURL, authToken string, id uint) error {
	url := fmt.Sprintf("%s/geofences/%d", baseURL, id)
	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		return err
	}

	req.Header.Set("Authorization", "Bearer "+authToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	fmt.Printf("Delete response: %s\n", string(body))
	return nil
}
