package examples

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

// Example demonstrating the frontend client update functionality

func ExampleClientUpdate() {
	fmt.Println("=== Frontend Client Update Example ===")
	fmt.Println()

	// Example 1: Successful client update
	fmt.Println("1. Successful Client Update:")
	successfulUpdateExample()
	fmt.Println()

	// Example 2: Email validation
	fmt.Println("2. Email Validation Example:")
	emailValidationExample()
	fmt.Println()

	// Example 3: Field restrictions
	fmt.Println("3. Field Restrictions:")
	fieldRestrictionsExample()
	fmt.Println()

	fmt.Println("=== Key Features ===")
	fmt.Println("✅ Users can only update their own client")
	fmt.Println("✅ Email uniqueness validation")
	fmt.Println("✅ No ID parameter needed")
	fmt.Println("✅ Limited to profile fields only")
	fmt.Println("✅ Removed unused methods for security")
}

func successfulUpdateExample() {
	// UpdateClientRequest with allowed fields
	updateRequest := map[string]interface{}{
		"name":                 "Updated Client Name",
		"email":                "<EMAIL>",
		"phone_number":         "**********",
		"company":              "Updated Company Ltd",
		"state":                "California",
		"city":                 "San Francisco",
		"town":                 "Downtown",
		"address":              "123 Updated Street, Suite 456",
		"gender":               "male",
		"description":          "Updated client description",
		"maps_provider":        "mapbox",
		"default_landing_page": "devices",
	}

	jsonData, _ := json.MarshalIndent(updateRequest, "", "  ")
	fmt.Printf("PUT /api/v1/frontend/clients\n")
	fmt.Printf("Authorization: Bearer <user_token>\n")
	fmt.Printf("Content-Type: application/json\n\n")
	fmt.Printf("%s\n", string(jsonData))

	// Simulate successful response
	fmt.Println("\n--- Response ---")
	fmt.Println("HTTP 200 OK")
	fmt.Println(`{"message": "Client updated successfully"}`)
}

func emailValidationExample() {
	fmt.Println("Email uniqueness validation prevents conflicts:")
	fmt.Println()

	// Example with existing email
	fmt.Println("❌ Trying to use existing email:")
	conflictRequest := map[string]interface{}{
		"name":         "Updated Name",
		"email":        "<EMAIL>", // Email already exists
		"phone_number": "**********",
	}

	jsonData, _ := json.MarshalIndent(conflictRequest, "", "  ")
	fmt.Printf("PUT /api/v1/frontend/clients\n")
	fmt.Printf("%s\n", string(jsonData))
	fmt.Println("\n--- Response ---")
	fmt.Println("HTTP 400 Bad Request")
	fmt.Println(`{"message": "Email already exists"}`)
	fmt.Println()

	// Example with same email (allowed)
	fmt.Println("✅ Keeping same email (allowed):")
	sameEmailRequest := map[string]interface{}{
		"name":         "Updated Name",
		"email":        "<EMAIL>", // Same as current email
		"phone_number": "**********",
	}

	jsonData, _ = json.MarshalIndent(sameEmailRequest, "", "  ")
	fmt.Printf("PUT /api/v1/frontend/clients\n")
	fmt.Printf("%s\n", string(jsonData))
	fmt.Println("\n--- Response ---")
	fmt.Println("HTTP 200 OK")
	fmt.Println(`{"message": "Client updated successfully"}`)
}

func fieldRestrictionsExample() {
	fmt.Println("✅ ALLOWED FIELDS (Frontend can update):")
	allowedFields := []string{
		"name (required)",
		"email (required, unique)",
		"phone_number (required)",
		"company",
		"state",
		"city",
		"town",
		"address",
		"gender",
		"description",
		"maps_provider",
		"default_landing_page",
	}
	for _, field := range allowedFields {
		fmt.Printf("  - %s\n", field)
	}
	fmt.Println()

	fmt.Println("❌ RESTRICTED FIELDS (Admin only, not accessible via frontend):")
	restrictedFields := []string{
		"client_type",
		"status",
		"created_by_id",
		"referred_by_id",
		"staff_id",
		"country_id",
		"currency_id",
		"billing_cycle",
		"billing_day",
		"is_lifetime",
		"next_billing_date",
		"last_billed_at",
		"suspended_at",
	}
	for _, field := range restrictedFields {
		fmt.Printf("  - %s\n", field)
	}
}

// Example of making actual HTTP request (commented out for safety)
func makeUpdateClientRequest(token string, clientData map[string]interface{}) error {
	jsonData, err := json.Marshal(clientData)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("PUT", "http://localhost:9001/api/v1/frontend/clients", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	fmt.Printf("Response Status: %s\n", resp.Status)
	return nil
}

func removedMethodsInfo() {
	fmt.Println("=== REMOVED METHODS (No longer available in frontend) ===")
	fmt.Println()

	fmt.Println("The following methods were removed for security and simplicity:")
	fmt.Println()

	fmt.Println("❌ GetAllClients")
	fmt.Println("   - Frontend users don't need to see all clients")
	fmt.Println("   - Security: Prevents data exposure")
	fmt.Println()

	fmt.Println("❌ GetClientById")
	fmt.Println("   - Frontend users only need their own client info")
	fmt.Println("   - Alternative: Client info available via user profile")
	fmt.Println()

	fmt.Println("❌ CreateClient")
	fmt.Println("   - Client creation handled during user registration")
	fmt.Println("   - Prevents duplicate client creation")
	fmt.Println()

	fmt.Println("❌ SearchClients")
	fmt.Println("   - Not needed for frontend users")
	fmt.Println("   - Reduces API surface area")
	fmt.Println()

	fmt.Println("✅ UpdateClient (KEPT & IMPROVED)")
	fmt.Println("   - Now works with current user's client only")
	fmt.Println("   - No ID parameter needed")
	fmt.Println("   - Email uniqueness validation")
	fmt.Println("   - Limited to profile fields")
}

func comparisonWithBackend() {
	fmt.Println("=== Frontend vs Backend Client Update ===")
	fmt.Println()

	fmt.Println("FRONTEND (PUT /api/v1/frontend/clients):")
	fmt.Println("✅ Users update their own client only")
	fmt.Println("✅ No ID parameter needed")
	fmt.Println("✅ Limited profile fields")
	fmt.Println("✅ Email uniqueness validation")
	fmt.Println("✅ Secure and focused")
	fmt.Println()

	fmt.Println("BACKEND (PUT /api/v1/backend/clients/:id):")
	fmt.Println("✅ Admins can update any client")
	fmt.Println("✅ Requires client ID parameter")
	fmt.Println("✅ Full access to all fields")
	fmt.Println("✅ Admin-level control")
	fmt.Println("✅ Billing and system fields")
}
