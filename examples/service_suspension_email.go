package examples

import (
	"fmt"
	"log"
	"time"
	"yotracker/internal/mail"
	"yotracker/internal/models"
)

func ExampleServiceSuspensionEmail() {
	fmt.Println("Example 1: Service suspension email")

	// Create sample client
	client := models.Client{
		Name:  "<PERSON>",
		Email: "<EMAIL>",
	}

	// Create sample currency
	currency := models.Currency{
		Symbol: "$",
		Code:   "USD",
	}

	// Create sample overdue invoices
	dueDate1 := time.Now().AddDate(0, 0, -15)
	dueDate2 := time.Now().AddDate(0, 0, -20)

	reference1 := "INV-2025/001"
	reference2 := "INV-2025/002"
	balance1 := 150.00
	balance2 := 250.00

	overdueInvoices := []models.Invoice{
		{
			Reference: &reference1,
			DueDate:   &dueDate1,
			Balance:   &balance1,
			Currency:  currency,
		},
		{
			Reference: &reference2,
			DueDate:   &dueDate2,
			Balance:   &balance2,
			Currency:  currency,
		},
	}

	// Test the service suspension email
	fmt.Println("Testing Service Suspension Email...")
	err := mail.SendServiceSuspensionEmail(client, overdueInvoices)
	if err != nil {
		log.Printf("Failed to send service suspension email: %v", err)
	} else {
		fmt.Println("Service suspension email sent successfully!")
		fmt.Printf("Email would be sent to: %s\n", client.Email)
		fmt.Printf("Total overdue amount: $%.2f\n", balance1+balance2)
		fmt.Printf("Number of overdue invoices: %d\n", len(overdueInvoices))
	}
}
