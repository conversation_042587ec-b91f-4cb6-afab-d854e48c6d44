package examples

import (
	"fmt"
	"log"
	"os"
	"time"
	"yotracker/internal/models"
	"yotracker/internal/service"
)

func SlackSimpleExample() {
	fmt.Println("=== Simple Slack Service Demo ===")

	// Set up test environment
	os.Setenv("GO_ENV", "test")
	os.Setenv("SLACK_CLIENT_ID", "demo-client-id")
	os.Setenv("SLACK_CLIENT_SECRET", "demo-client-secret")
	os.Setenv("SLACK_BOT_TOKEN", "xoxb-demo-token")

	// Initialize Slack service
	slackService, err := service.NewSlackService()
	if err != nil {
		log.Fatalf("Failed to initialize Slack service: %v", err)
	}

	fmt.Printf("✓ Slack service initialized\n")
	fmt.Printf("  Default channel: %s\n", slackService.GetDefaultChannel())

	// Example 1: Simple alert
	fmt.Println("\n1. Sending simple alert...")
	alert := &models.Alert{
		DeviceId:       func() *string { s := "DEMO123"; return &s }(),
		AlertType:      "speed_violation",
		AlertName:      func() *string { s := "Speed Limit Exceeded"; return &s }(),
		Message:        func() *string { s := "Vehicle exceeded speed limit of 60 km/h on Main Street"; return &s }(),
		Speed:          func() *float64 { f := 75.5; return &f }(),
		Direction:      func() *string { s := "North"; return &s }(),
		AlertTimestamp: time.Now(),
	}

	clientDevice := &models.ClientDevice{
		Name:        stringPtrSimple("Demo Vehicle"),
		PlateNumber: stringPtrSimple("DEMO-001"),
		DeviceId:    "DEMO123",
	}

	err = slackService.SendAlert(alert, clientDevice)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Println("✓ Alert sent successfully")
	}

	// Example 2: Custom message
	fmt.Println("\n2. Sending custom message...")
	blocks := []service.SlackBlock{
		{
			Type: "header",
			Text: &service.SlackText{
				Type: "plain_text",
				Text: "🚀 Demo Notification",
			},
		},
		{
			Type: "section",
			Text: &service.SlackText{
				Type: "mrkdwn",
				Text: "This is a *demo message* from YoTracker Slack service.",
			},
		},
	}

	err = slackService.SendCustomMessage("#general", "Demo Message", blocks)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Println("✓ Custom message sent successfully")
	}

	// Example 3: Webhook message
	fmt.Println("\n3. Sending webhook message...")
	webhookURL := "https://hooks.slack.com/services/demo/webhook/url"
	message := &service.SlackMessage{
		Text: "Demo webhook message",
		Blocks: []service.SlackBlock{
			{
				Type: "section",
				Text: &service.SlackText{
					Type: "plain_text",
					Text: "This message was sent via webhook.",
				},
			},
		},
	}

	err = slackService.SendToWebhook(webhookURL, message)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Println("✓ Webhook message sent successfully")
	}

	fmt.Println("\n=== Demo Complete ===")
	fmt.Println("Note: In test mode, no actual messages are sent to Slack.")
}

func stringPtrSimple(s string) *string {
	return &s
}
