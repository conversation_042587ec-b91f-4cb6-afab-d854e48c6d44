package examples

import (
	"fmt"
	"log"
	"time"
	"yotracker/internal/service"
)

// ExampleCommunicationCampaignLogs demonstrates the communication campaign logging system
func ExampleCommunicationCampaignLogs() {
	fmt.Println("=== Communication Campaign Logs Example ===")
	fmt.Println()

	// Create communication campaign log service
	logService := service.NewCommunicationCampaignLogService()

	// Example 1: Log successful SMS
	fmt.Println("1. Logging Successful SMS:")
	clientId := uint(1)
	err := logService.LogSMS(
		&clientId,       // Client ID
		"+263771234567", // Phone number
		"Device Toyota Hilux (ABC123) entered geofence 'Home Zone'", // Message
		true, // Success
		"",   // No error
		map[string]interface{}{ // Metadata
			"alert_id":   123,
			"alert_type": "geofence",
			"device_id":  "GT06_001",
		},
	)
	if err != nil {
		log.Printf("Failed to log SMS: %v", err)
	} else {
		fmt.Println("✓ SMS logged successfully")
	}
	fmt.Println()

	// Example 2: Log failed WhatsApp message
	fmt.Println("2. Logging Failed WhatsApp:")
	err = logService.LogWhatsApp(
		&clientId,       // Client ID
		"+263771234567", // Phone number
		"Device Toyota Hilux (ABC123) exited geofence 'Home Zone'", // Message
		false,                         // Failed
		"Invalid phone number format", // Error message
		map[string]interface{}{ // Metadata
			"alert_id":   124,
			"alert_type": "geofence",
			"device_id":  "GT06_001",
		},
	)
	if err != nil {
		log.Printf("Failed to log WhatsApp: %v", err)
	} else {
		fmt.Println("✓ WhatsApp failure logged successfully")
	}
	fmt.Println()

	// Example 3: Log successful email
	fmt.Println("3. Logging Successful Email:")
	emailContent := `Hello John Doe,

A device alert has been triggered:
Device: Toyota Hilux (ABC123)
Alert Type: geofence
Alert Message: Device Toyota Hilux (ABC123) entered geofence 'Home Zone'

View Device: https://app.yotracker.co.zw/devices/123`

	err = logService.LogEmail(
		&clientId,           // Client ID
		"<EMAIL>", // Email address
		emailContent,        // Full email content
		true,                // Success
		"",                  // No error
		map[string]interface{}{ // Metadata
			"alert_id":   125,
			"alert_type": "geofence",
			"device_id":  "GT06_001",
			"subject":    "Device Alert: Geofence Alert",
		},
	)
	if err != nil {
		log.Printf("Failed to log email: %v", err)
	} else {
		fmt.Println("✓ Email logged successfully")
	}
	fmt.Println()

	// Example 4: Get communication statistics
	fmt.Println("4. Getting Communication Statistics:")
	statistics, err := logService.GetCommunicationStatistics(
		&clientId, // Client ID
		nil,       // Start date (nil for all time)
		nil,       // End date (nil for all time)
	)
	if err != nil {
		log.Printf("Failed to get statistics: %v", err)
	} else {
		fmt.Printf("✓ Communication Statistics:\n")
		fmt.Printf("  Total Messages: %d\n", statistics.TotalMessages)
		fmt.Printf("  SMS Count: %d\n", statistics.SmsCount)
		fmt.Printf("  WhatsApp Count: %d\n", statistics.WhatsappCount)
		fmt.Printf("  Email Count: %d\n", statistics.EmailCount)
		fmt.Printf("  Successful: %d\n", statistics.SuccessfulCount)
		fmt.Printf("  Failed: %d\n", statistics.FailedCount)
	}
	fmt.Println()

	// Example 5: Manual logging with custom status
	fmt.Println("5. Manual Logging with Custom Status:")
	err = logService.LogCommunication(
		&clientId,       // Client ID
		"+263771234567", // Recipient
		"sms",           // Campaign type
		"Test message",  // Message
		"pending",       // Status
		nil,             // No error message
		map[string]interface{}{ // Metadata
			"custom_field": "custom_value",
			"timestamp":    time.Now().Unix(),
		},
	)
	if err != nil {
		log.Printf("Failed to log communication: %v", err)
	} else {
		fmt.Println("✓ Custom communication logged successfully")
	}

	fmt.Println("\n=== Key Features ===")
	fmt.Println("✅ Automatic logging of all SMS, WhatsApp, and Email communications")
	fmt.Println("✅ Success/failure tracking with error messages")
	fmt.Println("✅ Rich metadata support for analytics")
	fmt.Println("✅ Client-specific statistics and reporting")
	fmt.Println("✅ Integration with existing alert services")
	fmt.Println("✅ Future-ready for campaign management")
}

// ExampleAlertIntegration shows how to integrate with alert services
func ExampleAlertIntegration() {
	fmt.Println("=== Alert Integration Example ===")
	fmt.Println()

	// This would typically be called from your alert services
	logService := service.NewCommunicationCampaignLogService()

	// When sending an SMS alert
	phoneNumber := "+263771234567"
	message := "Device alert triggered"
	success := true
	errorMsg := ""
	clientId := uint(1)

	if success {
		logService.LogSMS(
			&clientId,
			phoneNumber,
			message,
			true,
			"",
			map[string]interface{}{
				"alert_id":   123,
				"alert_type": "geofence",
			},
		)
		fmt.Println("✓ SMS alert logged successfully")
	} else {
		logService.LogSMS(
			&clientId,
			phoneNumber,
			message,
			false,
			errorMsg,
			map[string]interface{}{
				"alert_id":   123,
				"alert_type": "geofence",
			},
		)
		fmt.Println("✓ SMS alert failure logged")
	}
}
