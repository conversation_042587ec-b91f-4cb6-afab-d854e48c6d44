package examples

import (
	"fmt"
	"log"
	"yotracker/internal/mail"
)

func ExampleClientWelcomeEmail() {
	// Example 1: Client welcome email when creating a new client
	fmt.Println("Example 1: Client welcome email")
	err := mail.SendWelcomeEmail("<EMAIL>", "ABC Corporation")
	if err != nil {
		log.Printf("Failed to send client welcome email: %v", err)
	} else {
		fmt.Println("Client welcome email sent successfully!")
	}

	// Example 2: Multiple client welcome emails
	fmt.Println("\nExample 2: Multiple client welcome emails")
	clients := []struct {
		Email string
		Name  string
	}{
		{"<EMAIL>", "Tech Startup Inc"},
		{"<EMAIL>", "Logistics Solutions Ltd"},
		{"<EMAIL>", "Manufacturing Corp"},
	}

	for _, client := range clients {
		err := mail.SendWelcomeEmail(client.Email, client.Name)
		if err != nil {
			log.Printf("Failed to send welcome email to %s: %v", client.Name, err)
		} else {
			fmt.Printf("Welcome email sent to %s (%s)\n", client.Name, client.Email)
		}
	}

	fmt.Println("\nAll client welcome emails completed!")
	fmt.Println("\nThe client welcome emails feature:")
	fmt.Println("✅ Professional Laravel-style design")
	fmt.Println("✅ Personalized greeting with client name")
	fmt.Println("✅ Welcome message and next steps")
	fmt.Println("✅ 'Get Started' action button")
	fmt.Println("✅ Both HTML and plain text versions")
	fmt.Println("✅ Automatic sending on client creation")
}

// Example of how this integrates with your client creation workflow
func CreateClientWithWelcomeEmail(name, email, phone, address string) error {
	// This simulates your client creation process
	fmt.Printf("Creating client: %s (%s)\n", name, email)

	// After successful client creation in database, send welcome email
	err := mail.SendWelcomeEmail(email, name)
	if err != nil {
		return fmt.Errorf("client created but failed to send welcome email: %v", err)
	}

	fmt.Printf("Client created and welcome email sent successfully to %s\n", email)
	return nil
}

// Example for different types of clients
func CreateDifferentClientTypes() {
	fmt.Println("\n--- Different Client Types Examples ---")

	// Enterprise client
	fmt.Println("\n1. Enterprise Client:")
	err := CreateClientWithWelcomeEmail(
		"Enterprise Solutions Inc",
		"<EMAIL>",
		"******-0123",
		"123 Business Ave, Corporate City",
	)
	if err != nil {
		log.Printf("Error: %v", err)
	}

	// Small business client
	fmt.Println("\n2. Small Business Client:")
	err = CreateClientWithWelcomeEmail(
		"Local Bakery",
		"<EMAIL>",
		"******-0456",
		"456 Main St, Small Town",
	)
	if err != nil {
		log.Printf("Error: %v", err)
	}

	// Startup client
	fmt.Println("\n3. Startup Client:")
	err = CreateClientWithWelcomeEmail(
		"Tech Innovators LLC",
		"<EMAIL>",
		"******-0789",
		"789 Innovation Blvd, Tech Hub",
	)
	if err != nil {
		log.Printf("Error: %v", err)
	}
}

// Example showing the welcome email content structure
func ShowWelcomeEmailStructure() {
	fmt.Println("\n--- Welcome Email Structure Example ---")
	fmt.Println("The client welcome email includes:")
	fmt.Println("📧 Subject: 'Welcome to YoTracker!'")
	fmt.Println("👋 Greeting: 'Welcome to YoTracker, [Client Name]!'")
	fmt.Println("📝 Content:")
	fmt.Println("   • Thank you for joining YoTracker")
	fmt.Println("   • Excited to have you on board")
	fmt.Println("   • Start tracking devices and managing fleet")
	fmt.Println("   • Support team contact information")
	fmt.Println("🔗 Action Button: 'Get Started' (links to dashboard)")
	fmt.Println("📱 Responsive design for all devices")
	fmt.Println("🎨 Professional Laravel-style template")
}

// Example of the backend controller integration
func ShowBackendIntegration() {
	fmt.Println("\n--- Backend Controller Integration ---")
	fmt.Println("When a client is created via POST /clients:")
	fmt.Println("1. Client data is validated")
	fmt.Println("2. Client is saved to database")
	fmt.Println("3. Welcome email is sent asynchronously")
	fmt.Println("4. Response includes email confirmation")
	fmt.Println("")
	fmt.Println("Response example:")
	fmt.Println(`{
  "message": "Client created successfully",
  "email": "Welcome email will be sent shortly"
}`)
}

// Example of error handling
func ShowErrorHandling() {
	fmt.Println("\n--- Error Handling Example ---")
	fmt.Println("✅ Non-blocking: Client creation succeeds even if email fails")
	fmt.Println("✅ Logging: Email errors are logged for monitoring")
	fmt.Println("✅ User feedback: Response indicates email will be sent")
	fmt.Println("✅ Graceful degradation: Core functionality not affected")
}

// RunClientWelcomeEmailExamples runs all client welcome email examples
func RunClientWelcomeEmailExamples() {
	ExampleClientWelcomeEmail()
	CreateDifferentClientTypes()
	ShowWelcomeEmailStructure()
	ShowBackendIntegration()
	ShowErrorHandling()
}
