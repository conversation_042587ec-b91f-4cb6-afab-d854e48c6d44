package examples

import (
	"fmt"
	"log"
	"yotracker/internal/mail"
	"yotracker/internal/service"
)

func ExampleNewAccountEmail() {
	// Example 1: Using the helper function for new account email
	fmt.Println("Example 1: New account email using helper function")
	err := mail.SendNewAccountEmail(
		"<EMAIL>",
		"John Doe",
		"TempPassword123!",
	)
	if err != nil {
		log.Printf("Failed to send new account email: %v", err)
	} else {
		fmt.Println("New account email sent successfully!")
	}

	// Example 2: Using the service directly for more control
	fmt.Println("\nExample 2: New account email using service directly")
	mailService, err := service.NewMailService()
	if err != nil {
		log.Printf("Failed to create mail service: %v", err)
		return
	}

	err = mailService.SendNewAccountEmail(
		"<EMAIL>",
		"<PERSON> Smith",
		"AdminPass456!",
		"https://yourdomain.com/admin/login",
	)
	if err != nil {
		log.Printf("Failed to send admin account email: %v", err)
	} else {
		fmt.Println("Admin account email sent successfully!")
	}

	// Example 3: Custom new account email with template
	fmt.Println("\nExample 3: Custom new account email with template")
	err = mailService.SendTemplatedEmail("<EMAIL>", "Your Developer Account", func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting("Welcome to the YoTracker Development Team!").
			AddContent(
				"A new developer account has been created for you.",
				"Here are your login credentials:",
				"Email: <EMAIL>",
				"Password: DevPass789!",
				"You now have access to:",
				"• Development environment",
				"• API documentation",
				"• Code repositories",
				"• Testing tools",
				"Please log in and change your password immediately for security reasons.",
				"If you have any questions, please contact the development team lead.",
			).
			SetAction("Access Developer Portal", template.AppURL+"/dev/login")
	})

	if err != nil {
		log.Printf("Failed to send developer account email: %v", err)
	} else {
		fmt.Println("Developer account email sent successfully!")
	}

	fmt.Println("\nAll new account email examples completed!")
	fmt.Println("\nThe new account emails feature:")
	fmt.Println("✅ Professional Laravel-style design")
	fmt.Println("✅ Clear credential display")
	fmt.Println("✅ Security reminder to change password")
	fmt.Println("✅ Login action button")
	fmt.Println("✅ Both HTML and plain text versions")
	fmt.Println("✅ Automatic integration with user creation")
}

// Example of how this integrates with your user creation workflow
func CreateUserWithEmailNotification(name, email, password, userType string) error {
	// This simulates your user creation process
	fmt.Printf("Creating user: %s (%s) with type: %s\n", name, email, userType)

	// After successful user creation in database, send email
	err := mail.SendNewAccountEmail(email, name, password)
	if err != nil {
		return fmt.Errorf("user created but failed to send email: %v", err)
	}

	fmt.Printf("User created and email sent successfully to %s\n", email)
	return nil
}

// Example for backend user creation
func CreateBackendUser() {
	fmt.Println("\n--- Backend User Creation Example ---")
	err := CreateUserWithEmailNotification(
		"Backend Admin",
		"<EMAIL>",
		"SecureAdminPass123!",
		"backend",
	)
	if err != nil {
		log.Printf("Error: %v", err)
	}
}

// Example for frontend/client user creation
func CreateClientUser() {
	fmt.Println("\n--- Client User Creation Example ---")
	err := CreateUserWithEmailNotification(
		"Client Manager",
		"<EMAIL>",
		"ClientPass456!",
		"client",
	)
	if err != nil {
		log.Printf("Error: %v", err)
	}
}

// Example for bulk user creation
func CreateBulkUsers() {
	fmt.Println("\n--- Bulk User Creation Example ---")

	users := []struct {
		Name     string
		Email    string
		Password string
		Type     string
	}{
		{"John Smith", "<EMAIL>", "TempPass1!", "client"},
		{"Sarah Johnson", "<EMAIL>", "TempPass2!", "client"},
		{"Mike Wilson", "<EMAIL>", "TempPass3!", "client"},
	}

	for _, user := range users {
		err := CreateUserWithEmailNotification(user.Name, user.Email, user.Password, user.Type)
		if err != nil {
			log.Printf("Failed to create user %s: %v", user.Email, err)
		}
	}
}

// Example showing the email content structure
func ShowEmailStructure() {
	fmt.Println("\n--- Email Structure Example ---")
	fmt.Println("The new account email includes:")
	fmt.Println("📧 Subject: 'Your New YoTracker Account'")
	fmt.Println("👋 Greeting: 'Hello [Name],'")
	fmt.Println("📝 Content:")
	fmt.Println("   • Account creation notification")
	fmt.Println("   • Login credentials (email & password)")
	fmt.Println("   • Security reminder")
	fmt.Println("   • Support contact information")
	fmt.Println("🔗 Action Button: 'Login to YoTracker'")
	fmt.Println("📱 Responsive design for all devices")
	fmt.Println("🔒 Professional security messaging")
}

// RunNewAccountEmailExamples runs all new account email examples
func RunNewAccountEmailExamples() {
	ExampleNewAccountEmail()
	CreateBackendUser()
	CreateClientUser()
	CreateBulkUsers()
	ShowEmailStructure()
}
