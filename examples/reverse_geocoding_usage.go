package examples

import (
	"fmt"
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/service"
)

func ReverseGeocodingExample() {
	// Initialize database
	config.InitDB()

	// Create reverse geocoding service with default batch size (20)
	geocodingService := service.NewReverseGeocodingService()

	// Alternative: Create service with custom batch size
	// geocodingService := service.NewReverseGeocodingServiceWithBatchSize(50)

	// Example 1: Get location name for specific coordinates
	fmt.Println("=== Example 1: Get location name for specific coordinates ===")
	latitude := -17.8216
	longitude := 31.0492

	locationName, err := geocodingService.GetLocationName(latitude, longitude)
	if err != nil {
		log.Printf("Error getting location name: %v", err)
	} else {
		fmt.Printf("Coordinates (%.6f, %.6f): %s\n", latitude, longitude, locationName)
	}

	// Example 2: Update GPS data location names
	fmt.Println("\n=== Example 2: Update GPS data location names ===")
	err = geocodingService.UpdateGPSDataLocationNames()
	if err != nil {
		log.Printf("Error updating GPS data location names: %v", err)
	} else {
		fmt.Println("GPS data location names updated successfully")
	}

	// Example 3: Test with different coordinates
	fmt.Println("\n=== Example 3: Test with different coordinates ===")
	testCoords := []struct {
		lat, lon float64
		desc     string
	}{
		{-17.8250, 31.0500, "Near Harare"},
		{-17.8300, 31.0550, "Harare area"},
		{-17.8350, 31.0600, "Further from Harare"},
	}

	for _, coord := range testCoords {
		locationName, err := geocodingService.GetLocationName(coord.lat, coord.lon)
		if err != nil {
			log.Printf("Error getting location name for %s: %v", coord.desc, err)
			continue
		}
		fmt.Printf("%s (%.6f, %.6f): %s\n", coord.desc, coord.lat, coord.lon, locationName)
	}

	// Example 4: Demonstrate caching
	fmt.Println("\n=== Example 4: Demonstrate caching ===")

	// Get cache stats before testing
	stats := geocodingService.GetCacheStats()
	fmt.Printf("Cache stats before testing: %+v\n", stats)

	// Test the same coordinates again - should use cache
	fmt.Println("Testing same coordinates again (should use cache):")
	for _, coord := range testCoords {
		startTime := time.Now()
		locationName, err := geocodingService.GetLocationName(coord.lat, coord.lon)
		elapsed := time.Since(startTime)
		if err != nil {
			log.Printf("Error getting location name for %s: %v", coord.desc, err)
			continue
		}
		fmt.Printf("%s (%.6f, %.6f): %s (took %v)\n", coord.desc, coord.lat, coord.lon, locationName, elapsed)
	}

	// Get cache stats after testing
	stats = geocodingService.GetCacheStats()
	fmt.Printf("Cache stats after testing: %+v\n", stats)

	// Example 5: Test nearby coordinates (should use cache)
	fmt.Println("\n=== Example 5: Test nearby coordinates (should use cache) ===")
	nearbyCoords := []struct {
		lat, lon float64
		desc     string
	}{
		{-17.8217, 31.0493, "Very near Harare (11m away)"},
		{-17.8251, 31.0501, "Very near second location (11m away)"},
	}

	for _, coord := range nearbyCoords {
		startTime := time.Now()
		locationName, err := geocodingService.GetLocationName(coord.lat, coord.lon)
		elapsed := time.Since(startTime)
		if err != nil {
			log.Printf("Error getting location name for %s: %v", coord.desc, err)
			continue
		}
		fmt.Printf("%s (%.6f, %.6f): %s (took %v)\n", coord.desc, coord.lat, coord.lon, locationName, elapsed)
	}
}
