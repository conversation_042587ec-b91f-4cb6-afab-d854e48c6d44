package examples

import (
	"fmt"
	"log"
	"time"
	"yotracker/internal/mail"
	"yotracker/internal/models"
)

func ExampleInvoiceReminderEmails() {
	fmt.Println("Example 1: Invoice reminder emails")

	// Create sample client
	client := models.Client{
		Name:  "<PERSON>",
		Email: "<EMAIL>",
	}

	// Create sample currency
	currency := models.Currency{
		Symbol: "$",
		Code:   "USD",
	}

	// Create sample overdue invoices for different reminder types

	// First reminder (2 days overdue)
	firstOverdueDate := time.Now().AddDate(0, 0, -2)
	firstInvoice := models.Invoice{
		Id:        1,
		Client:    client,
		Currency:  currency,
		Reference: func() *string { r := "INV-2025/001"; return &r }(),
		DueDate:   &firstOverdueDate,
		Balance:   func() *float64 { b := 150.00; return &b }(),
	}

	// Second reminder (5 days overdue)
	secondOverdueDate := time.Now().AddDate(0, 0, -5)
	secondInvoice := models.Invoice{
		Id:        2,
		Client:    client,
		Currency:  currency,
		Reference: func() *string { r := "INV-2025/002"; return &r }(),
		DueDate:   &secondOverdueDate,
		Balance:   func() *float64 { b := 250.00; return &b }(),
	}

	// Third reminder (10 days overdue)
	thirdOverdueDate := time.Now().AddDate(0, 0, -10)
	thirdInvoice := models.Invoice{
		Id:        3,
		Client:    client,
		Currency:  currency,
		Reference: func() *string { r := "INV-2025/003"; return &r }(),
		DueDate:   &thirdOverdueDate,
		Balance:   func() *float64 { b := 500.00; return &b }(),
	}

	fmt.Println("Testing Invoice Reminder Emails...")
	fmt.Println("=====================================")

	// Test first overdue reminder
	fmt.Println("\n1. Testing First Overdue Reminder (Gentle):")
	fmt.Printf("   Invoice: %s\n", *firstInvoice.Reference)
	fmt.Printf("   Due Date: %s\n", firstOverdueDate.Format("January 2, 2006"))
	fmt.Printf("   Amount: $%.2f\n", *firstInvoice.Balance)

	err := mail.SendFirstOverdueReminderEmail(firstInvoice)
	if err != nil {
		log.Printf("Failed to send first overdue reminder: %v", err)
	} else {
		fmt.Println("   ✅ First overdue reminder sent successfully!")
	}

	// Test second overdue reminder
	fmt.Println("\n2. Testing Second Overdue Reminder (Urgent):")
	fmt.Printf("   Invoice: %s\n", *secondInvoice.Reference)
	fmt.Printf("   Due Date: %s\n", secondOverdueDate.Format("January 2, 2006"))
	fmt.Printf("   Amount: $%.2f\n", *secondInvoice.Balance)

	err = mail.SendSecondOverdueReminderEmail(secondInvoice)
	if err != nil {
		log.Printf("Failed to send second overdue reminder: %v", err)
	} else {
		fmt.Println("   ⚠️  Second overdue reminder sent successfully!")
	}

	// Test third overdue reminder
	fmt.Println("\n3. Testing Third Overdue Reminder (Final Warning):")
	fmt.Printf("   Invoice: %s\n", *thirdInvoice.Reference)
	fmt.Printf("   Due Date: %s\n", thirdOverdueDate.Format("January 2, 2006"))
	fmt.Printf("   Amount: $%.2f\n", *thirdInvoice.Balance)

	err = mail.SendThirdOverdueReminderEmail(thirdInvoice)
	if err != nil {
		log.Printf("Failed to send third overdue reminder: %v", err)
	} else {
		fmt.Println("   🚨 Third overdue reminder sent successfully!")
	}

	fmt.Println("\n=====================================")
	fmt.Println("Email Reminder Testing Complete!")
	fmt.Printf("All emails would be sent to: %s\n", client.Email)
	fmt.Println("\nReminder Schedule:")
	fmt.Println("- First Reminder: 2 days after due date (gentle)")
	fmt.Println("- Second Reminder: 5 days after due date (urgent)")
	fmt.Println("- Third Reminder: 10 days after due date (final warning)")
	fmt.Println("- Service Suspension: After third reminder period")
}
