package examples

import (
	"fmt"
	"log"
	"os"
	"yotracker/internal/service"
)

// TestSlackToken validates a Slack bot token
func TestSlackToken() {
	fmt.Println("=== Slack Bot Token Validation ===")

	// Get token from environment
	botToken := os.Getenv("SLACK_BOT_TOKEN")
	if botToken == "" {
		fmt.Println("❌ No SLACK_BOT_TOKEN environment variable set")
		fmt.Println("Please set your bot token: export SLACK_BOT_TOKEN=xoxb-your-token-here")
		return
	}

	fmt.Printf("✓ Found bot token: %s...%s\n", botToken[:10], botToken[len(botToken)-4:])

	// Validate token format
	if len(botToken) < 20 || botToken[:5] != "xoxb-" {
		fmt.Println("❌ Invalid token format. Bot tokens should start with 'xoxb-'")
		return
	}

	fmt.Println("✓ Token format looks correct")

	// Initialize Slack service
	slackService, err := service.NewSlackService()
	if err != nil {
		log.Printf("❌ Failed to initialize Slack service: %v", err)
		return
	}

	fmt.Println("✓ Slack service initialized")

	// Test the bot token
	fmt.Println("\nTesting bot token with Slack API...")
	err = slackService.TestBotToken()
	if err != nil {
		fmt.Printf("❌ Bot token validation failed: %v\n", err)
		fmt.Println("\n🔧 Troubleshooting steps:")
		fmt.Println("1. Verify your bot token is correct")
		fmt.Println("2. Check that the bot app is installed in your workspace")
		fmt.Println("3. Ensure the bot has 'chat:write' scope")
		fmt.Println("4. Try regenerating the bot token if needed")
		return
	}

	fmt.Println("✅ Bot token is valid and working!")

	// Test sending a simple message
	fmt.Println("\nTesting message sending...")

	// Try to send to a test channel
	testChannel := os.Getenv("SLACK_TEST_CHANNEL")
	if testChannel == "" {
		testChannel = "#general" // Default to general channel
	}

	blocks := []service.SlackBlock{
		{
			Type: "section",
			Text: &service.SlackText{
				Type: "mrkdwn",
				Text: "🧪 *Test message from YoTracker*\n\nThis is a test to verify Slack integration is working correctly.",
			},
		},
		{
			Type: "context",
			Elements: []service.SlackElement{
				{
					Type: "mrkdwn",
					Text: &service.SlackText{
						Type: "mrkdwn",
						Text: "If you see this message, your Slack integration is working! 🎉",
					},
				},
			},
		},
	}

	err = slackService.SendCustomMessage(testChannel, "YoTracker Test Message", blocks)
	if err != nil {
		fmt.Printf("❌ Failed to send test message: %v\n", err)
		fmt.Println("\n🔧 Possible issues:")
		fmt.Println("1. Bot is not added to the target channel")
		fmt.Println("2. Channel name is incorrect (try using channel ID)")
		fmt.Println("3. Bot lacks permission to post in the channel")
		fmt.Printf("4. Try adding bot to %s channel first\n", testChannel)
		return
	}

	fmt.Printf("✅ Test message sent successfully to %s!\n", testChannel)
	fmt.Println("\n🎉 Slack integration is fully working!")
	fmt.Println("You can now use Slack notifications for your alerts.")
}

// ValidateSlackSetup provides a comprehensive setup validation
func ValidateSlackSetup() {
	fmt.Println("=== Slack Setup Validation ===")

	// Check environment variables
	botToken := os.Getenv("SLACK_BOT_TOKEN")
	webhookURL := os.Getenv("SLACK_WEBHOOK_URL")

	if botToken == "" && webhookURL == "" {
		fmt.Println("❌ No Slack configuration found")
		fmt.Println("Set either SLACK_BOT_TOKEN or SLACK_WEBHOOK_URL")
		return
	}

	if botToken != "" {
		fmt.Println("✓ Bot token configured")
		TestSlackToken()
	} else if webhookURL != "" {
		fmt.Println("✓ Webhook URL configured")
		fmt.Println("Note: Webhook testing requires actual webhook URL")
	}
}
