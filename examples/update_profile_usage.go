package examples

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

// Example demonstrating the difference between UpdateUser and UpdateProfile

func ExampleUpdateProfile() {
	fmt.Println("=== Update Profile vs Update User Examples ===")
	fmt.Println()

	// Example 1: UpdateProfile - User updates their own profile
	fmt.Println("1. UpdateProfile - User updates their own profile:")
	updateProfileExample()
	fmt.Println()

	// Example 2: UpdateUser - Admin updates any user's profile
	fmt.Println("2. UpdateUser - Admin updates any user's profile:")
	updateUserExample()
	fmt.Println()

	fmt.Println("=== Key Differences ===")
	fmt.Println("UpdateProfile:")
	fmt.Println("- Users can only update their own profile")
	fmt.Println("- No password field (use ChangePassword endpoint)")
	fmt.Println("- No admin fields (UserType, Status, ClientId)")
	fmt.Println("- Email and username uniqueness validation")
	fmt.Println("- Endpoint: PUT /users/profile")
	fmt.Println()
	fmt.Println("UpdateUser:")
	fmt.Println("- Admins can update any user's profile")
	fmt.Println("- Includes password field")
	fmt.Println("- Includes admin fields (UserType, Status, ClientId)")
	fmt.Println("- Requires user ID in URL")
	fmt.Println("- Endpoint: PUT /users/:id")
}

func updateProfileExample() {
	// UpdateProfile request body
	profileRequest := map[string]interface{}{
		"name":              "John Doe",
		"email":             "<EMAIL>",
		"username":          "johndoe",
		"gender":            "male",
		"telegram_user_id":  "123456789",
		"slack_webhook_url": "https://hooks.slack.com/services/...",
		"description":       "Software Developer",
	}

	jsonData, _ := json.MarshalIndent(profileRequest, "", "  ")
	fmt.Printf("PUT /api/v1/frontend/users/profile\n")
	fmt.Printf("Authorization: Bearer <user_token>\n")
	fmt.Printf("Content-Type: application/json\n\n")
	fmt.Printf("%s\n", string(jsonData))

	// Simulate the request (for demonstration only)
	simulateUpdateProfileRequest(profileRequest)
}

func updateUserExample() {
	// UpdateUser request body (admin updating user ID 123)
	userRequest := map[string]interface{}{
		"client_id":         1,
		"user_type":         "client",
		"name":              "Jane Smith",
		"email":             "<EMAIL>",
		"password":          "newpassword123", // Optional
		"username":          "janesmith",
		"gender":            "female",
		"telegram_user_id":  "987654321",
		"slack_webhook_url": "https://hooks.slack.com/services/...",
		"status":            "active",
		"description":       "Project Manager",
	}

	jsonData, _ := json.MarshalIndent(userRequest, "", "  ")
	fmt.Printf("PUT /api/v1/backend/users/123\n")
	fmt.Printf("Authorization: Bearer <admin_token>\n")
	fmt.Printf("Content-Type: application/json\n\n")
	fmt.Printf("%s\n", string(jsonData))

	// Simulate the request (for demonstration only)
	simulateUpdateUserRequest(userRequest)
}

func simulateUpdateProfileRequest(data map[string]interface{}) {
	fmt.Println("\n--- Simulated Response ---")

	// Simulate email uniqueness check
	if email, ok := data["email"].(string); ok {
		if email == "<EMAIL>" {
			fmt.Println("HTTP 400 Bad Request")
			fmt.Println(`{"message": "Email already exists"}`)
			return
		}
	}

	// Simulate username uniqueness check
	if username, ok := data["username"].(string); ok {
		if username == "existinguser" {
			fmt.Println("HTTP 400 Bad Request")
			fmt.Println(`{"message": "Username already exists"}`)
			return
		}
	}

	// Simulate successful update
	fmt.Println("HTTP 200 OK")
	fmt.Println(`{"message": "Profile updated successfully"}`)
}

func simulateUpdateUserRequest(data map[string]interface{}) {
	fmt.Println("\n--- Simulated Response ---")

	// Simulate successful admin update
	fmt.Println("HTTP 200 OK")
	fmt.Println(`{"message": "User updated successfully"}`)
}

// Example of making actual HTTP requests (commented out for safety)
func makeUpdateProfileRequest(token string, profileData map[string]interface{}) error {
	jsonData, err := json.Marshal(profileData)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("PUT", "http://localhost:9001/api/v1/frontend/users/profile", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	fmt.Printf("Response Status: %s\n", resp.Status)
	return nil
}

func makeUpdateUserRequest(token string, userID string, userData map[string]interface{}) error {
	jsonData, err := json.Marshal(userData)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("http://localhost:9000/api/v1/backend/users/%s", userID)
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	fmt.Printf("Response Status: %s\n", resp.Status)
	return nil
}
