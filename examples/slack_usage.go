package examples

import (
	"fmt"
	"log"
	"os"
	"time"
	"yotracker/internal/models"
	"yotracker/internal/service"
)

func SlackUsageExample() {
	fmt.Println("=== Slack Service Usage Examples ===")
	fmt.Println()

	// Set up example environment
	setupExampleEnvironment()

	// Example 1: Basic service initialization
	fmt.Println("1. Initializing Slack Service...")
	slackService, err := service.NewSlackService()
	if err != nil {
		log.Printf("Failed to initialize Slack service: %v", err)
		return
	}
	fmt.Printf("✓ Slack service initialized successfully\n")
	fmt.Printf("  - Bot Token configured: %t\n", slackService != nil)
	fmt.Printf("  - Service enabled: %t\n", slackService.IsEnabled())
	fmt.Printf("  - Default channel: %s\n", slackService.GetDefaultChannel())
	fmt.Println()

	// Example 2: Sending a device alert
	fmt.Println("2. Sending Device Alert...")
	alert := &models.Alert{
		DeviceId:       func() *string { s := "DEVICE123"; return &s }(),
		AlertType:      "speed_violation",
		AlertName:      func() *string { s := "Speed Violation"; return &s }(),
		Message:        func() *string { s := "Vehicle exceeded speed limit of 60 km/h"; return &s }(),
		Speed:          func() *float64 { f := 75.5; return &f }(),
		Direction:      func() *string { s := "North"; return &s }(),
		AlertTimestamp: time.Now(),
	}

	// Mock client device
	clientDevice := &models.ClientDevice{
		Name:        stringPtr("Fleet Vehicle 001"),
		PlateNumber: stringPtr("ABC-123"),
		DeviceId:    "DEVICE123",
	}

	err = slackService.SendAlert(alert, clientDevice)
	if err != nil {
		log.Printf("Failed to send alert: %v", err)
	} else {
		fmt.Printf("✓ Alert sent successfully\n")
	}
	fmt.Println()

	// Example 3: Sending custom message with blocks
	fmt.Println("3. Sending Custom Message with Blocks...")
	blocks := []service.SlackBlock{
		{
			Type: "header",
			Text: &service.SlackText{
				Type: "plain_text",
				Text: "🚀 System Notification",
			},
		},
		{
			Type: "section",
			Text: &service.SlackText{
				Type: "mrkdwn",
				Text: "This is a *custom notification* from the YoTracker system.\n\nKey features:\n• Rich text formatting\n• Interactive buttons\n• Professional styling",
			},
		},
		{
			Type: "section",
			Fields: []service.SlackText{
				{
					Type: "mrkdwn",
					Text: "*Status:*\nOperational",
				},
				{
					Type: "mrkdwn",
					Text: "*Uptime:*\n99.9%",
				},
			},
		},
		{
			Type: "actions",
			Elements: []service.SlackElement{
				{
					Type: "button",
					Text: &service.SlackText{
						Type: "plain_text",
						Text: "View Dashboard",
					},
					URL:      "https://yourdomain.com/dashboard",
					ActionID: "view_dashboard",
					Style:    "primary",
				},
				{
					Type: "button",
					Text: &service.SlackText{
						Type: "plain_text",
						Text: "View Reports",
					},
					URL:      "https://yourdomain.com/reports",
					ActionID: "view_reports",
				},
			},
		},
	}

	err = slackService.SendCustomMessage("#general", "System Notification", blocks)
	if err != nil {
		log.Printf("Failed to send custom message: %v", err)
	} else {
		fmt.Printf("✓ Custom message sent successfully\n")
	}
	fmt.Println()

	// Example 4: Sending to specific webhook
	fmt.Println("4. Sending to Specific Webhook...")
	webhookURL := "*****************************************************************************"

	message := &service.SlackMessage{
		Text: "Alert from specific webhook",
		Blocks: []service.SlackBlock{
			{
				Type: "section",
				Text: &service.SlackText{
					Type: "mrkdwn",
					Text: "This message was sent to a *specific webhook URL* rather than using the bot token.",
				},
			},
		},
	}

	err = slackService.SendToWebhook(webhookURL, message)
	if err != nil {
		log.Printf("Failed to send webhook message: %v", err)
	} else {
		fmt.Printf("✓ Webhook message sent successfully\n")
	}
	fmt.Println()

	// Example 5: Testing severity determination
	fmt.Println("5. Testing Alert Severity Determination...")
	testAlerts := []string{
		"critical_engine_failure",
		"warning_low_fuel",
		"emergency_accident",
		"info_maintenance_due",
		"alert_geofence_exit",
		"normal_status_update",
	}

	for _, alertType := range testAlerts {
		severity := determineSeverityExample(alertType)
		color := getSeverityColorExample(severity)
		fmt.Printf("  - %s → %s (%s)\n", alertType, severity, color)
	}
	fmt.Println()

	// Example 6: Raw data logging
	fmt.Println("6. Testing Raw Data Logging...")

	// Example raw hex data from a GPS tracker
	rawHexData := "24244750524D432C3132333435362E3030302C412C323334352E363738392C4E2C31323334352E363738392C452C302E30302C302E30302C3031303132332C2C2A37332323"

	err = slackService.SendRawDataLog("Fleet Vehicle 001", "DEVICE123", rawHexData)
	if err != nil {
		log.Printf("Failed to send raw data log: %v", err)
	} else {
		fmt.Printf("✓ Raw data log sent successfully\n")
	}
	fmt.Println()

	fmt.Println("=== Examples Complete ===")
	fmt.Println("Note: In test mode, no actual messages are sent to Slack.")
	fmt.Println("Configure SLACK_BOT_TOKEN or SLACK_WEBHOOK_URL to send real messages.")
}

func setupExampleEnvironment() {
	// Set test mode to prevent actual API calls
	os.Setenv("GO_ENV", "test")
	os.Setenv("TESTING_DB_NAME", "testing")

	// Set example Slack configuration
	if os.Getenv("SLACK_CLIENT_ID") == "" {
		os.Setenv("SLACK_CLIENT_ID", "example-client-id")
		fmt.Println("Note: Using example client ID. Set SLACK_CLIENT_ID for actual usage.")
	}

	if os.Getenv("SLACK_CLIENT_SECRET") == "" {
		os.Setenv("SLACK_CLIENT_SECRET", "example-client-secret")
		fmt.Println("Note: Using example client secret. Set SLACK_CLIENT_SECRET for actual usage.")
	}

	if os.Getenv("SLACK_BOT_TOKEN") == "" {
		os.Setenv("SLACK_BOT_TOKEN", "xoxb-example-token-for-demo")
		fmt.Println("Note: Using example bot token. Set SLACK_BOT_TOKEN for actual usage.")
	}

	if os.Getenv("SLACK_WEBHOOK_URL") == "" {
		os.Setenv("SLACK_WEBHOOK_URL", "https://hooks.slack.com/services/example/webhook/url")
		fmt.Println("Note: Using example webhook URL. Set SLACK_WEBHOOK_URL for actual usage.")
	}

	if os.Getenv("APP_URL") == "" {
		os.Setenv("APP_URL", "https://yourdomain.com")
	}

	fmt.Println("Environment configured for examples (test mode - no actual messages will be sent)")
	fmt.Println()
}

// Helper functions for examples
func stringPtr(s string) *string {
	return &s
}

func determineSeverityExample(alertType string) string {
	switch {
	case contains(alertType, "critical") || contains(alertType, "emergency"):
		return "critical"
	case contains(alertType, "warning") || contains(alertType, "alert"):
		return "warning"
	default:
		return "info"
	}
}

func getSeverityColorExample(severity string) string {
	switch severity {
	case "critical":
		return "#FF0000" // Red
	case "warning":
		return "#FFA500" // Orange
	default:
		return "#36A64F" // Green
	}
}

func contains(s, substr string) bool {
	// Simple substring check
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
