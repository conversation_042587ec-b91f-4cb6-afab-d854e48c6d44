package examples

import (
	"fmt"
	"log"
	"os"
	"yotracker/internal/service"
)

// TestRawDataLogging demonstrates the raw data logging functionality
func TestRawDataLogging() {
	fmt.Println("=== Slack Raw Data Logging Test ===")

	// Set up test environment
	os.Setenv("GO_ENV", "test")
	os.Setenv("SLACK_BOT_TOKEN", "xoxb-test-token")

	// Initialize Slack service
	slackService, err := service.NewSlackService()
	if err != nil {
		log.Fatalf("Failed to initialize Slack service: %v", err)
	}

	fmt.Printf("✓ Slack service initialized\n")
	fmt.Printf("  Raw data logging enabled: %t\n", slackService.ShouldSendRawDataLog())
	fmt.Printf("  Raw data channel: %s\n", slackService.GetRawDataChannel())

	// Example 1: GPS GPRMC data
	fmt.Println("\n1. Testing GPS GPRMC raw data...")
	gpsData := "24244750524D432C3132333435362E3030302C412C323334352E363738392C4E2C31323334352E363738392C452C302E30302C302E30302C3031303132332C2C2A37332323"

	err = slackService.SendRawDataLog("GPS Tracker 001", "GPS001", gpsData)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Println("✓ GPS raw data logged successfully")
	}

	// Example 2: Short command response
	fmt.Println("\n2. Testing device command response...")
	commandResponse := "4F4B0D0A" // "OK\r\n" in hex

	err = slackService.SendRawDataLog("Fleet Vehicle 002", "FLT002", commandResponse)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Println("✓ Command response logged successfully")
	}

	// Example 3: Long data packet
	fmt.Println("\n3. Testing long data packet...")
	longData := "78781F120B081D112E10CC026B3F3E0C466AA60B0D0A"
	longData += "78781F120B081D112E10CC026B3F3E0C466AA60B0D0A"
	longData += "78781F120B081D112E10CC026B3F3E0C466AA60B0D0A"

	err = slackService.SendRawDataLog("Tracker Device 003", "TRK003", longData)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		fmt.Println("✓ Long data packet logged successfully")
	}

	// Example 4: Test hex formatting
	fmt.Println("\n4. Testing hex formatting...")
	testHex := "48656C6C6F20576F726C64"
	formatted := testFormatHex(testHex)
	fmt.Printf("Original: %s\n", testHex)
	fmt.Printf("Formatted: %s\n", formatted)

	fmt.Println("\n=== Test Complete ===")
	fmt.Println("Note: In test mode, no actual messages are sent to Slack.")
	fmt.Println("Set SLACK_BOT_TOKEN and enable raw data logging to send real messages.")
	fmt.Println()
	fmt.Println("Configuration:")
	fmt.Println("- Set 'slack_raw_data_enabled' to 'true' in settings")
	fmt.Println("- Configure 'slack_raw_data_channel' for the target channel")
	fmt.Println("- Ensure bot has access to the raw data channel")
}

// testFormatHex demonstrates the hex formatting function
func testFormatHex(rawHex string) string {
	// This simulates the internal formatting logic
	cleanHex := rawHex
	var formatted string

	for i := 0; i < len(cleanHex); i += 2 {
		if i > 0 && i%32 == 0 {
			formatted += "\n"
		}
		if i > 0 && i%32 != 0 {
			formatted += " "
		}
		if i+1 < len(cleanHex) {
			formatted += cleanHex[i : i+2]
		} else {
			formatted += cleanHex[i:]
		}
	}

	return formatted
}

// DemoRawDataTypes shows different types of device data that can be logged
func DemoRawDataTypes() {
	fmt.Println("=== Common Device Raw Data Types ===")

	examples := map[string]string{
		"GPS GPRMC":           "24244750524D432C3132333435362E3030302C412C323334352E363738392C4E",
		"AT Command Response": "4F4B0D0A",
		"Binary Protocol":     "78781F120B081D112E10CC026B3F3E0C466AA60B0D0A",
		"Heartbeat":           "787811010001000000000000000000000D0A",
		"Login Packet":        "787813010123456789012345000000000000000D0A",
		"Location Data":       "787822120B081D112E10CC026B3F3E0C466AA60B0D0A",
	}

	for dataType, hexData := range examples {
		fmt.Printf("\n%s:\n", dataType)
		fmt.Printf("Raw: %s\n", hexData)
		fmt.Printf("Formatted: %s\n", testFormatHex(hexData))
		fmt.Printf("Length: %d bytes\n", len(hexData)/2)
	}

	fmt.Println("\n=== Demo Complete ===")
}
