# YoTracker Mail Service Examples

This directory contains comprehensive examples for using the YoTracker mail service with SendGrid integration.

**✅ Status: FIXED** - All examples are now working correctly with proper package declarations and test mode support.

## Files Overview

### Core Examples
- **`mail_usage.go`** - Basic mail service usage examples
- **`template_usage.go`** - Professional email template examples
- **`new_account_email.go`** - New account creation email examples
- **`client_welcome_email.go`** - Client welcome email examples
- **`controller_integration.go`** - Controller integration examples

### Running Examples

#### Option 1: Run All Examples
```bash
# Build and run the examples runner
go build -o mail-examples ./cmd/examples
./mail-examples
```

**Note**: Examples run in test mode by default and will not send actual emails. To send real emails, set your SendGrid API key:
```bash
export SENDGRID_API_KEY=your_actual_api_key
export FROM_EMAIL=<EMAIL>
./mail-examples
```

#### Option 2: Import and Use Individual Examples
```go
import "yotracker/examples"

// Run specific examples
examples.ExampleMailUsage()
examples.ExampleTemplateUsage()
examples.ExampleNewAccountEmail()
examples.ExampleClientWelcomeEmail()
examples.ExampleSetupMailRoutes()
```

## Example Categories

### 1. Basic Mail Usage (`mail_usage.go`)
- Simple email sending
- Email with CC recipients
- Email with attachments
- Bulk email sending
- Error handling

### 2. Template Usage (`template_usage.go`)
- Professional Laravel-style templates
- Custom template creation
- Template with action buttons
- HTML and text generation
- Template customization

### 3. New Account Emails (`new_account_email.go`)
- Account creation notifications
- Credential sharing
- Security reminders
- Different user types
- Bulk account creation

### 4. Client Welcome Emails (`client_welcome_email.go`)
- Client onboarding
- Welcome messages
- Professional branding
- Different client types
- Integration with client creation

### 5. Controller Integration (`controller_integration.go`)
- User creation controllers
- Device alert controllers
- Notification controllers
- Mail API endpoints
- Route setup

## Key Features Demonstrated

### Professional Email Templates
- **Laravel-style Design**: Professional, responsive email templates
- **Action Buttons**: Prominent call-to-action buttons with fallback links
- **Branding**: Consistent app branding throughout emails
- **Responsive**: Works perfectly on desktop and mobile devices

### Email Types
- **Welcome Emails**: Professional onboarding for users and clients
- **Account Creation**: Credential sharing with security reminders
- **Device Alerts**: Real-time notifications for device issues
- **Password Reset**: Secure password reset with time-limited links
- **Notifications**: General purpose notification system
- **Invoices**: Professional invoices with PDF attachments

### Integration Patterns
- **Non-blocking**: Email sending doesn't block main operations
- **Error Handling**: Graceful error handling with logging
- **Async Processing**: Background email sending with goroutines
- **User Feedback**: Clear response messages about email status

## Configuration Required

Before running examples, set up your environment:

```env
# .env file
SENDGRID_API_KEY=your_sendgrid_api_key_here
FROM_EMAIL=<EMAIL>
FROM_NAME=YoTracker
APP_URL=https://yourdomain.com
```

## Example Output

When you run the examples, you'll see:

```
=== YoTracker Mail Service Examples ===

1. Basic Mail Usage Examples:
-----------------------------
Example 1: Simple email using helper function
Simple email sent successfully!

2. Template Usage Examples:
---------------------------
Example 1: Welcome email with professional template
Professional welcome email sent successfully!

3. New Account Email Examples:
------------------------------
Example 1: New account email using helper function
New account email sent successfully!

4. Client Welcome Email Examples:
----------------------------------
Example 1: Client welcome email
Client welcome email sent successfully!

=== All Examples Completed ===
```

## Testing

The examples include comprehensive error handling and will work with fake API keys for testing purposes. For actual email sending, configure your SendGrid API key.

## Integration with Your Application

These examples show exactly how the mail service is integrated into the YoTracker application:

- **User Creation**: Automatic welcome and account emails
- **Client Onboarding**: Professional welcome emails for new clients
- **Device Management**: Alert emails for device issues
- **System Notifications**: General purpose notification system

All examples follow the same patterns used in the actual YoTracker controllers, making them perfect references for implementation.
