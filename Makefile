# Makefile for YoTracker

.PHONY: help build run-backend run-server run-cron seed test clean

help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

build: ## Build all applications
	@echo "Building applications..."
	@cd cmd/server && go build -o tcp_server
	@cd cmd/web/backend && go build -o backend
	@cd cmd/seed && go build -o seeder
	@cd cmd/cron && go build -o cron
	@echo "Build completed!"

run-backend: ## Run the backend web server
	@echo "Starting backend server..."
	@go run cmd/web/backend/backend.go

run-server: ## Run the TCP server
	@echo "Starting TCP server..."
	@go run cmd/server/server.go

run-cron: ## Run the cron jobs
	@echo "Starting cron jobs..."
	@go run cmd/cron/cron.go

seed: ## Run database seeders
	@echo "Running database seeders..."
	@go run cmd/seed/main.go
	@echo "Seeding completed!"

test: ## Run tests
	@echo "Running tests..."
	@go test ./...

test-utils: ## Run utils tests specifically
	@echo "Running utils tests..."
	@go test ./internal/utils -v

clean: ## Clean build artifacts
	@echo "Cleaning build artifacts..."
	@rm -f cmd/server/tcp_server
	@rm -f cmd/web/backend/backend
	@rm -f cmd/seed/seeder
	@rm -f cmd/cron/cron
	@echo "Clean completed!"

dev-setup: seed ## Setup development environment
	@echo "Development environment setup completed!"
