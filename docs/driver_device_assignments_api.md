# Driver Device Assignments API Documentation

## Overview

The Driver Device Assignments API allows you to manage daily driver assignments to vehicles. This system tracks which driver is assigned to which vehicle on specific dates, providing historical tracking and better fleet management capabilities.

## Driver Device Assignment Model

### Fields

- `id` (uint): Primary key
- `created_by_id` (uint): ID of the user who created the assignment
- `driver_id` (uint): ID of the driver
- `client_device_id` (uint): ID of the vehicle/device
- `assignment_date` (date): Date of the assignment
- `start_time` (time, optional): Start time of the assignment
- `end_time` (time, optional): End time of the assignment
- `status` (string): Status (active, completed, cancelled)
- `notes` (text, optional): Additional notes
- `created_at` (timestamp): Creation timestamp
- `updated_at` (timestamp): Last update timestamp

## API Endpoints

### Frontend API (Client-specific)

Base URL: `/api/v1/frontend/driver_device_assignments`

#### GET /driver_device_assignments
Get all driver device assignments for the authenticated client.

**Query Parameters:**
- `date` (optional): Filter by specific date (YYYY-MM-DD)
- `driver_id` (optional): Filter by driver
- `client_device_id` (optional): Filter by device/vehicle
- `status` (optional): Filter by status
- `start_date` (optional): Start date for range filter
- `end_date` (optional): End date for range filter

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "assignment_date": "2024-01-15",
      "start_time": "08:00:00",
      "end_time": "17:00:00",
      "status": "active",
      "notes": "Regular shift",
      "driver": {
        "id": 1,
        "name": "John Doe",
        "driver_license_no": "DL123456"
      },
      "client_device": {
        "id": 1,
        "name": "Truck 001",
        "plate_number": "ABC123"
      }
    }
  ]
}
```

#### GET /driver_device_assignments/search
Search driver device assignments with pagination.

**Query Parameters:**
- `search` (optional): Search by driver name, device name, or license number
- `date` (optional): Filter by specific date
- `driver_id` (optional): Filter by driver
- `client_device_id` (optional): Filter by device
- `status` (optional): Filter by status
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

#### GET /driver_device_assignments/:id
Get a specific driver device assignment by ID.

#### POST /driver_device_assignments
Create a new driver device assignment.

**Request Body:**
```json
{
  "driver_id": 1,
  "client_device_id": 1,
  "assignment_date": "2024-01-15",
  "start_time": "08:00:00",
  "end_time": "17:00:00",
  "status": "active",
  "notes": "Regular shift assignment"
}
```

**Note:** Only one active assignment per device per date is allowed.

#### POST /driver_device_assignments/rfid
Create a new driver device assignment using driver RFID (for self-service).

**Request Body:**
```json
{
  "driver_rfid": "RFID123456",
  "client_device_id": 1,
  "assignment_date": "2024-01-15",
  "start_time": "08:00:00",
  "end_time": "17:00:00",
  "status": "active",
  "notes": "Self-assigned via RFID"
}
```

**Note:** 
- Driver RFID must be registered in the system
- Device must belong to the same client as the driver
- Only one active assignment per device per date is allowed
- Perfect for driver self-service with RFID cards/tags

#### PUT /driver_device_assignments/:id
Update an existing driver device assignment.

#### DELETE /driver_device_assignments/:id
Delete a driver device assignment.

#### GET /driver_device_assignments/current
Get the current active driver assignment for a device today.

**Query Parameters:**
- `client_device_id` (required): ID of the device

### Backend API (Admin)

Base URL: `/api/v1/backend/driver_device_assignments`

Same endpoints as frontend, but with additional query parameters:
- `client_id` (optional): Filter by client

## Business Rules

1. **One Active Assignment Per Device Per Date**: Only one driver can be actively assigned to a device on any given date.

2. **Assignment Statuses**:
   - `active`: Currently assigned and working
   - `completed`: Assignment has been completed
   - `cancelled`: Assignment was cancelled

3. **Time Tracking**: Optional start and end times can be set for shift management.

4. **Historical Tracking**: All assignments are preserved for historical analysis.

## Integration with GPS Data

GPS data no longer directly includes driver information. Instead, you can:

1. **Get Current Driver**: Use the assignment API to get the current driver for a device
2. **Historical Analysis**: Query assignments for specific date ranges
3. **Driver Tracking**: Track which vehicles a driver was assigned to

### Example: Getting Driver for GPS Data

```bash
# Get current driver assignment for a device
curl -X GET "/api/v1/frontend/driver_device_assignments/current?client_device_id=1" \
  -H "Authorization: Bearer <token>"

# Get assignments for a specific date range
curl -X GET "/api/v1/frontend/driver_device_assignments?client_device_id=1&start_date=2024-01-01&end_date=2024-01-31" \
  -H "Authorization: Bearer <token>"
```

## Database Migration

Run the following migration to create the driver device assignments table:

```sql
-- Create driver_device_assignments table
CREATE TABLE IF NOT EXISTS `driver_device_assignments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_by_id` bigint unsigned NOT NULL,
  `driver_id` bigint unsigned NOT NULL,
  `client_device_id` bigint unsigned NOT NULL,
  `assignment_date` date NOT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'active',
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_driver_device_assignments_driver_id` (`driver_id`),
  KEY `idx_driver_device_assignments_client_device_id` (`client_device_id`),
  KEY `idx_driver_device_assignments_assignment_date` (`assignment_date`),
  KEY `idx_driver_device_assignments_status` (`status`),
  UNIQUE KEY `unique_active_assignment_per_device_per_date` (`client_device_id`, `assignment_date`, `status`),
  CONSTRAINT `fk_driver_device_assignments_driver_id` FOREIGN KEY (`driver_id`) REFERENCES `drivers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_driver_device_assignments_client_device_id` FOREIGN KEY (`client_device_id`) REFERENCES `client_devices` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Remove driver_id column from gps_data table if it exists
ALTER TABLE `gps_data` DROP COLUMN IF EXISTS `driver_id`;
```

## Usage Examples

### Creating a Daily Assignment

```bash
curl -X POST /api/v1/frontend/driver_device_assignments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "driver_id": 1,
    "client_device_id": 1,
    "assignment_date": "2024-01-15",
    "start_time": "08:00:00",
    "end_time": "17:00:00",
    "status": "active",
    "notes": "Regular morning shift"
  }'
```

### Creating Assignment via RFID (Driver Self-Service)

```bash
curl -X POST /api/v1/frontend/driver_device_assignments/rfid \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "driver_rfid": "RFID123456",
    "client_device_id": 1,
    "assignment_date": "2024-01-15",
    "start_time": "08:00:00",
    "end_time": "17:00:00",
    "status": "active",
    "notes": "Self-assigned via RFID card"
  }'
```

### Checking Current Driver

```bash
curl -X GET "/api/v1/frontend/driver_device_assignments/current?client_device_id=1" \
  -H "Authorization: Bearer <token>"
```

### Getting Assignment History

```bash
curl -X GET "/api/v1/frontend/driver_device_assignments?client_device_id=1&start_date=2024-01-01&end_date=2024-01-31" \
  -H "Authorization: Bearer <token>"
```

### Completing an Assignment

```bash
curl -X PUT /api/v1/frontend/driver_device_assignments/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "driver_id": 1,
    "client_device_id": 1,
    "assignment_date": "2024-01-15",
    "start_time": "08:00:00",
    "end_time": "17:00:00",
    "status": "completed",
    "notes": "Shift completed successfully"
  }'
```

## Benefits of This Approach

1. **Historical Tracking**: Complete history of driver assignments
2. **Flexible Scheduling**: Support for different shifts and time periods
3. **Better Fleet Management**: Clear assignment tracking per day
4. **Audit Trail**: Full record of who was assigned to what vehicle when
5. **Scalability**: Can handle multiple assignments per day if needed
6. **Status Management**: Track active, completed, and cancelled assignments 