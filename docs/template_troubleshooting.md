# WhatsApp Template Troubleshooting Guide

## Error: Template name does not exist

### Error Message
```
WhatsApp API error: 404 - map[error:map[code:132001 error_data:map[details:template name (device_alert) does not exist in en_US messaging_product:whatsapp] fbtrace_id:AwWJIgHygmmr7kyVN6n0A4f message:(#132001) Template name does not exist in the translation type:OAuthException]]
```

## Step-by-Step Troubleshooting

### 1. Check Template in WhatsApp Business Manager

1. Go to [business.facebook.com](https://business.facebook.com)
2. Navigate to your WhatsApp Business account
3. Go to **Messaging** → **Message Templates**
4. Look for a template named `device_alert`

**What to check:**
- ✅ Template exists
- ✅ Template is approved (not pending or rejected)
- ✅ Template language is `en` (English)
- ✅ Template name is exactly `device_alert` (case-sensitive)

### 2. Common Issues and Solutions

#### Issue 1: Template Not Created
**Solution**: Create the template in WhatsApp Business Manager

#### Issue 2: Template Not Approved
**Solution**: Wait for approval or check rejection reason

#### Issue 3: Wrong Language
**Solution**: Check template language or create template in correct language

#### Issue 4: Wrong Template Name
**Solution**: Update settings or rename template

### 3. Verify Template Details

**Required Template:**
- **Name**: `device_alert`
- **Language**: `en`
- **Category**: Utility
- **Status**: Approved

**Template Content:**
```
🚨 {{1}}
{{2}}

Login to our portal to view more details
```

**Button Configuration:**
- Button Type: URL
- Button Text: "View Device"
- URL: Static URL (e.g., "https://yotracker.co.zw/dashboard")

### 4. Check Settings Configuration

Verify your database settings:
```sql
SELECT * FROM settings WHERE setting_key LIKE '%whatsapp%template%';
```

Expected results:
- `whatsapp_device_alert_template` = `device_alert`
- `whatsapp_template_language` = `en`

### 5. Test Template with Different Language

If the template exists in a different language, try updating the language setting:

```sql
UPDATE settings 
SET setting_value = 'en' 
WHERE setting_key = 'whatsapp_template_language';
```

### 6. Create Template if Missing

If the template doesn't exist, create it:

1. **Template Name**: `device_alert`
2. **Category**: Marketing
3. **Language**: English (en_US)
4. **Content**:
   ```
   🚨 {{1}}
   {{2}}
   
   Login to our portal to view more details
   ```
5. **Button**: Add "View Device" button
6. **Submit for approval**

### 7. Alternative Template Names

If you have a different template name, update the setting:

```sql
UPDATE settings 
SET setting_value = 'your_actual_template_name' 
WHERE setting_key = 'whatsapp_device_alert_template';
```

### 8. Debug Template Information

Add this code to debug template information:

```go
// Debug template settings
templateName := models.GetSetting("whatsapp_device_alert_template")
language := models.GetSetting("whatsapp_template_language")

fmt.Printf("Template Name: %s\n", templateName)
fmt.Printf("Template Language: %s\n", language)
```

### 9. Common Template Names

Check if you have any of these templates:
- `device_alert`
- `alert_notification`
- `device_notification`
- `geofence_alert`
- `speed_alert`

### 10. WhatsApp API Testing

Test the template directly with WhatsApp API:

```bash
curl -X POST \
  "https://graph.facebook.com/v18.0/YOUR_PHONE_NUMBER_ID/messages" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messaging_product": "whatsapp",
    "recipient_type": "individual",
    "to": "RECIPIENT_PHONE_NUMBER",
    "type": "template",
    "template": {
      "name": "device_alert",
      "language": {
        "code": "en_US"
      },
      "components": [
        {
          "type": "body",
          "parameters": [
            {
              "type": "text",
              "text": "Speed Alert"
            },
            {
              "type": "text", 
              "text": "Device exceeded speed limit"
            }
          ]
        }
      ]
    }
  }'
```

## Quick Fixes

### Option 1: Create the Template
1. Go to WhatsApp Business Manager
2. Create template named `device_alert`
3. Wait for approval

### Option 2: Use Existing Template
1. Check what templates you have
2. Update the setting to use existing template name

### Option 3: Change Language
1. Check template language
2. Update `whatsapp_template_language` setting

### Option 4: Disable WhatsApp Temporarily
```sql
UPDATE settings 
SET setting_value = '0' 
WHERE setting_key = 'whatsapp_alerts_enabled';
```

## Next Steps

1. **Check WhatsApp Business Manager** for template status
2. **Verify template name and language**
3. **Create template if missing**
4. **Update settings if needed**
5. **Test again** 