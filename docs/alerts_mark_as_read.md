# Alert Mark as Read Functionality

This document describes the mark as read functionality for alerts in the YoTracker system.

## Overview

The alert system now supports marking alerts as read/unread with the following features:
- Mark individual alerts as read or unread
- Bulk mark multiple alerts as read
- Mark all alerts as read (with optional filters)
- Automatic timestamp tracking when alerts are marked as read

## Database Schema

The `alerts` table includes the following fields for read status:
- `read` (boolean, nullable) - Whether the alert has been read
- `read_at` (timestamp, nullable) - When the alert was marked as read

## API Endpoints

### Backend API (Admin)

All backend endpoints are prefixed with `/api/v1/backend/alerts`

#### Mark Single Alert as Read
```http
PATCH /api/v1/backend/alerts/{id}/mark-read
```

**Response:**
```json
{
  "message": "Alert marked as read successfully",
  "data": {
    "id": 1,
    "read": true,
    "read_at": "2025-06-14T19:30:00Z",
    ...
  }
}
```

#### Mark Single Alert as Unread
```http
PATCH /api/v1/backend/alerts/{id}/mark-unread
```

**Response:**
```json
{
  "message": "<PERSON>ert marked as unread successfully",
  "data": {
    "id": 1,
    "read": false,
    "read_at": null,
    ...
  }
}
```

#### Bulk Mark Alerts as Read
```http
PATCH /api/v1/backend/alerts/bulk/mark-read
```

**Request Body:**
```json
{
  "alert_ids": [1, 2, 3, 4, 5]
}
```

**Response:**
```json
{
  "message": "Alerts marked as read successfully",
  "updated_count": 5
}
```

#### Mark All Alerts as Read
```http
PATCH /api/v1/backend/alerts/mark-all-read?client_device_id=1&alert_type=speed_alert
```

**Query Parameters (optional):**
- `client_device_id` - Filter by specific device
- `device_id` - Filter by device ID
- `alert_type` - Filter by alert type

**Response:**
```json
{
  "message": "All alerts marked as read successfully",
  "updated_count": 15
}
```

### Frontend API (Client)

All frontend endpoints are prefixed with `/api/v1/frontend/alerts` and automatically filter by the authenticated client.

#### Mark Single Alert as Read
```http
PATCH /api/v1/frontend/alerts/{id}/mark-read
```

#### Mark Single Alert as Unread
```http
PATCH /api/v1/frontend/alerts/{id}/mark-unread
```

#### Bulk Mark Alerts as Read
```http
PATCH /api/v1/frontend/alerts/bulk/mark-read
```

#### Mark All Alerts as Read
```http
PATCH /api/v1/frontend/alerts/mark-all-read?client_device_id=1&alert_type=speed_alert
```

## Usage Examples

### JavaScript/Frontend Usage

```javascript
// Mark single alert as read
async function markAlertAsRead(alertId) {
  const response = await fetch(`/api/v1/frontend/alerts/${alertId}/mark-read`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
}

// Mark multiple alerts as read
async function markAlertsAsRead(alertIds) {
  const response = await fetch('/api/v1/frontend/alerts/bulk/mark-read', {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ alert_ids: alertIds })
  });
  return response.json();
}

// Mark all alerts as read
async function markAllAlertsAsRead(filters = {}) {
  const params = new URLSearchParams(filters);
  const response = await fetch(`/api/v1/frontend/alerts/mark-all-read?${params}`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
}
```

### cURL Examples

```bash
# Mark alert as read
curl -X PATCH "http://localhost:9001/api/v1/frontend/alerts/1/mark-read" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Bulk mark alerts as read
curl -X PATCH "http://localhost:9001/api/v1/frontend/alerts/bulk/mark-read" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"alert_ids": [1, 2, 3]}'

# Mark all speed alerts as read
curl -X PATCH "http://localhost:9001/api/v1/frontend/alerts/mark-all-read?alert_type=speed_alert" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Filtering Alerts by Read Status

You can filter alerts by read status using the existing GET endpoints:

```http
GET /api/v1/frontend/alerts?read=true    # Get only read alerts
GET /api/v1/frontend/alerts?read=false   # Get only unread alerts
GET /api/v1/frontend/alerts              # Get all alerts
```

## Security

- Frontend endpoints automatically filter alerts by the authenticated client
- Backend endpoints have full access to all alerts
- All endpoints require proper authentication
- Alert IDs are validated to ensure they exist before updating

## Error Handling

Common error responses:

```json
// Alert not found
{
  "message": "Alert not found"
}

// Invalid request body
{
  "message": "No alert IDs provided"
}

// Database error
{
  "message": "Database error message"
}
```

## Testing

Run the mark as read tests:

```bash
# Test backend functionality
go test ./cmd/web/backend/controllers -v -run TestMarkAlert

# Test frontend functionality  
go test ./cmd/web/frontend/controllers -v -run TestFrontendMarkAlert

# Run all alert tests
go test ./cmd/web/*/controllers -v -run Alert
```
