# Google Geocoding Integration

## Overview

The Google Geocoding integration automatically fetches location names for GPS coordinates in the `last_location` routes using Google's Geocoding API. This provides real-time location names when GPS data doesn't have them populated.

## Features

- **Automatic Location Name Fetching**: Checks for empty location names and fetches them on-demand
- **Google Maps API Integration**: Uses Google's Geocoding API for accurate location data
- **Settings-Based Configuration**: API key stored in database settings
- **Error Handling**: Graceful fallback if geocoding fails
- **Multi-Route Support**: Works with backend, frontend, and public device share routes

## API Usage

The service uses Google's Geocoding API:
- **Base URL**: `https://maps.googleapis.com/maps/api/geocode/json`
- **Method**: GET
- **Parameters**: `latlng`, `key`, `language`
- **Response**: JSON with formatted address and address components

## Configuration

### Google Maps API Key

The API key is stored in the database settings table:

```sql
-- Check if API key exists
SELECT * FROM settings WHERE setting_key = 'google_maps_api_key';

-- Add API key if not exists
INSERT INTO settings (name, setting_key, setting_value, category) VALUES
('Google Maps API Key', 'google_maps_api_key', 'YOUR_API_KEY_HERE', 'system');
```

### API Key Requirements

1. **Google Cloud Console**: Create a project and enable Geocoding API
2. **API Key**: Generate an API key with Geocoding API access
3. **Billing**: Enable billing (required for Geocoding API)
4. **Quotas**: Monitor usage to avoid rate limits

## Implementation Details

### Service Location
- **Service**: `internal/service/google_geocoding_service.go`
- **Routes**: 
  - Backend: `cmd/web/backend/controllers/gps_data.go`
  - Frontend: `cmd/web/frontend/controllers/gps_data.go`
  - Public: `cmd/web/public/controllers/device_share.go`

### Route Integration

The `last_location` routes automatically check for empty location names:

```go
func GetLastLocation(c *gin.Context) {
    var gpsData models.GPSData
    // ... fetch GPS data ...
    
    // Check if location name is empty and fetch it
    if gpsData.LocationName == nil || *gpsData.LocationName == "" {
        geocodingService := service.NewGoogleGeocodingService()
        err := geocodingService.UpdateGPSDataLocationName(&gpsData)
        if err != nil {
            // Log error but don't fail the request
            fmt.Printf("Failed to fetch location name: %v", err)
        }
    }
    
    c.JSON(http.StatusOK, gin.H{"data": gpsData})
}
```

### Location Name Format

The service builds location names in the following priority order:
1. Street number + Street name (if available)
2. Street name (if available)
3. Suburb/Locality
4. City/Administrative area
5. State/Province
6. Country

Example output: `"123 Main St, Harare, Zimbabwe"`

## Error Handling

The service handles various error scenarios gracefully:

1. **Missing API Key**: Returns error but doesn't crash the route
2. **API Errors**: Logs error and continues without location name
3. **Network Issues**: Timeout after 10 seconds
4. **Invalid Coordinates**: Returns appropriate error message

## Performance Considerations

- **Caching**: No built-in caching (relies on database storage)
- **Rate Limiting**: Google's API has quotas (check your plan)
- **Timeout**: 10-second timeout per request
- **Concurrent Requests**: Each route call makes one API request if needed

## Testing

### Unit Tests
```bash
go test ./internal/service -run TestGoogleGeocodingService -v
```

### Manual Testing
```bash
go run examples/google_geocoding_usage.go
```

## Example Usage

### API Response

**Request**:
```
GET /api/v1/backend/gps_data/last_location?client_device_id=123
```

**Response**:
```json
{
  "data": {
    "id": 456,
    "client_device_id": 123,
    "latitude": -17.8216,
    "longitude": 31.0492,
    "location_name": "Herbert Chitepo Avenue, Milton Park, Harare, Zimbabwe",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### Before/After Comparison

**Before** (empty location name):
```json
{
  "location_name": null
}
```

**After** (fetched from Google):
```json
{
  "location_name": "Herbert Chitepo Avenue, Milton Park, Harare, Zimbabwe"
}
```

## Troubleshooting

### Common Issues

1. **"google_maps_api_key setting is not configured"**
   - **Solution**: Add API key to settings table
   - **Check**: `SELECT * FROM settings WHERE setting_key = 'google_maps_api_key';`

2. **"Google geocoding API returned status: REQUEST_DENIED"**
   - **Solution**: Check API key validity and billing status
   - **Check**: Google Cloud Console > APIs & Services > Credentials

3. **"Google geocoding API returned status: OVER_QUERY_LIMIT"**
   - **Solution**: Check API quotas and billing
   - **Check**: Google Cloud Console > APIs & Services > Quotas

4. **"Failed to fetch location name"**
   - **Solution**: Check network connectivity and API key
   - **Check**: Logs for specific error details

### Debug Information

Enable debug logging to see detailed API interactions:

```go
// Add to your logging configuration
log.SetLevel(log.DebugLevel)
```

## Cost Considerations

- **Google Geocoding API**: Pay-per-use pricing
- **Typical Cost**: ~$5 per 1,000 requests
- **Monitoring**: Set up billing alerts in Google Cloud Console
- **Optimization**: Consider caching frequently requested locations

## Future Enhancements

Potential improvements:
- **Caching Layer**: Cache location names to reduce API calls
- **Batch Processing**: Process multiple coordinates in one request
- **Fallback APIs**: Use alternative geocoding services
- **Rate Limiting**: Implement client-side rate limiting
- **Location History**: Store location name history for analysis 