# Geofence Implementation Documentation

## Overview

This document describes the comprehensive geofence implementation for the YoTracker system. The implementation provides real-time geofence monitoring with entry/exit detection, alert generation, and performance optimization.

## Features

- **Real-time geofence monitoring** - Automatic checking during GPS data saving
- **Multiple geofence types** - Support for circles (with future polygon/rectangle support)
- **Entry/Exit detection** - Tracks device movement in and out of geofences
- **Alert generation** - Creates alerts in the alerts table for geofence events
- **Event logging** - Maintains detailed geofence event history
- **Performance optimization** - Uses caching to prevent duplicate alerts
- **Client isolation** - Proper client-based access control
- **Hierarchical geofence scoping** - Client, fleet, and device-level geofences
- **Extensible design** - Easy to add new geofence shapes in the future

## Database Schema

### Geofences Table
```sql
CREATE TABLE geofences (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    client_id BIGINT NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    geofence_type VARCHAR(255) DEFAULT 'circle',  -- circle, polygon, rectangle

    -- Scope definition - where this geofence applies
    applies_to VARCHAR(255) DEFAULT 'client',     -- client, fleet, device
    fleet_id BIGINT NULL,                         -- required if applies_to = 'fleet'
    client_device_id BIGINT NULL,                 -- required if applies_to = 'device'
    device_id VARCHAR(255),                       -- for backward compatibility

    -- Event triggers - what events to generate
    trigger_events VARCHAR(255) DEFAULT 'both',   -- entry, exit, both

    -- Circle-specific fields
    radius DOUBLE NULL,      -- in meters, required for circle type
    latitude DOUBLE NULL,    -- center latitude for circle
    longitude DOUBLE NULL,   -- center longitude for circle

    -- Polygon/rectangle fields
    coordinates TEXT NULL,   -- JSON array of coordinates for polygon/rectangle

    status VARCHAR(255) DEFAULT 'active',
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_client_id (client_id),
    INDEX idx_applies_to (applies_to),
    INDEX idx_fleet_id (fleet_id),
    INDEX idx_client_device_id (client_device_id),
    INDEX idx_geofence_type (geofence_type)
);
```

### Geofence Events Table
```sql
CREATE TABLE geofence_events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    client_device_id BIGINT NOT NULL,
    geofence_id BIGINT NOT NULL,
    event_type VARCHAR(255) NOT NULL,  -- 'entry' or 'exit'
    event_timestamp TIMESTAMP NOT NULL,
    latitude DOUBLE NOT NULL,
    longitude DOUBLE NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_client_device_id (client_device_id),
    INDEX idx_geofence_id (geofence_id),
    INDEX idx_event_timestamp (event_timestamp)
);
```

## API Endpoints

### Backend API (Admin)
All backend endpoints are prefixed with `/api/v1/backend/geofences`

- `GET /` - Get all geofences with pagination
- `GET /search` - Search geofences with filters
- `GET /:id` - Get geofence by ID
- `POST /` - Create new geofence
- `PUT /:id` - Update geofence
- `DELETE /:id` - Delete geofence
- `GET /events` - Get geofence events with pagination
- `GET /device/:device_id` - Get geofences for specific device

### Frontend API (Client)
All frontend endpoints are prefixed with `/api/v1/frontend/geofences`

- `GET /` - Get client's geofences with pagination
- `GET /search` - Search client's geofences with filters
- `GET /:id` - Get client's geofence by ID
- `POST /` - Create new geofence for client
- `PUT /:id` - Update client's geofence
- `DELETE /:id` - Delete client's geofence
- `GET /events` - Get client's geofence events with pagination
- `GET /device/:device_id` - Get geofences for client's specific device

## Request/Response Examples

### Create Device-Level Circle Geofence (Entry Only)
```json
{
    "name": "Home Geofence",
    "geofence_type": "circle",
    "applies_to": "device",
    "client_device_id": 123,
    "trigger_events": "entry",
    "radius": 100,
    "latitude": -17.8216,
    "longitude": 31.0492,
    "status": "active"
}
```

### Create Fleet-Level Polygon Geofence (Exit Only)
```json
{
    "name": "Fleet Operating Area",
    "geofence_type": "polygon",
    "applies_to": "fleet",
    "fleet_id": 456,
    "trigger_events": "exit",
    "coordinates": "[{\"lat\":-17.8216,\"lng\":31.0492},{\"lat\":-17.8220,\"lng\":31.0500},{\"lat\":-17.8210,\"lng\":31.0510},{\"lat\":-17.8200,\"lng\":31.0495}]",
    "status": "active"
}
```

### Create Client-Level Rectangle Geofence (Both Events)
```json
{
    "name": "Company Campus",
    "geofence_type": "rectangle",
    "applies_to": "client",
    "trigger_events": "both",
    "coordinates": "[{\"lat\":-17.8216,\"lng\":31.0492},{\"lat\":-17.8220,\"lng\":31.0492},{\"lat\":-17.8220,\"lng\":31.0500},{\"lat\":-17.8216,\"lng\":31.0500}]",
    "status": "active"
}
```

### Geofence Response
```json
{
    "id": 1,
    "client_id": 1,
    "client_device_id": 123,
    "device_id": "DEVICE001",
    "name": "Home Geofence",
    "radius": 100,
    "latitude": -17.8216,
    "longitude": 31.0492,
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
}
```

### Geofence Event Response
```json
{
    "id": 1,
    "client_device_id": 123,
    "geofence_id": 1,
    "event_type": "entry",
    "event_timestamp": "2024-01-01T12:00:00Z",
    "latitude": -17.8216,
    "longitude": 31.0492,
    "created_at": "2024-01-01T12:00:01Z",
    "updated_at": "2024-01-01T12:00:01Z",
    "geofence": {
        "id": 1,
        "name": "Home Geofence"
    },
    "client_device": {
        "id": 123,
        "device_id": "DEVICE001"
    }
}
```

## Integration with GPS Data

The geofence checking is automatically integrated into the GPS data saving process in `internal/service/location_data_service.go`. When GPS data is saved:

1. GPS data is processed and saved to the database
2. Device location and status are updated
3. **Geofence checking is performed asynchronously** using `CheckGeofences()`
4. Alerts and events are created if entry/exit is detected

## Performance Optimizations

### 1. Asynchronous Processing
Geofence checking runs in a separate goroutine to avoid blocking GPS data saving.

### 2. State Caching
The system maintains an in-memory cache of geofence states to prevent duplicate alerts:
```go
var geofenceStateCache = make(map[uint]*GeofenceState)
```

### 3. Efficient Distance Calculation
Uses the existing `HaversineDistance` function for accurate distance calculations.

### 4. Database Indexing
Proper indexes on `client_id`, `client_device_id`, and `event_timestamp` for fast queries.

### 5. Selective Geofence Loading
- First loads device-specific geofences
- Falls back to client-level geofences if none found
- Only processes active geofences

## Validation Rules

### Coordinates
- Latitude: -90 to 90 degrees
- Longitude: -180 to 180 degrees

### Radius
- Minimum: 10 meters
- Maximum: 50,000 meters (50 km)

### Required Fields
- Name (required)
- Latitude (required)
- Longitude (required)
- Radius (required)
- Either `client_device_id` or `device_id` (required)

## Alert Generation

When a geofence event occurs, the system:

1. **Checks device preferences** - Respects `GeofenceEntryEvents` and `GeofenceExitEvents` flags
2. **Creates alert** with type "geofence"
3. **Includes detailed information**:
   - Geofence name and ID
   - Event type (entry/exit)
   - Current coordinates
   - Geofence center and radius

### Alert Additional Data
```json
{
    "geofence_id": 1,
    "geofence_name": "Home Geofence",
    "event_type": "entry",
    "latitude": -17.8216,
    "longitude": 31.0492,
    "radius": 100,
    "geofence_lat": -17.8216,
    "geofence_lng": 31.0492
}
```

## Testing

The implementation includes comprehensive tests in `internal/service/geofence_service_test.go`:

- **Functional tests** - Entry/exit detection
- **Validation tests** - Coordinate and radius validation
- **Performance tests** - Multiple geofence checking
- **Integration tests** - Database operations

Run tests with:
```bash
go test ./internal/service -v
```

## Security Considerations

1. **Client isolation** - Frontend API only shows client's own geofences
2. **Device ownership validation** - Ensures devices belong to the requesting client
3. **Input validation** - Validates all coordinates and radius values
4. **SQL injection prevention** - Uses parameterized queries

## Hierarchical Geofence System

The geofence system supports three levels of scope, allowing flexible assignment of geofences:

### Scope Levels

#### Client-Level Geofences (`applies_to: "client"`)
- **Scope**: Apply to ALL devices belonging to the client
- **Use cases**: Company-wide policies, general operating areas
- **Required fields**: None additional
- **Example**: Company campus boundary that applies to all company vehicles

#### Fleet-Level Geofences (`applies_to: "fleet"`)
- **Scope**: Apply to ALL devices in a specific fleet
- **Use cases**: Fleet-specific operating zones, department areas
- **Required fields**: `fleet_id`
- **Example**: Delivery fleet restricted to city center area

#### Device-Level Geofences (`applies_to: "device"`)
- **Scope**: Apply to ONE specific device only
- **Use cases**: Device-specific zones, personal vehicle areas
- **Required fields**: `client_device_id`
- **Example**: Personal vehicle home garage geofence

### Geofence Resolution Priority

When checking geofences for a device, the system considers ALL applicable geofences:

1. **Device-specific geofences** (applies_to = "device" AND client_device_id = device.id)
2. **Fleet-specific geofences** (applies_to = "fleet" AND fleet_id = device.fleet_id) - if device belongs to a fleet
3. **Client-level geofences** (applies_to = "client" AND client_id = device.client_id)

All applicable geofences are checked simultaneously, allowing for overlapping coverage and multiple alert types.

## Event Trigger Configuration

Each geofence can be configured to trigger on specific events using the `trigger_events` field:

### Trigger Event Types

#### Entry Only (`trigger_events: "entry"`)
- **Behavior**: Only triggers alerts/events when a device ENTERS the geofence
- **Use cases**: Welcome notifications, arrival tracking, check-in alerts
- **Example**: Home arrival notification, office check-in alert

#### Exit Only (`trigger_events: "exit"`)
- **Behavior**: Only triggers alerts/events when a device EXITS the geofence
- **Use cases**: Departure notifications, unauthorized exit alerts, security monitoring
- **Example**: Leaving work notification, vehicle theft alert when leaving secure area

#### Both Events (`trigger_events: "both"`) - Default
- **Behavior**: Triggers alerts/events for both ENTRY and EXIT
- **Use cases**: Complete movement tracking, comprehensive monitoring
- **Example**: Full activity tracking for company vehicles

### Event Trigger Logic

The system respects the `trigger_events` setting when processing GPS data:

```go
// Entry event detection
if isInside && (!existed || !wasInside) {
    if shouldTriggerEvent(geofence.TriggerEvents, "entry") {
        createGeofenceAlert(...)
        createGeofenceEvent(...)
    }
}

// Exit event detection
if !isInside && existed && wasInside {
    if shouldTriggerEvent(geofence.TriggerEvents, "exit") {
        createGeofenceAlert(...)
        createGeofenceEvent(...)
    }
}
```

## Geofence Types

### Currently Supported

#### Circle Geofences
- **Type**: `"circle"`
- **Required fields**: `latitude`, `longitude`, `radius`
- **Description**: Circular area defined by center point and radius in meters
- **Algorithm**: Haversine distance calculation
- **Use cases**: Home zones, office areas, parking lots

#### Polygon Geofences
- **Type**: `"polygon"`
- **Required fields**: `coordinates` (JSON array of lat/lng points)
- **Description**: Complex shapes defined by multiple coordinate points (minimum 3 points)
- **Algorithm**: Ray casting algorithm for point-in-polygon detection
- **Use cases**: Irregular areas, city boundaries, complex zones, property boundaries

#### Rectangle Geofences
- **Type**: `"rectangle"`
- **Required fields**: `coordinates` (JSON array with 4 corner points)
- **Description**: Rectangular areas defined by corner coordinates
- **Algorithm**: Bounding box calculation for efficient checking
- **Use cases**: Building footprints, rectangular zones, parking areas

### Adding New Geofence Types

The system is designed to easily support new geofence types:

1. **Add validation** in `ValidateGeofenceByType()`
2. **Add checking logic** in `isPointInsideGeofence()`
3. **Update API documentation** and examples

## Future Enhancements

1. **Time-based geofences** - Active only during certain hours
2. **Geofence groups** - Organize related geofences
3. **Advanced notifications** - Email/SMS alerts for geofence events
4. **Geofence analytics** - Time spent in geofences, frequency analysis
5. **Geofence templates** - Pre-defined geofence shapes

## Troubleshooting

### Common Issues

1. **No alerts generated**
   - Check device event preferences (`GeofenceEntryEvents`, `GeofenceExitEvents`)
   - Verify geofence status is "active"
   - Ensure device belongs to correct client

2. **Performance issues**
   - Monitor geofence cache size
   - Check database indexes
   - Consider reducing geofence check frequency

3. **Duplicate alerts**
   - Verify state cache is working correctly
   - Check for multiple GPS data sources

### Debug Information

Enable debug logging to see geofence checking details:
```go
fmt.Printf("Checking geofences for device %d at %f,%f", clientDeviceId, latitude, longitude)
```
