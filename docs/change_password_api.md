# Change Password API Documentation

## Overview
The ChangePassword functionality allows authenticated users to change their password by providing their current password and a new password with confirmation.

## Endpoints

### Frontend (Client Users)
- **URL**: `PUT /api/v1/frontend/users/change-password`
- **Authentication**: Required (Bearer token)
- **Middleware**: AuthMiddleware, CheckForClient

### Backend (Admin Users)
- **URL**: `PUT /api/v1/backend/users/change-password`
- **Authentication**: Required (Bearer token)
- **Middleware**: AuthMiddleware, CheckIfUserIsBackend

## Request Format

### Headers
```
Content-Type: application/json
Authorization: Bearer <your_jwt_token>
```

### Request Body
```json
{
  "current_password": "your_current_password",
  "password": "your_new_password",
  "password_confirmation": "your_new_password"
}
```

### Field Validation
- `current_password`: Required, must match user's existing password
- `password`: Required, minimum 6 characters
- `password_confirmation`: Required, must match the `password` field

## Response Format

### Success Response (200 OK)
```json
{
  "message": "Password changed successfully"
}
```

### Error Responses

#### Validation Error (400 Bad Request)
```json
{
  "message": "Password and password confirmation do not match"
}
```

#### Incorrect Current Password (400 Bad Request)
```json
{
  "message": "Current password is incorrect"
}
```

#### Unauthorized (401 Unauthorized)
```json
{
  "message": "Unauthorized"
}
```

#### Server Error (500 Internal Server Error)
```json
{
  "message": "Failed to update password"
}
```

## Security Features

1. **Authentication Required**: Users must be logged in with a valid JWT token
2. **Current Password Verification**: Users must provide their current password to change it
3. **Password Confirmation**: New password must be confirmed to prevent typos
4. **Password Hashing**: New passwords are hashed using bcrypt before storage
5. **User Isolation**: Users can only change their own password (enforced by middleware)

## Example Usage

### Using curl
```bash
curl -X PUT \
  http://localhost:9001/api/v1/frontend/users/change-password \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your_jwt_token_here' \
  -d '{
    "current_password": "oldpassword123",
    "password": "newpassword456",
    "password_confirmation": "newpassword456"
  }'
```

### Using JavaScript (fetch)
```javascript
const response = await fetch('/api/v1/frontend/users/change-password', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    current_password: 'oldpassword123',
    password: 'newpassword456',
    password_confirmation: 'newpassword456'
  })
});

const result = await response.json();
console.log(result.message);
```

## Implementation Details

### Models
- Uses `ChangePasswordRequest` struct for request validation
- Defined in `internal/models/user.go`

### Controllers
- Frontend: `cmd/web/frontend/controllers/users.go`
- Backend: `cmd/web/backend/controllers/users.go`

### Password Service
- Password hashing: `internal/service/password.go`
- Uses bcrypt for secure password hashing

### Database
- Updates the `password` field in the `users` table
- Uses GORM for database operations

## Testing
Comprehensive tests are available:
- Frontend: `cmd/web/frontend/controllers/users_change_password_test.go`
- Backend: `cmd/web/backend/controllers/users_change_password_test.go`

Test scenarios include:
- Successful password change
- Incorrect current password
- Password confirmation mismatch
- Missing required fields
- Unauthorized access
