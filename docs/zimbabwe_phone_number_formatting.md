# Zimbabwe Phone Number Formatting

## Overview

The alert services now include special handling for Zimbabwe phone numbers to ensure proper formatting for WhatsApp and SMS delivery.

## Zimbabwe Phone Number Format

Zimbabwe phone numbers follow this pattern:
- **Local format**: `0774175438` (10 digits starting with 0)
- **International format**: `+263774175438` (country code +263 + 9 digits)

## Formatting Logic

### Automatic Detection
The system automatically detects Zimbabwe numbers by checking:
1. **Length**: Exactly 10 digits
2. **Prefix**: Starts with `0`

### Conversion Process
When a Zimbabwe number is detected:
1. Remove the leading `0`
2. Add the Zimbabwe country code `+263`
3. Result: `0774175438` → `+263774175438`

### Examples

| Input | Output | Description |
|-------|--------|-------------|
| `0774175438` | `+263774175438` | Zimbabwe mobile number |
| `0712345678` | `+263712345678` | Zimbabwe mobile number |
| `0734567890` | `+263734567890` | Zimbabwe mobile number |
| `+263774175438` | `+263774175438` | Already formatted |
| `1234567890` | `+2631234567890` | Other number (uses default) |
| `+1234567890` | `+1234567890` | US number (unchanged) |

## Implementation

### WhatsApp Service
```go
func (w *WhatsAppService) formatPhoneNumber(phone string) string {
    // Remove any non-digit characters except +
    formatted := ""
    for _, char := range phone {
        if char >= '0' && char <= '9' || char == '+' {
            formatted += string(char)
        }
    }

    // If no country code, handle Zimbabwe format
    if !strings.HasPrefix(formatted, "+") {
        // Check if it's a Zimbabwe number (10 digits starting with 0)
        if len(formatted) == 10 && strings.HasPrefix(formatted, "0") {
            // Remove the first 0 and add Zimbabwe country code
            formatted = "+263" + formatted[1:]
        } else {
            // Use default country code for other formats
            defaultCountryCode := models.GetSetting("whatsapp_default_country_code")
            if defaultCountryCode == "" {
                defaultCountryCode = "263" // Default to Zimbabwe
            }
            formatted = "+" + defaultCountryCode + formatted
        }
    }

    return formatted
}
```

### SMS Service
The SMS service uses the same formatting logic as the WhatsApp service to ensure consistency.

## Configuration

### Default Country Code
The system uses the `whatsapp_default_country_code` setting from the database:
- **Default value**: `263` (Zimbabwe)
- **Setting key**: `whatsapp_default_country_code`
- **Category**: `notifications`

### Settings Table
```sql
INSERT INTO settings (name, setting_key, setting_value, category) VALUES
('WhatsApp Default Country Code', 'whatsapp_default_country_code', '263', 'notifications');
```

## Usage Examples

### Client Configuration
```go
client := &models.Client{
    Name: "Test Company",
}

// Set Zimbabwe phone numbers (local format)
client.SetWhatsappPhoneNumbers([]string{
    "0774175438",  // Will be formatted to +263774175438
    "0712345678",  // Will be formatted to +263712345678
})

// Set SMS numbers (local format)
client.SetSmsPhoneNumbers([]string{
    "0774175438",  // Will be formatted to +263774175438
    "0734567890",  // Will be formatted to +263734567890
})
```

### Alert Sending
```go
// The system automatically formats numbers when sending alerts
err := service.SendAlertToClient(client, alert, device)
// WhatsApp/SMS will be sent to the properly formatted numbers
```

## Testing

### Unit Tests
The formatting logic is tested with various input formats:

```go
tests := []struct {
    input    string
    expected string
}{
    // Zimbabwe numbers (10 digits starting with 0)
    {"0774175438", "+263774175438"},
    {"0712345678", "+263712345678"},
    {"0734567890", "+263734567890"},
    
    // Numbers with country code
    {"+1234567890", "+1234567890"},
    {"+44 20 7946 0958", "+442079460958"},
    
    // Other formats (will use default country code)
    {"1234567890", "+2631234567890"},
    {"(*************", "+2635551234567"},
}
```

### Validation Tests
Phone number validation ensures proper formatting:

```go
tests := []struct {
    input    string
    expected bool
}{
    // Zimbabwe numbers
    {"0774175438", true}, // Will be formatted to +263774175438
    {"0712345678", true}, // Will be formatted to +263712345678
    
    // Numbers with country code
    {"+1234567890", true},
    {"+44 20 7946 0958", true},
    
    // Other valid formats
    {"1234567890", true}, // Will be formatted to +2631234567890
    
    // Invalid formats
    {"invalid", false},
    {"123", false}, // Too short
    {"", false},    // Empty
}
```

## Benefits

1. **Automatic Detection**: No manual configuration needed for Zimbabwe numbers
2. **Consistent Formatting**: All Zimbabwe numbers are properly formatted for international delivery
3. **Backward Compatibility**: Existing numbers with country codes are unchanged
4. **Flexible**: Supports other country codes for non-Zimbabwe numbers
5. **Tested**: Comprehensive test coverage ensures reliability

## Migration Notes

- Existing phone numbers in the database will be automatically formatted when alerts are sent
- No database migration is required for existing data
- The formatting happens at runtime during alert sending
- JSON array support allows multiple phone numbers per client 