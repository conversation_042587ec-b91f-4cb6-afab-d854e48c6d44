# Reverse Geocoding Cron Job

## Overview

The reverse geocoding cron job automatically updates the `location_name` field in GPS data records using the OpenStreetMap Nominatim API. This provides human-readable location names for GPS coordinates.

## Features

- **Automatic Processing**: Runs every minute via cron job
- **Rate Limiting**: Respects Nominatim API's 1 request per second limit
- **Batch Processing**: Processes up to 20 records per run for minute-by-minute processing
- **Error Handling**: Continues processing even if individual records fail
- **Ordered Processing**: Processes records by `created_at DESC` (newest first)
- **Smart Caching**: In-memory cache to avoid duplicate API calls for nearby coordinates
- **Overlap Prevention**: Prevents multiple concurrent runs

## API Usage

The service uses the OpenStreetMap Nominatim API:
- **Base URL**: `https://nominatim.openstreetmap.org/reverse`
- **Rate Limit**: 1 request per second
- **User Agent**: Required header set to "YoTracker/1.0"
- **Format**: JSON response with address details

## Location Name Format

The service builds location names in the following priority order:
1. Road name (if available)
2. Suburb/Town/City/Village (first available)
3. County/State (first available)
4. Country

Example output: `"Samora Machel Avenue, Harare, Harare Province, Zimbabwe"`

## Cron Schedule

```bash
# Runs every minute
* * * * *
```

## Implementation Details

### Service Location
- **Service**: `internal/service/reverse_geocoding_service.go`
- **Cron Integration**: `cmd/cron/cron.go`
- **Model**: `internal/models/gps_data.go`

### Database Query
```sql
SELECT * FROM gps_data 
WHERE location_name IS NULL OR location_name = '' 
ORDER BY created_at DESC 
LIMIT 20
```

### Rate Limiting
The service automatically sleeps for 1 second between API requests to respect the Nominatim rate limit.

### Caching
The service implements an in-memory cache to avoid duplicate API calls:

- **Cache Key**: Coordinates rounded to 4 decimal places (~11 meters precision)
- **Cache Radius**: Dynamic based on location type:
  - 100 meters for street-level locations
  - 1 km for cities/towns
  - 5 km for provinces/states
- **Cache TTL**: 7 days for cache hits, 30 days for cache cleanup
- **Thread Safety**: Uses RWMutex for concurrent access

#### Cache Benefits
- **Reduced API Calls**: Nearby coordinates reuse cached results
- **Faster Processing**: Cache hits are nearly instant
- **Cost Savings**: Fewer API requests to Nominatim
- **Better Performance**: Subsequent cron runs are much faster

## Testing

### Unit Tests
```bash
go test ./internal/service -run TestReverseGeocodingService
```

### Manual Testing
```bash
go run examples/reverse_geocoding_usage.go
```

## Configuration

No additional configuration is required. The service uses default settings:
- **Timeout**: 10 seconds per request
- **Batch Size**: 20 records per run (configurable)
- **Language**: English (`accept-language: en`)
- **Frequency**: Every minute
- **Overlap Protection**: Prevents concurrent runs

## Error Handling

The service handles various error scenarios:
- **Network errors**: Logged and skipped
- **API errors**: Logged and skipped
- **Database errors**: Logged and skipped
- **Invalid coordinates**: Logged and skipped

## Monitoring

The cron job logs the following information:
- Start and completion messages
- Number of records found and processed
- Individual record processing status
- Error messages for failed operations

## Example Log Output

```
Starting GPS location names processing...
Found 20 GPS data records without location names
Processing record 1/20 (ID: 12345, Lat: -17.821600, Lon: 31.049200)
Updated record 12345 with location: Samora Machel Avenue, Harare, Harare Province, Zimbabwe (API CALL)
Processing record 2/20 (ID: 12346, Lat: -17.825000, Lon: 31.050000)
Updated record 12346 with location: Enterprise Road, Harare, Harare Province, Zimbabwe (API CALL)
Processing record 3/20 (ID: 12347, Lat: -17.821700, Lon: 31.049300)
Updated record 12347 with location: Samora Machel Avenue, Harare, Harare Province, Zimbabwe (CACHE HIT)
...
Completed processing 20 GPS data records
Cache hits: 8, API calls: 12
GPS location names processing completed successfully
```

**Note**: If a previous run is still processing, you'll see:
```
GPS location names processing already running, skipping...
```

## Integration with GPS Data

The `LocationName` field is automatically populated for new GPS data records that don't have location names. The minute-by-minute cron job ensures that location names are updated almost instantly as new GPS data comes in, providing real-time location information for tracking applications.

### Benefits of Minute-by-Minute Processing

- **Real-time Updates**: Location names are available within 1 minute of GPS data being recorded
- **Better User Experience**: Users see meaningful location names instead of coordinates almost instantly
- **Minimal Backlog**: Very small batch sizes prevent any accumulation of unprocessed records
- **Cache Efficiency**: Frequent runs maximize cache hit rates for nearby locations
- **Cost Optimization**: Small batches minimize the impact of API rate limits
- **Overlap Protection**: Prevents multiple concurrent runs from overwhelming the system

## Future Enhancements

Potential improvements:
- **Caching**: Cache location names to avoid repeated API calls for nearby coordinates
- **Batch API**: Use Nominatim's batch reverse geocoding endpoint (if available)
- **Fallback APIs**: Implement fallback geocoding services
- **Custom Mapping**: Allow custom location name formatting per client
- **Geographic Boundaries**: Filter by geographic regions to improve accuracy 