# Slack Service Documentation

The Slack service provides comprehensive integration with Slack for sending alert notifications and custom messages. It supports both Slack Bot Token authentication and Webhook URLs, with rich message formatting using Slack blocks and styling.

## Features

- **Dual Authentication**: Support for Slack Bot Token and Webhook URLs
- **Rich Formatting**: Uses Slack blocks for professional message styling
- **Alert Integration**: Automatic Slack notifications for device alerts
- **Raw Data Logging**: Formatted hex data logging for device debugging
- **Color Coding**: Alert severity-based color coding (red for critical, orange for warning, green for info)
- **Configurable**: Settings-based configuration with environment variable fallbacks
- **Test-Friendly**: Mocked external calls in test environment
- **Production Ready**: Clean logging with only warnings and errors

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Slack Configuration (choose one or both methods)
# Method 1: Bot Token (Recommended for production)
SLACK_BOT_TOKEN=xoxb-your-bot-token-here

# Method 2: Webhook URL (Simple setup, limited functionality)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url
```

### Database Settings

The following settings are automatically seeded and can be configured via the admin panel:

- `slack_bot_token` - Slack Bot Token (if not using environment variable)
- `slack_webhook_url` - Slack Webhook URL (if not using environment variable)
- `slack_default_channel` - Default channel for alerts (default: #alerts)
- `slack_alerts_enabled` - Enable/disable Slack alerts (true/false)
- `slack_alert_severity_filter` - Comma-separated list of alert levels to send (e.g., "warning,critical")
- `slack_raw_data_enabled` - Enable/disable raw data logging (true/false)
- `slack_raw_data_channel` - Channel for raw data logs (default: #raw-data)

### Authentication Priority

The service tries authentication methods in this order:
1. **Bot Token** (recommended for production)
2. **Webhook URL** (simplest setup, limited functionality)

### Setting up Bot Token

1. Go to [Slack API Apps](https://api.slack.com/apps)
2. Create a new app or select existing app
3. Go to "OAuth & Permissions"
4. Add the following scopes:
   - `chat:write` - Send messages to channels
   - `chat:write.public` - Send messages to channels the app isn't in (optional)
5. Install the app to your workspace
6. Copy the "Bot User OAuth Token" (starts with `xoxb-`)
7. Add the bot to channels where you want to post messages

## Usage

### Basic Service Initialization

```go
import "yotracker/internal/service"

// Initialize Slack service
slackService, err := service.NewSlackService()
if err != nil {
    log.Printf("Failed to initialize Slack service: %v", err)
    return
}

// Check if Slack is enabled
if !slackService.IsEnabled() {
    log.Println("Slack notifications are disabled")
    return
}
```

### Sending Alert Notifications

```go
// Create an alert
alert := &service.Alert{
    DeviceId:  "DEVICE123",
    AlertType: "warning",
    AlertName: "Speed Limit Exceeded",
    Message:   "Vehicle exceeded speed limit of 60 km/h",
    Speed:     75.5,
    Direction: "North",
    Timestamp: time.Now(),
}

// Get client device for additional context
var clientDevice models.ClientDevice
config.DB.First(&clientDevice, deviceId)

// Send alert to Slack
err := slackService.SendAlert(alert, &clientDevice)
if err != nil {
    log.Printf("Failed to send Slack alert: %v", err)
}
```

### Sending Custom Messages

```go
// Create custom blocks
blocks := []service.SlackBlock{
    {
        Type: "header",
        Text: &service.SlackText{
            Type: "plain_text",
            Text: "System Notification",
        },
    },
    {
        Type: "section",
        Text: &service.SlackText{
            Type: "mrkdwn",
            Text: "This is a custom message with *bold* and _italic_ text.",
        },
    },
    {
        Type: "actions",
        Elements: []service.SlackElement{
            {
                Type: "button",
                Text: &service.SlackText{
                    Type: "plain_text",
                    Text: "View Details",
                },
                URL:      "https://yourdomain.com/details",
                ActionID: "view_details",
                Style:    "primary",
            },
        },
    },
}

// Send custom message
err := slackService.SendCustomMessage("#general", "System Notification", blocks)
if err != nil {
    log.Printf("Failed to send custom message: %v", err)
}
```

### Sending to Specific Webhook

```go
// Send to a specific webhook URL
webhookURL := "*****************************************************************************"

message := &service.SlackMessage{
    Text: "Alert from specific webhook",
    Blocks: blocks,
}

err := slackService.SendToWebhook(webhookURL, message)
if err != nil {
    log.Printf("Failed to send webhook message: %v", err)
}
```

### Sending Raw Device Data Logs

```go
// Log raw hex data from device communication
deviceName := "Fleet Vehicle 001"
deviceId := "DEVICE123"
rawHexData := "24244750524D432C3132333435362E3030302C412C323334352E363738392C4E"

err := slackService.SendRawDataLog(deviceName, deviceId, rawHexData)
if err != nil {
    log.Printf("Failed to send raw data log: %v", err)
}
```

## Alert Message Format

Alert messages are automatically formatted with rich blocks including:

- **Header**: Alert name or type with emoji
- **Device Information**: Device ID, alert type, device name, plate number
- **Alert Details**: Message, speed, direction (if available)
- **Timestamp**: When the alert occurred
- **Action Button**: Link to dashboard

### Raw Data Message Format

Raw data messages use a simplified block structure for better compatibility:

- **Device Header**: Device name and ID with emoji indicator
- **Hex Data**: Formatted hex data in code blocks with byte count
- **Timestamp**: When the data was logged with context note

### Color Coding

- 🔴 **Critical**: Red (#FF0000) - for critical alerts and emergencies
- 🟠 **Warning**: Orange (#FFA500) - for warnings and alerts
- 🟢 **Info**: Green (#36A64F) - for informational messages

## Integration with Alerts

The Slack service is automatically integrated with the alert system:

### Automatic Notifications

When alerts are created via:
- `CreateAlert` API endpoint
- `Alert.SaveAlert()` method in alert data service

Slack notifications are automatically sent if:
- Slack is enabled (`slack_alerts_enabled = true`)
- Alert severity matches the filter (`slack_alert_severity_filter`)
- Valid Slack configuration exists

### Email + Slack Integration

Use the combined helper function for both email and Slack notifications:

```go
import "yotracker/internal/mail"

// Send both email and Slack notifications
err := mail.SendDeviceAlertWithSlack(
    userEmail,
    deviceId,
    alertType,
    alertMessage,
    alert,
    clientDevice,
)
```

## Testing

The service includes comprehensive tests and automatically skips external API calls in test mode:

```bash
# Run Slack service tests
go test ./internal/service -run TestSlackService

# Run all service tests
go test ./internal/service
```

### Test Mode Behavior

When `GO_ENV=test` or `TESTING_DB_NAME` is set:
- No actual HTTP requests are made to Slack
- All operations return successfully
- Logs indicate test mode operation

## Error Handling

The service includes robust error handling:

- **Configuration Errors**: Clear messages when tokens/webhooks are missing
- **API Errors**: Detailed Slack API error responses
- **Network Errors**: HTTP timeout and connection error handling
- **Graceful Degradation**: Continues operation if Slack is unavailable

## Security Considerations

- **Token Security**: Store bot tokens in environment variables, not in database
- **Webhook URLs**: Treat webhook URLs as sensitive credentials
- **Channel Permissions**: Ensure bot has permission to post to configured channels
- **Rate Limiting**: Slack API has rate limits; service handles basic retry logic

## Troubleshooting

### Common Issues

1. **"either SLACK_BOT_TOKEN or SLACK_WEBHOOK_URL must be configured"**
   - Set at least one authentication method in environment variables or settings

2. **"Slack API error: invalid_auth"**
   - Check that your bot token is valid and has necessary permissions

3. **"webhook returned status 404"**
   - Verify webhook URL is correct and webhook is still active

4. **Messages not appearing (200 response but no message)**
   - **Most common issue**: Bot token lacks `chat:write` scope
   - Bot is not added to the target channel
   - Channel name is incorrect (try using channel ID instead)
   - Bot doesn't have permission to post in the channel
   - Check the detailed API response logs for warnings

5. **Channel not found errors**
   - Use channel ID (e.g., `C1234567890`) instead of channel name
   - Ensure channel is public or bot is invited to private channels

6. **Permission denied errors**
   - Add bot to the workspace if not already added
   - Ensure bot has `chat:write` scope in OAuth settings
   - Check that bot is added to the specific channel

### Debug Mode

Enable debug logging to troubleshoot issues:

```go
import "log"

// Enable detailed logging
log.SetFlags(log.LstdFlags | log.Lshortfile)
```

## Best Practices

1. **Use Bot Tokens for Production**: More reliable than webhooks
2. **Configure Severity Filters**: Avoid notification spam
3. **Test in Development**: Use test mode to avoid sending test alerts
4. **Monitor Rate Limits**: Implement backoff for high-volume alerts
5. **Fallback Strategy**: Always have email as backup notification method
