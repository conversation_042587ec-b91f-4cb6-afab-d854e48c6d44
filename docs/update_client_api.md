# Update Client API Documentation

## Overview
The UpdateClient functionality allows authenticated frontend users to update their own client information. This endpoint is specifically designed for frontend users to manage their client profile data.

## Endpoint

### Frontend (Client Users)
- **URL**: `PUT /api/v1/frontend/clients`
- **Authentication**: Required (Bearer token)
- **Middleware**: AuthMiddleware, CheckForClient

## Request Format

### Headers
```
Content-Type: application/json
Authorization: Bearer <your_jwt_token>
```

### Request Body
```json
{
  "name": "Updated Client Name",
  "email": "<EMAIL>",
  "phone_number": "**********",
  "company": "Updated Company Ltd",
  "state": "California",
  "city": "San Francisco",
  "town": "Downtown",
  "address": "123 Updated Street, Suite 456",
  "gender": "male",
  "description": "Updated client description",
  "maps_provider": "mapbox",
  "default_landing_page": "devices"
}
```

### Field Validation
- `name`: Required, string
- `email`: Required, valid email format (must be unique)
- `phone_number`: Required, string
- `company`: Optional, string
- `state`: Optional, string
- `city`: Optional, string
- `town`: Optional, string
- `address`: Optional, string
- `gender`: Optional, string
- `description`: Optional, string
- `maps_provider`: Optional, string (e.g., "google", "mapbox")
- `default_landing_page`: Optional, string (e.g., "dashboard", "devices")

## Response Format

### Success Response (200 OK)
```json
{
  "message": "Client updated successfully"
}
```

### Error Responses

#### Validation Error (400 Bad Request)
```json
{
  "message": "Key: 'UpdateClientRequest.Email' Error:Tag 'email'"
}
```

#### Email Already Exists (400 Bad Request)
```json
{
  "message": "Email already exists"
}
```

#### Client Not Found (404 Not Found)
```json
{
  "message": "Client not found"
}
```

#### Unauthorized (401 Unauthorized)
```json
{
  "message": "Unauthorized"
}
```

#### Server Error (500 Internal Server Error)
```json
{
  "message": "Failed to update client"
}
```

## Security Features

1. **Authentication Required**: Users must be logged in with a valid JWT token
2. **Self-Update Only**: Users can only update their own client information
3. **Email Uniqueness**: Prevents duplicate emails across all clients
4. **Same Email Allowed**: Users can keep their current email when updating other fields
5. **Input Validation**: All fields are validated according to their constraints
6. **Limited Fields**: Only allows updating client profile fields, not admin fields

## Validation Rules

### Email Validation
- Must be a valid email format
- Must be unique across all clients (except current client)
- Required field

### Phone Number Validation
- Required field
- String format (allows various phone number formats)

### Other Fields
- `name`: Required, cannot be empty
- All other fields are optional and can be null/empty

## Fields NOT Allowed for Frontend Users

The following fields are restricted and cannot be updated by frontend users:
- `client_type`: Admin-only field
- `status`: Admin-only field
- `created_by_id`: System field
- `referred_by_id`: Admin-only field
- `staff_id`: Admin-only field
- `country_id`: Admin-only field
- `currency_id`: Admin-only field
- `billing_cycle`: Admin-only field
- `billing_day`: Admin-only field
- `is_lifetime`: Admin-only field
- `next_billing_date`: Admin-only field
- `last_billed_at`: System field
- `suspended_at`: System field

## Example Usage

### Using curl
```bash
curl -X PUT \
  http://localhost:9001/api/v1/frontend/clients \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your_jwt_token_here' \
  -d '{
    "name": "Updated Client Name",
    "email": "<EMAIL>",
    "phone_number": "**********",
    "company": "Updated Company Ltd",
    "state": "California",
    "city": "San Francisco",
    "town": "Downtown",
    "address": "123 Updated Street, Suite 456",
    "gender": "male",
    "description": "Updated client description",
    "maps_provider": "mapbox",
    "default_landing_page": "devices"
  }'
```

### Using JavaScript (fetch)
```javascript
const response = await fetch('/api/v1/frontend/clients', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    name: 'Updated Client Name',
    email: '<EMAIL>',
    phone_number: '**********',
    company: 'Updated Company Ltd',
    state: 'California',
    city: 'San Francisco',
    town: 'Downtown',
    address: '123 Updated Street, Suite 456',
    gender: 'male',
    description: 'Updated client description',
    maps_provider: 'mapbox',
    default_landing_page: 'devices'
  })
});

const result = await response.json();
console.log(result.message);
```

## Implementation Details

### Models
- Uses `UpdateClientRequest` struct for request validation
- Defined in `internal/models/client.go`

### Controllers
- Frontend: `cmd/web/frontend/controllers/clients.go`
- Function: `UpdateClient`

### Database
- Updates multiple fields in the `clients` table
- Uses GORM for database operations
- Performs email uniqueness checks before updating

### Uniqueness Validation
- Email: Checks if email exists for any other client
- Allows users to keep their current email

## Differences from Backend Client Update

- **Frontend UpdateClient**: Users update their own client only
- **Backend UpdateClient**: Admins can update any client (including admin fields)
- **Frontend**: No admin fields like ClientType, Status, billing settings
- **Frontend**: No ID parameter needed (uses current user's client)
- **Frontend**: Limited to profile fields only

## Removed Methods

The following methods were removed from the frontend clients controller as they are not needed for frontend users:

- `GetAllClients`: Frontend users don't need to see all clients
- `GetClientById`: Frontend users only need their own client info (available via user profile)
- `CreateClient`: Client creation is handled during user registration
- `SearchClients`: Not needed for frontend users

## Testing

Comprehensive tests are available in:
- `cmd/web/frontend/controllers/clients_update_test.go`

Test scenarios include:
- Successful client update
- Email already exists validation
- Same email update allowed
- Missing required fields
- Invalid email format
- Unauthorized access

## Route Configuration

The route is configured in `cmd/web/frontend/routes/routes.go`:
```go
clients := v1.Group("/clients")
clients.PUT("", controllers.UpdateClient)
```

Note: No ID parameter is needed since users can only update their own client.
