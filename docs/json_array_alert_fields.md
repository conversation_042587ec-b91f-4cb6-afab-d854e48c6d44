# JSON Array Support for Alert Fields

## Overview

The client model now supports JSON arrays for WhatsApp phone numbers, SMS phone numbers, and alert email addresses. This allows clients to have multiple contact methods for receiving alerts.

## Database Schema Changes

The following fields in the `clients` table now store JSON arrays:

- `whatsapp_phone_number` - JSON array of WhatsApp phone numbers
- `sms_phone_number` - JSON array of SMS phone numbers  
- `alerts_email` - JSON array of alert email addresses

### Migration

Run the migration file `migrations/alert_fields_json_migration.sql` to:
1. Update column types to TEXT for JSON storage
2. Convert existing single values to JSON array format
3. Add database indexes for better performance
4. Add documentation comments

## Model Methods

The `Client` model provides helper methods for working with JSON arrays:

### WhatsApp Phone Numbers

```go
// Get all WhatsApp phone numbers as a slice
numbers := client.GetWhatsappPhoneNumbers()

// Set WhatsApp phone numbers from a slice
client.SetWhatsappPhoneNumbers([]string{"+1234567890", "+0987654321"})

// Add a single WhatsApp phone number
client.AddWhatsappPhoneNumber("+1234567890")

// Remove a WhatsApp phone number
client.RemoveWhatsappPhoneNumber("+1234567890")
```

### SMS Phone Numbers

```go
// Get all SMS phone numbers as a slice
numbers := client.GetSmsPhoneNumbers()

// Set SMS phone numbers from a slice
client.SetSmsPhoneNumbers([]string{"+1234567890", "+0987654321"})

// Add a single SMS phone number
client.AddSmsPhoneNumber("+1234567890")

// Remove an SMS phone number
client.RemoveSmsPhoneNumber("+1234567890")
```

### Alert Emails

```go
// Get all alert email addresses as a slice
emails := client.GetAlertEmails()

// Set alert email addresses from a slice
client.SetAlertEmails([]string{"<EMAIL>", "<EMAIL>"})

// Add a single alert email
client.AddAlertEmail("<EMAIL>")

// Remove an alert email
client.RemoveAlertEmail("<EMAIL>")
```

## Backward Compatibility

The helper methods provide backward compatibility:

- If a field contains a single value (non-JSON), it will be treated as a single-item array
- If a field is nil or empty, an empty slice is returned
- JSON unmarshaling errors fall back to treating the value as a single string

## Alert Service Integration

The alert services have been updated to send alerts to all configured contact methods:

### WhatsApp Service
- Sends messages to all WhatsApp phone numbers
- Continues sending to other numbers if one fails
- Returns the last error encountered

### SMS Service  
- Sends messages to all SMS phone numbers
- Continues sending to other numbers if one fails
- Returns the last error encountered

### Email Service
- Sends emails to all alert email addresses
- Falls back to main client email if no alert emails configured
- Continues sending to other emails if one fails
- Returns the last error encountered

## Example Usage

### Creating a client with multiple contact methods

```go
client := &models.Client{
    Name: "Test Company",
    Email: "<EMAIL>",
}

// Set multiple WhatsApp numbers
client.SetWhatsappPhoneNumbers([]string{
    "+1234567890",
    "+0987654321", 
    "+1122334455",
})

// Set multiple SMS numbers
client.SetSmsPhoneNumbers([]string{
    "+1234567890",
    "+0987654321",
})

// Set multiple alert emails
client.SetAlertEmails([]string{
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
})
```

### Adding contact methods dynamically

```go
// Add new WhatsApp number
client.AddWhatsappPhoneNumber("+1555123456")

// Add new SMS number
client.AddSmsPhoneNumber("+1555123456")

// Add new alert email
client.AddAlertEmail("<EMAIL>")
```

### Sending alerts to all contact methods

```go
// The alert services automatically handle multiple recipients
err := service.SendAlertToClient(client, alert, device)
if err != nil {
    // Handle error (may be partial failure)
    log.Printf("Alert sending errors: %v", err)
}
```

## Database Storage Format

The JSON arrays are stored as text in the database:

```sql
-- Example of stored data
whatsapp_phone_number: '["+1234567890", "+0987654321", "+1122334455"]'
sms_phone_number: '["+1234567890", "+0987654321"]'
alerts_email: '["<EMAIL>", "<EMAIL>", "<EMAIL>"]'
```

## Testing

Run the tests to verify JSON array functionality:

```bash
go test ./internal/models -v -run TestClient
```

The tests cover:
- Getting/setting JSON arrays
- Adding/removing individual items
- Backward compatibility with single values
- Error handling for malformed JSON
- Duplicate prevention in add methods 