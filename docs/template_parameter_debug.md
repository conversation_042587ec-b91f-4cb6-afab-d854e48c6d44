# WhatsApp Template Parameter Debug Guide

## Issue: URL Parameter Not Replaced

### Problem
- **URL Received**: `https://app.yotracker.co.zw/devices/%7B%7B1%7D%7D1`
- **Decoded**: `https://app.yotracker.co.zw/devices/{{1}}1`
- **Expected**: `https://app.yotracker.co.zw/devices/123`

### Root Cause Analysis

The template parameter `{{1}}` is not being replaced with the actual device ID. This suggests:

1. **Template Structure Mismatch**: Your WhatsApp template might have a different parameter structure
2. **Parameter Order Issue**: The parameters might be in the wrong order
3. **Template Configuration**: The template might not be configured correctly

## Debug Steps

### Step 1: Check Your WhatsApp Template Structure

Go to WhatsApp Business Manager and check your `device_alert` template:

1. **Template Name**: `device_alert`
2. **Language**: `en`
3. **Status**: Active - Quality

### Step 2: Verify Template Parameters

Your template should have this structure:
```
Body: {{1}} - {{2}}
Button: {{1}}
```

### Step 3: Check Debug Output

Run a test alert and check the debug output:

```
DEBUG: SendDeviceAlertTemplate - Parameters:
DEBUG: - alertTitle: Geofence Alert
DEBUG: - alertMessage: Device Toyota Hilux (ABC123) entered geofence 'Home Zone'
DEBUG: - deviceId: 123
DEBUG: - parameters array: [Geofence Alert Device Toyota Hilux (ABC123) entered geofence 'Home Zone' 123]

DEBUG: Body Parameters: [{Type:text Text:Geofence Alert} {Type:text Text:Device Toyota Hilux (ABC123) entered geofence 'Home Zone'}]
DEBUG: Button Parameters: [{Type:text Text:123}]
```

### Step 4: Check WhatsApp API Response

Look for any error messages in the API response that might indicate parameter issues.

## Possible Solutions

### Solution 1: Template Parameter Order

If your template has a different parameter order, update the code:

```go
// Current (assuming template structure):
// Body: {{1}} - {{2}}
// Button: {{1}}

// If your template is different, adjust accordingly:
// Body: {{1}} - {{2}}  
// Button: {{3}} (if button uses {{3}} instead of {{1}})
```

### Solution 2: Template Structure

Your template might need to be updated in WhatsApp Business Manager:

1. **Body**: `{{1}} - {{2}}`
2. **Button URL**: `https://app.yotracker.co.zw/devices/{{1}}`

### Solution 3: Parameter Mapping

The issue might be that the button parameter is not being mapped correctly. Check if your template uses:
- `{{1}}` for both body and button (current assumption)
- `{{3}}` for button (different parameter)

## Test Template Structure

### Current Assumption:
```
Template: device_alert
Body: {{1}} - {{2}}
Button URL: https://app.yotracker.co.zw/devices/{{1}}
```

### Alternative Structure (if needed):
```
Template: device_alert
Body: {{1}} - {{2}}
Button URL: https://app.yotracker.co.zw/devices/{{3}}
```

## Next Steps

1. **Check your actual template structure** in WhatsApp Business Manager
2. **Run a test alert** and check the debug output
3. **Verify the parameter mapping** matches your template
4. **Update the code** if the template structure is different

## Expected Working URL

After fixing, you should get:
```
https://app.yotracker.co.zw/devices/123
```

Where `123` is the actual `ClientDevice.Id`. 