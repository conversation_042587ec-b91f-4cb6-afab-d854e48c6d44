# WhatsApp Business API Templates Guide

## Overview

WhatsApp Business API requires pre-approved message templates for business-initiated messages. You cannot send free-form messages to customers without using approved templates.

## Template Types

### 1. Text Templates
Simple text messages with dynamic parameters.

### 2. Interactive Templates
Messages with buttons for user interaction.

### 3. Media Templates
Messages with images, documents, or videos.

## Required Templates for Yotracker

### 1. Universal Alert Template
**Template Name**: `device_alert`  
**Category**: Utility  
**Language**: English (en)

**Template Content**:
```
🚨 {{1}}
{{2}}

Login to our portal to view more details
```

**Button Footer**:
```
[View Device] - Uses {{1}} parameter (Device ID)
```

**Parameters**:
- `{{1}}` (Body) - Alert title (e.g., "Speed Alert", "Geofence Alert")
- `{{2}}` (Body) - Alert message
- `{{1}}` (Button) - URL parameter (device ID)

## Step-by-Step Template Creation

### Step 1: Access WhatsApp Business Manager
1. Go to [business.facebook.com](https://business.facebook.com)
2. Navigate to your WhatsApp Business account
3. Go to **Messaging** → **Message Templates**

### Step 2: Create Universal Alert Template
1. Click **Create Template**
2. Fill in the details:
   - **Template Name**: `device_alert`
   - **Category**: Utility
   - **Language**: English
   - **Template Content**: Copy the content above
3. **Add Button Footer**:
   - Click **Add Button**
   - Button Text: "View Device"
   - Button Type: "URL"
   - URL: Your portal URL with device ID parameter
4. Click **Create Template**

### Step 3: Submit for Approval
1. Review your templates
2. Click **Submit for Review**
3. Wait for approval (24-48 hours typically)

## Template Approval Guidelines

### Do's ✅
- Use clear, professional language
- Include relevant information only
- Follow WhatsApp's content policies
- Use appropriate categories

### Don'ts ❌
- Use promotional language in utility templates
- Include spam or misleading content
- Use inappropriate categories
- Include personal information

## Implementation in Code

### Template Message Structure
```go
type TemplateContent struct {
    Name       string              `json:"name"`
    Language   *Language           `json:"language"`
    Components []TemplateComponent `json:"components,omitempty"`
}

type TemplateComponent struct {
    Type       string              `json:"type"`
    Parameters []TemplateParameter `json:"parameters,omitempty"`
}

type TemplateParameter struct {
    Type string `json:"type"`
    Text string `json:"text,omitempty"`
}
```

### Sending Template Messages
```go
// Send device alert template
err := whatsappService.SendDeviceAlertTemplate(
    phoneNumber,
    "Device-001",
    "geofence",
    "Device has left the designated area",
    "123" // Device ID for button URL
)

// Send speed alert template
err := whatsappService.SendDeviceAlertTemplate(
    phoneNumber,
    "Device-001", 
    "speed",
    "Device exceeded speed limit of 80 km/h",
    "123" // Device ID for button URL
)
```

## Template Management

### Settings Configuration
Add template settings to your database:

```sql
INSERT INTO settings (name, setting_key, setting_value, category) VALUES
('WhatsApp Device Alert Template', 'whatsapp_device_alert_template', 'device_alert', 'notifications'),
('WhatsApp Template Language', 'whatsapp_template_language', 'en_US', 'notifications'),
('Dashboard URL', 'dashboard_url', 'https://yotracker.co.zw/dashboard', 'system');
```

### Dynamic Template Selection
```go
// Get template name from settings
templateName := models.GetSetting("whatsapp_device_alert_template")
if templateName == "" {
    templateName = "device_alert" // Default fallback
}

// Get language from settings
language := models.GetSetting("whatsapp_template_language")
if language == "" {
    language = "en_US" // Default fallback
}
```

## Testing Templates

### Test Mode
```go
// Enable test mode
os.Setenv("TEST_ENV", "true")

// Test template sending
err := whatsappService.SendDeviceAlertTemplate(
    "+263774175438",
    "Test Device",
    "test",
    "This is a test alert",
    "https://test.yotracker.co.zw/dashboard"
)
```

### Template Validation
```go
// Validate template parameters
func validateTemplateParameters(templateName string, parameters []string) error {
    switch templateName {
    case "device_alert":
        if len(parameters) != 4 {
            return fmt.Errorf("device_alert template requires 4 parameters")
        }
    case "geofence_alert":
        if len(parameters) != 2 {
            return fmt.Errorf("geofence_alert template requires 2 parameters")
        }
    default:
        return fmt.Errorf("unknown template: %s", templateName)
    }
    return nil
}
```

## Error Handling

### Common Template Errors
1. **Template Not Found**: Template name doesn't exist
2. **Template Not Approved**: Template is pending approval
3. **Invalid Parameters**: Wrong number or type of parameters
4. **Language Mismatch**: Template language doesn't match

### Error Response Example
```json
{
  "error": {
    "message": "Template 'device_alert' not found",
    "type": "OAuthException",
    "code": 100,
    "error_subcode": 33
  }
}
```

## Best Practices

### 1. Template Design
- Keep messages concise and clear
- Use appropriate emojis sparingly
- Include actionable information
- Test with different parameter lengths

### 2. Parameter Management
- Validate parameters before sending
- Handle empty or null parameters gracefully
- Truncate long parameters if needed
- Use meaningful default values

### 3. Error Handling
- Log template errors for debugging
- Implement fallback templates
- Retry failed sends with exponential backoff
- Monitor template usage and success rates

### 4. Performance
- Cache template configurations
- Batch template sends when possible
- Monitor API rate limits
- Use appropriate timeouts

## Monitoring and Analytics

### Track Template Performance
```go
type TemplateMetrics struct {
    TemplateName    string
    SentCount       int
    SuccessCount    int
    ErrorCount      int
    AverageResponseTime time.Duration
}

func trackTemplateMetrics(templateName string, success bool, responseTime time.Duration) {
    // Implement metrics tracking
}
```

### Dashboard Integration
- Monitor template approval status
- Track delivery success rates
- Analyze user engagement
- Optimize template content

## Troubleshooting

### Template Not Working
1. Check if template is approved
2. Verify template name spelling
3. Ensure correct parameter count
4. Check language code

### Approval Issues
1. Review content for policy violations
2. Use appropriate category
3. Ensure professional language
4. Contact WhatsApp support if needed

### API Errors
1. Check authentication tokens
2. Verify phone number format
3. Monitor rate limits
4. Review error responses

## Next Steps

1. **Create Templates**: Set up the required templates in WhatsApp Business Manager
2. **Submit for Approval**: Wait for template approval
3. **Test Implementation**: Use test mode to verify functionality
4. **Monitor Performance**: Track template success rates
5. **Optimize**: Refine templates based on user feedback 