# Email Alert Troubleshooting Guide

## Issue: Email Alerts Not Sending

### Debug Steps

## 1. Check Email Alert Service Status

### Global Email Alerts Setting
Check if email alerts are enabled globally:
```sql
SELECT * FROM settings WHERE setting_key = 'email_alerts_enabled';
```

**Expected**: `email_alerts_enabled` should be `true` or `1`

### Client Email Alerts Setting
Check if the specific client has email alerts enabled:
```sql
SELECT id, name, email_alerts_enabled, alerts_email FROM clients WHERE id = YOUR_CLIENT_ID;
```

**Expected**: `email_alerts_enabled` should be `true` or `1`

## 2. Check Email Configuration

### Mail Service Settings
Check your mail service configuration:
```sql
SELECT * FROM settings WHERE setting_key LIKE '%mail%' OR setting_key LIKE '%smtp%';
```

**Required Settings**:
- `mail_host` - SMTP server (e.g., smtp.gmail.com)
- `mail_port` - SMTP port (e.g., 587)
- `mail_username` - Email username
- `mail_password` - Email password
- `mail_from_address` - From email address
- `mail_from_name` - From name

## 3. Check Alert Email Addresses

### Client Alert Emails
Check if the client has alert email addresses configured:
```sql
SELECT id, name, alerts_email FROM clients WHERE id = YOUR_CLIENT_ID;
```

**Expected**: `alerts_email` should contain valid email addresses (JSON array)

### Fallback to Main Email
If no alert emails are configured, the system falls back to the client's main email address.

## 4. Debug Output Analysis

When you trigger an alert, you should see debug output like:

```
Email Alert Service - Starting email alert
Email Alert Service - Alert emails from client: [<EMAIL>]
Email Alert Service - Sending emails to: [<EMAIL>]
Email Alert Service - Sending email to: <EMAIL>
Email Alert Service - Successfully sent <NAME_EMAIL>
```

### Common Debug Messages:

#### "Email alerts are disabled globally"
- **Fix**: Set `email_alerts_enabled` to `true` in settings

#### "Client has email alerts disabled"
- **Fix**: Enable email alerts for the specific client

#### "No alert emails configured, using main email"
- **Fix**: Configure alert emails for the client or ensure main email is valid

#### "Error sending email to..."
- **Fix**: Check mail service configuration (SMTP settings)

## 5. Mail Service Configuration

### Check Mail Service Settings
```sql
SELECT setting_key, setting_value FROM settings 
WHERE setting_key IN ('mail_host', 'mail_port', 'mail_username', 'mail_from_address');
```

### Common SMTP Settings:
- **Gmail**: smtp.gmail.com:587
- **Outlook**: smtp-mail.outlook.com:587
- **Custom**: Your SMTP server details

## 6. Test Email Configuration

### Test Mail Service
You can test the mail service directly:

```go
// Test mail service
mailService, err := service.NewMailService()
if err != nil {
    fmt.Printf("Mail service error: %v\n", err)
    return
}

err = mailService.SendTemplatedEmail("<EMAIL>", "Test Subject", func(template *service.EmailTemplate) *service.EmailTemplate {
    return template.
        SetGreeting("Hello,").
        AddContent("This is a test email").
        SetAction("Test", "https://example.com")
})
```

## 7. Environment Variables

### Check Environment Variables
Make sure these environment variables are set (if using them):
- `MAIL_HOST`
- `MAIL_PORT`
- `MAIL_USERNAME`
- `MAIL_PASSWORD`
- `MAIL_FROM_ADDRESS`

## 8. Common Issues and Solutions

### Issue 1: Email Alerts Disabled
**Problem**: `email_alerts_enabled` is `false`
**Solution**: Update settings table

### Issue 2: Client Email Alerts Disabled
**Problem**: Client has `email_alerts_enabled = false`
**Solution**: Enable for specific client

### Issue 3: No Email Addresses
**Problem**: No alert emails configured
**Solution**: Add email addresses to client's `alerts_email` field

### Issue 4: SMTP Configuration
**Problem**: Mail service not configured
**Solution**: Set up SMTP settings in database

### Issue 5: Authentication Error
**Problem**: SMTP authentication failed
**Solution**: Check username/password and enable "less secure apps" if using Gmail

## 9. Quick Fix Commands

### Enable Global Email Alerts
```sql
UPDATE settings SET setting_value = 'true' WHERE setting_key = 'email_alerts_enabled';
```

### Enable Client Email Alerts
```sql
UPDATE clients SET email_alerts_enabled = true WHERE id = YOUR_CLIENT_ID;
```

### Add Alert Email to Client
```sql
UPDATE clients SET alerts_email = '["<EMAIL>"]' WHERE id = YOUR_CLIENT_ID;
```

## 10. Next Steps

1. **Check debug output** when triggering an alert
2. **Verify email settings** in the database
3. **Test mail service** configuration
4. **Check client email preferences**
5. **Verify SMTP settings** are correct

Run a test alert and share the debug output to identify the specific issue. 