# Multi-Channel Alert System Documentation

This document describes the new multi-channel alert system in YoTracker that supports SMS, WhatsApp, and Email alerts.

## Overview

The alert system allows clients to receive device alerts through multiple channels based on their preferences. Each client can enable/disable different alert types and configure specific contact information for each channel.

## Client Model Updates

The `Client` model has been extended with the following alert-related fields:

```go
type Client struct {
    // ... existing fields ...
    
    // Alert preferences (backend-only, can only be enabled from backend)
    WhatsappAlertsEnabled  *bool   `json:"whatsapp_alerts_enabled" gorm:"default:false"`
    SmsAlertsEnabled       *bool   `json:"sms_alerts_enabled" gorm:"default:false"`
    EmailAlertsEnabled     *bool   `json:"email_alerts_enabled" gorm:"default:false"`
    
    // Alert contact information (can be set by frontend)
    WhatsappPhoneNumber    *string `json:"whatsapp_phone_number"`
    SmsPhoneNumber         *string `json:"sms_phone_number"`
    AlertsEmail            *string `json:"alerts_email"`
}
```

### Field Descriptions

- **WhatsappAlertsEnabled**: Whether WhatsApp alerts are enabled for this client (backend-only)
- **SmsAlertsEnabled**: Whether SMS alerts are enabled for this client (backend-only)
- **EmailAlertsEnabled**: Whether email alerts are enabled for this client (backend-only)
- **WhatsappPhoneNumber**: Phone number for WhatsApp alerts (frontend can set)
- **SmsPhoneNumber**: Phone number for SMS alerts (frontend can set)
- **AlertsEmail**: Email address for alerts (frontend can set, defaults to main email)

## Alert Services

### 1. Twilio SMS Service

SMS alerts are implemented using Twilio's SMS API.

#### Configuration

Set the following environment variables:

```env
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=your_twilio_phone_number
```

#### Global Settings

Add these settings to the database:

```sql
INSERT INTO settings (name, setting_key, setting_value, category) VALUES
('SMS Alerts Enabled', 'sms_alerts_enabled', 'true', 'notifications');
```

### 2. WhatsApp Service

WhatsApp alerts are currently a placeholder implementation for future integration.

#### Configuration

Set the following environment variables (when implemented):

```env
WHATSAPP_API_KEY=your_whatsapp_api_key
WHATSAPP_PHONE_NUMBER_ID=your_whatsapp_phone_number_id
```

#### Global Settings

Add these settings to the database:

```sql
INSERT INTO settings (name, setting_key, setting_value, category) VALUES
('WhatsApp Alerts Enabled', 'whatsapp_alerts_enabled', 'true', 'notifications');
```

### 3. Email Alert Service

Email alerts use the existing mail service with professional templates.

#### Configuration

Uses existing email configuration:

```env
SENDGRID_API_KEY=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>
FROM_NAME=YoTracker
```

#### Global Settings

Add these settings to the database:

```sql
INSERT INTO settings (name, setting_key, setting_value, category) VALUES
('Email Alerts Enabled', 'email_alerts_enabled', 'true', 'notifications');
```

## Usage Examples

### 1. Send Alerts to a Client

```go
import "yotracker/internal/service"

// Create an alert
alert := &models.Alert{
    DeviceId:       stringPtr("DEVICE123"),
    AlertType:      "speed_violation",
    AlertName:      stringPtr("Speed Violation"),
    AlertLevel:     stringPtr("warning"),
    Message:        stringPtr("Vehicle exceeded speed limit of 60 km/h"),
    AlertTimestamp: time.Now(),
}

// Create a client device
clientDevice := &models.ClientDevice{
    Name:        stringPtr("Fleet Vehicle 001"),
    PlateNumber: stringPtr("ABC-123"),
    DeviceId:    "DEVICE123",
}

// Send alerts through all enabled channels
err := service.SendMultiChannelAlert(client, alert, clientDevice)
if err != nil {
    log.Printf("Failed to send alerts: %v", err)
}
```

### 2. Send Specific Type of Alert

```go
// Send SMS alert only
err := service.SendSMSAlert(client, alert, clientDevice)

// Send WhatsApp alert only
err := service.SendWhatsAppAlert(client, alert, clientDevice)

// Send email alert only
err := service.SendEmailAlert(client, alert, clientDevice)
```

### 3. Send Alert by Type

```go
// Send specific type of alert
err := service.SendAlertToClientByType(client, alert, clientDevice, "sms")
err := service.SendAlertToClientByType(client, alert, clientDevice, "whatsapp")
err := service.SendAlertToClientByType(client, alert, clientDevice, "email")
err := service.SendAlertToClientByType(client, alert, clientDevice, "all")
```

### 4. Using AlertManager Directly

```go
// Create alert manager
alertManager, err := service.NewAlertManager()
if err != nil {
    log.Printf("Failed to create alert manager: %v", err)
    return
}

// Send alerts through all services
err = alertManager.SendAlert(client, alert, clientDevice)
```

## Integration with Existing Alert System

The new alert system is automatically integrated with the existing alert data service. When alerts are created via:

- `Alert.SaveAlert()` method
- `CreateAlert` API endpoint

The system will automatically:

1. Save the alert to the database
2. Load the client with alert preferences
3. Send alerts through all enabled channels
4. Continue sending Slack notifications (legacy support)

## Alert Message Format

### SMS Messages

SMS messages are formatted as:

```
Device: Fleet Vehicle 001
ID: DEVICE123
Alert: speed_violation
Type: Speed Violation
Message: Vehicle exceeded speed limit of 60 km/h
Time: 2024-01-15 14:30:25
```

### Email Messages

Email alerts use professional templates with:

- Device information
- Alert details
- Action button to dashboard
- Professional styling

### WhatsApp Messages

WhatsApp messages are currently placeholder implementations.

## Error Handling

The alert system includes comprehensive error handling:

- **Service Initialization**: Graceful handling of missing configuration
- **Client Preferences**: Respects client alert preferences
- **Missing Contact Info**: Clear error messages for missing phone numbers/emails
- **Test Mode**: Automatic skipping in test environments
- **Async Processing**: Non-blocking alert sending

## Testing

The alert system automatically detects test environments and skips actual API calls:

```go
// In test mode, alerts are logged but not sent
if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
    log.Printf("TEST MODE: Skipping SMS alert to %s", client.SmsPhoneNumber)
    return nil
}
```

## Database Migration

To add the new alert fields to the clients table:

```sql
ALTER TABLE clients 
ADD COLUMN whatsapp_alerts_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN sms_alerts_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN email_alerts_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN whatsapp_phone_number VARCHAR(255),
ADD COLUMN sms_phone_number VARCHAR(255),
ADD COLUMN alerts_email VARCHAR(255);
```

## Frontend Integration

### Client Update API

The frontend can update alert contact information:

```json
PUT /api/v1/frontend/clients
{
    "whatsapp_phone_number": "+1234567890",
    "sms_phone_number": "+1234567890",
    "alerts_email": "<EMAIL>"
}
```

### Alert Preferences

Alert preferences (enabled/disabled) can only be set from the backend for security reasons.

## Security Considerations

1. **Alert Preferences**: Only backend can enable/disable alert types
2. **Contact Information**: Frontend can update contact details
3. **Phone Number Validation**: Implement proper phone number validation
4. **Rate Limiting**: Consider implementing rate limiting for alerts
5. **Sensitive Data**: Ensure alert messages don't contain sensitive information

## Future Enhancements

1. **WhatsApp Business API**: Full WhatsApp integration
2. **Alert Templates**: Customizable alert message templates
3. **Alert Scheduling**: Time-based alert preferences
4. **Alert History**: Track sent alerts and delivery status
5. **Webhook Integration**: Send alerts to external webhooks
6. **Alert Categories**: Different alert types with different priorities 