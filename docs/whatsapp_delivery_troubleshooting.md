# WhatsApp Message Delivery Troubleshooting

## Issue: API Success but No Message Received

### Success Message
```
Successfully sent WhatsApp template message to +***********
```

### Common Causes and Solutions

## 1. WhatsApp Business Account Status

### Check Account Status
1. Go to [business.facebook.com](https://business.facebook.com)
2. Navigate to your WhatsApp Business account
3. Check **Account Status**:
   - ✅ **Active**: Account is working
   - ⚠️ **Pending**: Waiting for approval
   - ❌ **Suspended**: Account suspended

### Check Phone Number Status
1. Go to **Phone Numbers** section
2. Verify your phone number status:
   - ✅ **Active**: Ready to send messages
   - ⚠️ **Pending**: Waiting for verification
   - ❌ **Suspended**: Phone number suspended

## 2. Template Approval Status

### Check Template Status
1. Go to **Messaging** → **Message Templates**
2. Find your `device_alert` template
3. Check status:
   - ✅ **Active - Quality**: Template approved and working
   - ⚠️ **Pending**: Waiting for approval
   - ❌ **Rejected**: Template rejected

### Template Language Verification
- **Template Language**: English
- **API Language**: `en`
- **Status**: Must be approved

## 3. Recipient Phone Number Issues

### Phone Number Format
- **Your Number**: `+***********`
- **Expected Format**: `+************` (missing last digit?)
- **Check**: Verify the complete phone number

### Phone Number Registration
- **Issue**: Phone number not registered on WhatsApp
- **Solution**: Ensure the number is active on WhatsApp

### Business-Initiated Messages
- **Issue**: Recipient hasn't messaged your business first
- **Solution**: Recipient must send a message to your business within 24 hours

## 4. Message Template Window

### 24-Hour Window
- **Rule**: Can only send business-initiated messages within 24 hours of customer's last message
- **Check**: When did the customer last message your business?
- **Solution**: Customer needs to send a message first

### Session Messages
- **Type**: Messages sent within 24-hour window
- **Requirement**: Customer must have messaged first

## 5. API Response Analysis

### Check Full API Response
Add this debug code to see the complete response:

```go
// In sendMessage function, add:
responseBody, _ := io.ReadAll(resp.Body)
fmt.Printf("DEBUG: Full API Response: %s\n", string(responseBody))
```

### Expected Success Response
```json
{
  "messaging_product": "whatsapp",
  "contacts": [
    {
      "input": "+************",
      "wa_id": "************"
    }
  ],
  "messages": [
    {
      "id": "wamid.HBgLMjYzNzc0MTc1NDM4FQIAEhg..."
    }
  ]
}
```

### Check for Errors in Response
Look for:
- `error` fields
- `error_data` with details
- `wa_id` field (confirms number is registered)

## 6. Phone Number Verification

### Test with Different Numbers
1. **Test with your own number** (if different from business number)
2. **Test with a verified WhatsApp number**
3. **Check if number is registered on WhatsApp**

### Common Phone Number Issues
- **Wrong format**: Missing country code
- **Not registered**: Number not on WhatsApp
- **Business number**: Can't send to your own business number

## 7. Template Parameter Issues

### Check Template Parameters
Your template expects:
- `{{1}}` - Alert title
- `{{2}}` - Alert message
- `{{1}}` - Button parameter

### Verify Parameter Values
```go
// Debug output should show:
DEBUG: Parameters: [Speed Alert Device exceeded speed limit 123]
```

## 8. WhatsApp Business API Limits

### Rate Limits
- **Messages per second**: Check if you're hitting limits
- **Daily limits**: Verify you haven't exceeded daily quota
- **Template limits**: Some templates have usage limits

### Account Limits
- **Free tier limits**: Check your account tier
- **Message credits**: Verify you have available credits

## 9. Testing Steps

### Step 1: Verify Phone Number
```bash
# Test with a known working number
curl -X POST \
  "https://graph.facebook.com/v18.0/YOUR_PHONE_NUMBER_ID/messages" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messaging_product": "whatsapp",
    "recipient_type": "individual",
    "to": "TEST_PHONE_NUMBER",
    "type": "template",
    "template": {
      "name": "device_alert",
      "language": {
        "code": "en"
      },
      "components": [
        {
          "type": "body",
          "parameters": [
            {"type": "text", "text": "Test Alert"},
            {"type": "text", "text": "This is a test message"}
          ]
        },
        {
          "type": "button",
          "sub_type": "url",
          "index": "0",
          "parameters": [
            {"type": "text", "text": "123"}
          ]
        }
      ]
    }
  }'
```

### Step 2: Check API Response
Look for:
- `wa_id` in contacts array
- `id` in messages array
- Any error messages

### Step 3: Verify Template
1. **Template name**: `device_alert`
2. **Language**: `en`
3. **Status**: Active
4. **Parameters**: 3 parameters (2 body + 1 button)

## 10. Common Solutions

### Solution 1: Customer Message First
- **Issue**: Business-initiated message without customer contact
- **Fix**: Customer must send a message to your business first

### Solution 2: Check Phone Number
- **Issue**: Wrong or unregistered phone number
- **Fix**: Verify phone number format and registration

### Solution 3: Template Issues
- **Issue**: Template not approved or wrong parameters
- **Fix**: Check template status and parameter count

### Solution 4: Account Issues
- **Issue**: Account suspended or pending
- **Fix**: Contact WhatsApp Business support

## 11. Debug Code

Add this to your `sendMessage` function:

```go
// After making the API call
responseBody, _ := io.ReadAll(resp.Body)
fmt.Printf("DEBUG: HTTP Status: %d\n", resp.StatusCode)
fmt.Printf("DEBUG: Full Response: %s\n", string(responseBody))

// Parse response to check for wa_id
var response WhatsAppResponse
if err := json.Unmarshal(responseBody, &response); err == nil {
    if len(response.Contacts) > 0 {
        fmt.Printf("DEBUG: Contact wa_id: %s\n", response.Contacts[0].WaID)
    }
    if len(response.Messages) > 0 {
        fmt.Printf("DEBUG: Message ID: %s\n", response.Messages[0].ID)
    }
}
```

## Next Steps

1. **Check WhatsApp Business Manager** for account and template status
2. **Verify phone number** format and registration
3. **Test with a different number** that you know works
4. **Add debug logging** to see full API response
5. **Check 24-hour window** - customer may need to message first 