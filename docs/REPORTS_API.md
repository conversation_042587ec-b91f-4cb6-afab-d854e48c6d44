# YoTracker Reports API Documentation

## Overview
The YoTracker Reports API provides comprehensive reporting functionality for fleet management, including report generation, scheduling, and export capabilities.

## Frontend Routes (Port 8081)
**Base URL:** `http://localhost:8081/api/v1/reports`

### Report Management

#### Get Reports List
```http
GET /api/v1/reports
```
Returns all available reports organized by category.

**Response:**
```json
{
  "success": true,
  "data": {
    "reports_by_category": {
      "Behavior": [...],
      "Detail": [...],
      "Executive": [...]
    },
    "total_reports": 15
  }
}
```

#### Get Report Details
```http
GET /api/v1/reports/{id}
```
Returns details of a specific report.

### Report Generation

#### Generate Report
```http
POST /api/v1/reports/{id}/generate
```
Generates report data with specified filters.

**Request Body:**
```json
{
  "start_date": "2025-08-01T00:00:00Z",
  "end_date": "2025-08-16T23:59:59Z",
  "client_device_ids": [1, 2, 3],
  "driver_ids": [1, 2],
  "per_page": 100,
  "page": 1
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "report_info": {...},
    "filters": {...},
    "data": [...],
    "summary": {...},
    "metadata": {
      "generated_at": "2025-08-16T10:30:00Z",
      "total_records": 1500,
      "filtered_records": 150,
      "execution_time": "45ms",
      "format": "json"
    }
  }
}
```

#### Export Report
```http
POST /api/v1/reports/{id}/export?format=pdf
```
Exports report to PDF, Excel, or CSV format.

**Query Parameters:**
- `format`: pdf, excel, csv

**Request Body:** Same as Generate Report

### Scheduled Reports

#### Get Scheduled Reports
```http
GET /api/v1/reports/scheduled
```
Returns all scheduled reports for the user.

#### Create Scheduled Report
```http
POST /api/v1/reports/scheduled
```

**Request Body:**
```json
{
  "report_id": 1,
  "name": "Weekly Driver Safety Report",
  "description": "Weekly safety scorecard for all drivers",
  "frequency": "weekly",
  "timezone": "UTC",
  "filters": {
    "start_date": "2025-08-01T00:00:00Z",
    "end_date": "2025-08-16T23:59:59Z"
  },
  "recipients": [
    {"email": "<EMAIL>", "name": "Fleet Manager"},
    {"email": "<EMAIL>", "name": "Safety Officer"}
  ],
  "format": "pdf"
}
```

#### Update Scheduled Report
```http
PUT /api/v1/reports/scheduled/{id}
```

#### Delete Scheduled Report
```http
DELETE /api/v1/reports/scheduled/{id}
```

## Backend Routes (Port 8080) - Admin Only
**Base URL:** `http://localhost:8080/api/v1/reports`

### Report Administration

#### Get All Reports
```http
GET /api/v1/reports
```
Returns all reports (including inactive ones).

#### Create Report
```http
POST /api/v1/reports
```

**Request Body:**
```json
{
  "name": "Custom Fleet Report",
  "description": "Custom report for fleet analysis",
  "category": "Custom",
  "report_type": "custom_fleet_analysis",
  "default_filters": {}
}
```

#### Update Report
```http
PUT /api/v1/reports/{id}
```

#### Delete Report
```http
DELETE /api/v1/reports/{id}
```

### Scheduled Reports Administration

#### Get All Scheduled Reports
```http
GET /api/v1/reports/scheduled
```
Returns all scheduled reports across all users.

#### Get Scheduled Report Details
```http
GET /api/v1/reports/scheduled/{id}
```

#### Run Scheduled Report Manually
```http
POST /api/v1/reports/scheduled/{id}/run
```
Manually executes a scheduled report.

### Analytics & Statistics

#### Get Report Categories
```http
GET /api/v1/reports/categories
```
Returns all available report categories.

#### Get Report Statistics
```http
GET /api/v1/reports/stats
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_reports": 15,
    "active_reports": 15,
    "total_scheduled_reports": 5,
    "active_schedules": 4,
    "reports_run_today": 12,
    "reports_run_this_week": 45
  }
}
```

#### Get Execution History
```http
GET /api/v1/reports/execution-history
```
Returns recent report execution history.

## Available Reports

### Behavior Reports
- **Driver Safety Scorecard** - Comprehensive safety scoring
- **Speeding Violations Report** - Detailed violation tracking
- **Harsh Driving Events** - Coaching recommendations
- **Driver Performance Ranking** - Performance comparisons

### Detail Reports
- **Position Log Report** - Raw GPS data with context
- **Trip Detail Report** - Complete journey analysis
- **Geofence Activity Report** - Zone monitoring
- **Customer Visit Report** - Service efficiency

### Executive Reports
- **Executive Fleet Summary** - C-level KPIs
- **Fleet ROI Dashboard** - Investment analysis

### Cost & Financial Reports
- **Fuel Consumption Analysis** - Cost breakdown
- **Vehicle Utilization Report** - Efficiency metrics

### Operations Reports
- **Daily Operations Dashboard** - Real-time overview
- **Emergency Response Report** - Safety incidents

## Filter Parameters

### Common Filters
- `start_date` (ISO 8601): Start date for data range
- `end_date` (ISO 8601): End date for data range
- `client_device_ids` (array): Filter by specific devices
- `driver_ids` (array): Filter by specific drivers
- `fleet_ids` (array): Filter by fleets
- `per_page` (integer): Records per page (default: 100)
- `page` (integer): Page number (default: 1)

### Behavior-Specific Filters
- `event_types` (array): Filter by event types
- `min_severity` (float): Minimum severity level
- `max_severity` (float): Maximum severity level

### Trip-Specific Filters
- `min_distance` (float): Minimum trip distance (km)
- `max_distance` (float): Maximum trip distance (km)
- `min_duration` (integer): Minimum trip duration (seconds)
- `max_duration` (integer): Maximum trip duration (seconds)

### Speed-Specific Filters
- `min_speed` (float): Minimum speed (km/h)
- `max_speed` (float): Maximum speed (km/h)
- `speed_limit` (float): Speed limit for violations

## Export Formats

### PDF Export
- Professional formatting
- Company branding
- Charts and graphs
- Executive summaries

### Excel Export
- Multiple worksheets
- Pivot tables
- Conditional formatting
- Raw data access

### CSV Export
- Simple data format
- Easy import to other systems
- Lightweight file size
- Programmatic access

## Error Responses

### Common Error Codes
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (report/schedule not found)
- `500` - Internal Server Error

### Error Response Format
```json
{
  "success": false,
  "error": "Error description",
  "code": "ERROR_CODE"
}
```

## Rate Limiting
- Report generation: 10 requests per minute
- Export operations: 5 requests per minute
- Scheduled reports: No limit (internal processing)

## Authentication
All routes require valid authentication tokens. Include in header:
```
Authorization: Bearer <your-token>
```
