# Update Profile API Documentation

## Overview
The UpdateProfile functionality allows authenticated users to update their profile information including name, email, username, and other personal details. The API includes validation to ensure email and username uniqueness.

## Endpoints

### Frontend (Client Users)
- **URL**: `PUT /api/v1/frontend/users/profile`
- **Authentication**: Required (Bearer token)
- **Middleware**: AuthMiddleware, CheckForClient

### Backend (Admin Users)
- **URL**: `PUT /api/v1/backend/users/profile`
- **Authentication**: Required (Bearer token)
- **Middleware**: AuthMiddleware, CheckIfUserIsBackend

## Request Format

### Headers
```
Content-Type: application/json
Authorization: Bearer <your_jwt_token>
```

### Request Body
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "username": "johndo<PERSON>",
  "gender": "male",
  "telegram_user_id": "123456789",
  "slack_webhook_url": "https://hooks.slack.com/services/...",
  "description": "Software Developer"
}
```

### Field Validation
- `name`: Required, string
- `email`: Required, valid email format
- `username`: Optional, string (must be unique if provided)
- `gender`: Optional, string
- `telegram_user_id`: Optional, string
- `slack_webhook_url`: Optional, string (URL)
- `description`: Optional, string

## Response Format

### Success Response (200 OK)
```json
{
  "message": "Profile updated successfully"
}
```

### Error Responses

#### Validation Error (400 Bad Request)
```json
{
  "message": "Key: 'UpdateProfileRequest.Email' Error:Tag 'email'"
}
```

#### Email Already Exists (400 Bad Request)
```json
{
  "message": "Email already exists"
}
```

#### Username Already Exists (400 Bad Request)
```json
{
  "message": "Username already exists"
}
```

#### Unauthorized (401 Unauthorized)
```json
{
  "message": "Unauthorized"
}
```

#### Server Error (500 Internal Server Error)
```json
{
  "message": "Failed to update profile"
}
```

## Security Features

1. **Authentication Required**: Users must be logged in with a valid JWT token
2. **Self-Update Only**: Users can only update their own profile (enforced by middleware)
3. **Email Uniqueness**: Prevents duplicate emails across all users
4. **Username Uniqueness**: Prevents duplicate usernames across all users
5. **Same Email Allowed**: Users can keep their current email when updating other fields
6. **Input Validation**: All fields are validated according to their constraints

## Validation Rules

### Email Validation
- Must be a valid email format
- Must be unique across all users (except current user)
- Required field

### Username Validation
- Must be unique across all users (except current user)
- Optional field
- Can be null/empty

### Other Fields
- `name`: Required, cannot be empty
- `gender`, `telegram_user_id`, `slack_webhook_url`, `description`: Optional fields

## Example Usage

### Using curl
```bash
curl -X PUT \
  http://localhost:9001/api/v1/frontend/users/profile \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your_jwt_token_here' \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "username": "johndoe",
    "gender": "male",
    "telegram_user_id": "123456789",
    "slack_webhook_url": "https://hooks.slack.com/services/...",
    "description": "Software Developer"
  }'
```

### Using JavaScript (fetch)
```javascript
const response = await fetch('/api/v1/frontend/users/profile', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    username: 'johndoe',
    gender: 'male',
    telegram_user_id: '123456789',
    slack_webhook_url: 'https://hooks.slack.com/services/...',
    description: 'Software Developer'
  })
});

const result = await response.json();
console.log(result.message);
```

## Implementation Details

### Models
- Uses `UpdateProfileRequest` struct for request validation
- Defined in `internal/models/user.go`

### Controllers
- Frontend: `cmd/web/frontend/controllers/users.go`
- Backend: `cmd/web/backend/controllers/users.go`

### Database
- Updates multiple fields in the `users` table
- Uses GORM for database operations
- Performs uniqueness checks before updating

### Uniqueness Validation
- Email: Checks if email exists for any other user
- Username: Checks if username exists for any other user
- Allows users to keep their current email/username

## Testing
Comprehensive tests are available:
- Frontend: `cmd/web/frontend/controllers/users_update_profile_test.go`
- Backend: `cmd/web/backend/controllers/users_update_profile_test.go`

Test scenarios include:
- Successful profile update
- Email already exists validation
- Username already exists validation
- Same email update allowed
- Invalid email format
- Missing required fields
- Unauthorized access

## Differences from UpdateUser
- **UpdateProfile**: Users update their own profile only
- **UpdateUser**: Admins can update any user's profile (including admin fields)
- **UpdateProfile**: No password field (use ChangePassword for that)
- **UpdateProfile**: No admin fields like UserType, Status, ClientId
