# Drivers API Documentation

## Overview

The Drivers API allows you to manage driver information for vehicles in the YoTracker system. When a driver is created with an email address, a corresponding user account is automatically created with the user type "driver".

## Driver Model

### Fields

- `id` (uint): Primary key
- `created_by_id` (uint): ID of the user who created the driver
- `client_id` (uint): ID of the client the driver belongs to
- `user_id` (uint, optional): ID of the associated user account (if email provided)
- `fleet_id` (uint, optional): ID of the fleet the driver belongs to
- `country_id` (uint, optional): ID of the driver's country
- `name` (string): Driver's full name
- `dob` (date, optional): Date of birth
- `gender` (string, optional): Gender
- `blood_type` (string, optional): Blood type
- `driver_license_no` (string, optional): Driver's license number
- `license_expiry_date` (date, optional): License expiry date
- `phone_number` (string, optional): Phone number
- `email` (string, optional): Email address (creates user account if provided)
- `address` (text, optional): Address
- `rfid` (string, optional): RFID tag
- `description` (text, optional): Description
- `status` (string): Status (active/inactive)
- `created_at` (timestamp): Creation timestamp
- `updated_at` (timestamp): Last update timestamp

## API Endpoints

### Frontend API (Client-specific)

Base URL: `/api/v1/frontend/drivers`

#### GET /drivers
Get all drivers for the authenticated client.

**Query Parameters:**
- `search` (optional): Search by name, license number, phone, or email
- `status` (optional): Filter by status
- `fleet_id` (optional): Filter by fleet

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "driver_license_no": "DL123456",
      "phone_number": "**********",
      "email": "<EMAIL>",
      "status": "active",
      "fleet": {
        "id": 1,
        "name": "Fleet A"
      },
      "country": {
        "id": 1,
        "name": "United States"
      }
    }
  ]
}
```

#### GET /drivers/search
Search drivers with pagination.

**Query Parameters:**
- `search` (optional): Search term
- `status` (optional): Filter by status
- `fleet_id` (optional): Filter by fleet
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

#### GET /drivers/:id
Get a specific driver by ID.

#### POST /drivers
Create a new driver.

**Request Body:**
```json
{
  "client_id": 1,
  "fleet_id": 1,
  "country_id": 1,
  "name": "John Doe",
  "dob": "1990-01-01",
  "gender": "male",
  "blood_type": "O+",
  "driver_license_no": "DL123456",
  "license_expiry_date": "2025-12-31",
  "phone_number": "**********",
  "email": "<EMAIL>",
  "address": "123 Main St, City",
  "rfid": "RFID123",
  "description": "Experienced driver",
  "status": "active"
}
```

**Note:** If an email is provided, a user account will be automatically created with:
- User type: "driver"
- A randomly generated password
- The same name, gender, and status as the driver

#### PUT /drivers/:id
Update an existing driver.

#### DELETE /drivers/:id
Delete a driver and associated user account.

### Backend API (Admin)

Base URL: `/api/v1/backend/drivers`

Same endpoints as frontend, but with additional query parameters:
- `client_id` (optional): Filter by client

## GPS Data Integration

GPS data now includes driver information when available:

```json
{
  "id": 1,
  "client_device_id": 1,
  "driver_id": 1,
  "latitude": 40.7128,
  "longitude": -74.0060,
  "speed": 25.5,
  "timestamp": "2024-01-01T12:00:00Z",
  "client_device": {
    "id": 1,
    "name": "Vehicle 1",
    "driver": {
      "id": 1,
      "name": "John Doe",
      "driver_license_no": "DL123456"
    }
  }
}
```

## Database Migration

Run the following migration to create the drivers table:

```sql
-- Create drivers table
CREATE TABLE IF NOT EXISTS `drivers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_by_id` bigint unsigned NOT NULL,
  `client_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `fleet_id` bigint unsigned DEFAULT NULL,
  `country_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `dob` date DEFAULT NULL,
  `gender` varchar(50) DEFAULT NULL,
  `blood_type` varchar(10) DEFAULT NULL,
  `driver_license_no` varchar(100) DEFAULT NULL,
  `license_expiry_date` date DEFAULT NULL,
  `phone_number` varchar(50) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text,
  `rfid` varchar(100) DEFAULT NULL,
  `description` text,
  `status` varchar(50) NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_drivers_client_id` (`client_id`),
  KEY `idx_drivers_user_id` (`user_id`),
  KEY `idx_drivers_fleet_id` (`fleet_id`),
  KEY `idx_drivers_country_id` (`country_id`),
  CONSTRAINT `fk_drivers_client_id` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_drivers_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_drivers_fleet_id` FOREIGN KEY (`fleet_id`) REFERENCES `fleets` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_drivers_country_id` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add driver_id column to users table
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `driver_id` bigint unsigned DEFAULT NULL;
ALTER TABLE `users` ADD INDEX IF NOT EXISTS `idx_users_driver_id` (`driver_id`);
ALTER TABLE `users` ADD CONSTRAINT IF NOT EXISTS `fk_users_driver_id` FOREIGN KEY (`driver_id`) REFERENCES `drivers` (`id`) ON DELETE SET NULL;
```

## Usage Examples

### Creating a Driver with User Account

```bash
curl -X POST /api/v1/frontend/drivers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "client_id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone_number": "**********",
    "driver_license_no": "DL123456",
    "status": "active"
  }'
```

### Assigning a Driver to a Vehicle

```bash
curl -X PUT /api/v1/frontend/client_devices/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "driver_id": 1
  }'
```

### Viewing GPS Data with Driver Information

```bash
curl -X GET "/api/v1/frontend/gps_data/search?client_device_id=1&start_date=2024-01-01&end_date=2024-01-02" \
  -H "Authorization: Bearer <token>"
``` 