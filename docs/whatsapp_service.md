# WhatsApp Service Implementation

## Overview

The WhatsApp service provides comprehensive messaging capabilities for YoTracker alerts using the WhatsApp Business API. It supports both simple text messages and interactive messages with buttons for enhanced user engagement.

## Features

✅ **Text Messages**: Simple text-only notifications  
✅ **Interactive Messages**: Messages with buttons and actions  
✅ **Alert-Specific Templates**: Pre-built templates for different alert types  
✅ **Phone Number Validation**: Automatic formatting and validation  
✅ **Environment-Based Configuration**: Easy setup via environment variables  
✅ **Test Mode Support**: Safe testing without sending actual messages  

## Environment Variables

### Required Variables

```bash
# WhatsApp Business API Access Token
WHATSAPP_ACCESS_TOKEN=your_access_token_here

# WhatsApp Phone Number ID
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
```

### Optional Variables

```bash
# WhatsApp API Base URL (defaults to v18.0)
WHATSAPP_BASE_URL=https://graph.facebook.com/v18.0

# Default Country Code for phone numbers (defaults to "1" for US)
WHATSAPP_DEFAULT_COUNTRY_CODE=263
```

## Message Types

### 1. Text Messages

Simple text-only messages for basic notifications.

```go
whatsappService, err := service.NewWhatsAppService()
if err != nil {
    return err
}

err = whatsappService.SendTextMessage("+1234567890", "Hello! This is a test message.")
```

### 2. Interactive Messages

Messages with buttons and interactive elements.

```go
buttons := []service.Button{
    {
        Type: "reply",
        Reply: service.Reply{
            ID:    "view_device",
            Title: "View Device",
        },
    },
    {
        Type: "reply",
        Reply: service.Reply{
            ID:    "dismiss",
            Title: "Dismiss",
        },
    },
}

err = whatsappService.SendInteractiveMessage(
    "+1234567890",
    "🚨 Alert Notification",
    "A device has triggered an alert. Would you like to view the details?",
    "Click a button below to take action",
    buttons,
)
```

## Alert-Specific Message Templates

### Geofence Alerts

```go
// Automatically creates geofence-specific interactive message
alert := &models.Alert{
    AlertType: "geofence",
    AlertName: func() *string { s := "Geofence Entry"; return &s }(),
    Message:   func() *string { s := "Device TRK001 has entered geofence 'Warehouse Zone'"; return &s }(),
}

err = whatsappService.SendAlert(client, alert, clientDevice)
```

**Features:**
- 📍 Location-based header
- "View Location" button
- Customizable message content

### Speed Alerts

```go
alert := &models.Alert{
    AlertType: "speed",
    AlertName: func() *string { s := "Speed Violation"; return &s }(),
    Message:   func() *string { s := "Device TRK001 is traveling at 85 km/h (limit: 60 km/h)"; return &s }(),
}

err = whatsappService.SendAlert(client, alert, clientDevice)
```

**Features:**
- ⚡ Speed alert header
- "View Details" button
- Speed and limit information

### Maintenance Alerts

```go
alert := &models.Alert{
    AlertType: "maintenance",
    AlertName: func() *string { s := "Maintenance Due"; return &s }(),
    Message:   func() *string { s := "Device TRK001 requires oil change maintenance. Due date: 2024-01-15"; return &s }(),
}

err = whatsappService.SendAlert(client, alert, clientDevice)
```

**Features:**
- 🔧 Maintenance header
- "Schedule Maintenance" button
- Due date information

### Generic Device Alerts

```go
alert := &models.Alert{
    AlertType: "device_offline",
    AlertName: func() *string { s := "Device Offline"; return &s }(),
    Message:   func() *string { s := "Device TRK001 has gone offline"; return &s }(),
}

err = whatsappService.SendAlert(client, alert, clientDevice)
```

**Features:**
- 🚨 Device alert header
- "View Device" button
- Generic device information

## Phone Number Handling

### Automatic Formatting

The service automatically formats phone numbers for WhatsApp API:

```go
// Input formats supported:
"+1234567890"     // Already formatted
"1234567890"      // Adds default country code
"+44 20 7946 0958" // Removes spaces
"******-123-4567"  // Removes dashes
```

### Validation

```go
isValid := whatsappService.ValidatePhoneNumber("+1234567890")
formatted := whatsappService.FormatPhoneNumber("1234567890")
```

## Integration with Alert System

The WhatsApp service integrates seamlessly with the existing alert system:

```go
// In alert_data_service.go
func (a *Alert) SaveAlert() {
    // ... existing code ...
    
    // Send WhatsApp alerts if enabled
    if client.WhatsappAlertsEnabled != nil && *client.WhatsappAlertsEnabled {
        whatsappService, err := service.NewWhatsAppService()
        if err == nil {
            go func() {
                if err := whatsappService.SendAlert(client, alert, clientDevice); err != nil {
                    log.Printf("Failed to send WhatsApp alert: %v", err)
                }
            }()
        }
    }
}
```

## Button Types

### Reply Buttons

```go
{
    Type: "reply",
    Reply: Reply{
        ID:    "view_device",
        Title: "View Device",
    },
}
```

**Use Cases:**
- View device details
- View location on map
- Schedule maintenance
- Dismiss alert

### URL Buttons (Future Enhancement)

```go
{
    Type: "url",
    URL: URL{
        URL: "https://yotracker.com/device/123",
        Title: "Open Dashboard",
    },
}
```

### Phone Buttons (Future Enhancement)

```go
{
    Type: "phone",
    Phone: Phone{
        PhoneNumber: "+1234567890",
        Title: "Call Support",
    },
}
```

## Error Handling

The service includes comprehensive error handling:

```go
err := whatsappService.SendTextMessage(phone, message)
if err != nil {
    switch {
    case strings.Contains(err.Error(), "WHATSAPP_ACCESS_TOKEN"):
        // Configuration error
        log.Printf("WhatsApp not configured: %v", err)
    case strings.Contains(err.Error(), "API error"):
        // API error
        log.Printf("WhatsApp API error: %v", err)
    default:
        // Other errors
        log.Printf("WhatsApp error: %v", err)
    }
}
```

## Test Mode

The service automatically detects test environments and logs messages instead of sending:

```bash
# Enable test mode
export TEST_ENV=true
```

In test mode, messages are logged but not sent:
```
[WHATSAPP TEST] Would send message to +1234567890: {MessagingProduct:whatsapp ...}
```

## Security Features

✅ **Environment Variables**: Sensitive data stored in environment variables  
✅ **Phone Number Validation**: Prevents invalid phone numbers  
✅ **Test Mode**: Safe testing without sending actual messages  
✅ **Error Handling**: Comprehensive error handling and logging  
✅ **Rate Limiting**: Built-in HTTP client timeout (30 seconds)  

## API Response Handling

The service handles WhatsApp API responses:

```go
type WhatsAppResponse struct {
    MessagingProduct string `json:"messaging_product"`
    Contacts         []struct {
        Input string `json:"input"`
        WaID  string `json:"wa_id"`
    } `json:"contacts"`
    Messages []struct {
        ID string `json:"id"`
    } `json:"messages"`
}
```

## Configuration Examples

### Development Environment

```bash
# .env file
WHATSAPP_ACCESS_TOKEN=your_dev_access_token
WHATSAPP_PHONE_NUMBER_ID=your_dev_phone_number_id
WHATSAPP_BASE_URL=https://graph.facebook.com/v18.0
WHATSAPP_DEFAULT_COUNTRY_CODE=1
TEST_ENV=true  # Enable test mode for development
```

### Production Environment

```bash
# Production environment variables
WHATSAPP_ACCESS_TOKEN=your_prod_access_token
WHATSAPP_PHONE_NUMBER_ID=your_prod_phone_number_id
WHATSAPP_BASE_URL=https://graph.facebook.com/v18.0
WHATSAPP_DEFAULT_COUNTRY_CODE=1
# TEST_ENV not set (messages will be sent)
```

## Troubleshooting

### Common Issues

1. **"WHATSAPP_ACCESS_TOKEN environment variable is required"**
   - Set the `WHATSAPP_ACCESS_TOKEN` environment variable
   - Ensure the token is valid and has proper permissions

2. **"WHATSAPP_PHONE_NUMBER_ID environment variable is required"**
   - Set the `WHATSAPP_PHONE_NUMBER_ID` environment variable
   - Verify the phone number ID in your WhatsApp Business account

3. **"WhatsApp API error: 401"**
   - Check if the access token is valid
   - Verify the phone number ID is correct
   - Ensure the token has the required permissions

4. **"WhatsApp API error: 400"**
   - Check the message format
   - Verify phone number format
   - Ensure the message content meets WhatsApp guidelines

### Debug Mode

Enable debug logging by setting the log level:

```go
// In your application
log.SetLevel(log.DebugLevel)
```

## Future Enhancements

### Planned Features

- [ ] **URL Buttons**: Direct links to dashboard pages
- [ ] **Phone Buttons**: Direct calling functionality
- [ ] **Media Messages**: Support for images and documents
- [ ] **Template Messages**: Pre-approved message templates
- [ ] **Webhook Integration**: Handle button responses
- [ ] **Message Templates**: Reusable message templates
- [ ] **Bulk Messaging**: Send to multiple recipients
- [ ] **Message Status Tracking**: Track delivery and read status

### Custom Message Templates

```go
// Future implementation
err = whatsappService.SendTemplateMessage(
    phoneNumber,
    "alert_notification",
    map[string]string{
        "device_name": "Truck 001",
        "alert_type": "Geofence Entry",
        "location": "Warehouse Zone",
    },
)
```

## Best Practices

1. **Always validate phone numbers** before sending messages
2. **Use test mode** during development and testing
3. **Handle errors gracefully** and log them appropriately
4. **Keep messages concise** and actionable
5. **Use appropriate button labels** that clearly indicate the action
6. **Test with real devices** before going to production
7. **Monitor API usage** and rate limits
8. **Keep access tokens secure** and rotate them regularly

## Support

For issues with the WhatsApp service:

1. Check the environment variables are set correctly
2. Verify the WhatsApp Business API credentials
3. Test with a simple text message first
4. Check the application logs for detailed error messages
5. Consult the WhatsApp Business API documentation 